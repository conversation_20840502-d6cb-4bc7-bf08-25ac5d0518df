<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    :title="title"
    class="role-form-dialog"
    style="width: 700px"
    @update:show="$emit('update:visible', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="24">
        <!-- 基础信息 -->
        <n-form-item-gi label="角色名称" path="name">
          <n-input
            v-model:value="formData.name"
            placeholder="请输入角色名称"
            @blur="checkRoleName"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="角色编码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入角色编码"
            @blur="checkRoleCode"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="数据范围" path="data_scope">
          <n-select
            v-model:value="formData.data_scope"
            :options="dataScopeOptions"
            placeholder="请选择数据范围"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="排序权重" path="sort_order">
          <n-input-number
            v-model:value="formData.sort_order"
            placeholder="请输入排序权重"
            :min="0"
            :max="9999"
            class="w-full"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="角色状态" path="is_active">
          <n-switch
            v-model:value="formData.is_active"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item-gi>
      </n-grid>
      
      <n-form-item label="角色描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入角色描述"
          :rows="3"
        />
      </n-form-item>
      
      <!-- 权限分配 -->
      <n-form-item label="权限分配" path="permission_ids">
        <div class="permission-tree-container">
          <div class="permission-tree-header">
            <n-space>
              <n-button size="small" @click="handleExpandAll">
                全部展开
              </n-button>
              <n-button size="small" @click="handleCollapseAll">
                全部折叠
              </n-button>
              <n-button size="small" @click="handleCheckAll">
                全选
              </n-button>
              <n-button size="small" @click="handleUncheckAll">
                全不选
              </n-button>
            </n-space>
            
            <n-input
              v-model:value="permissionSearchKeyword"
              placeholder="搜索权限..."
              size="small"
              clearable
              style="width: 200px"
            >
              <template #prefix>
                <n-icon><SearchIcon /></n-icon>
              </template>
            </n-input>
          </div>
          
          <div class="permission-tree-content">
            <n-tree
              :data="filteredPermissionTree"
              :checked-keys="formData.permission_ids"
              :expanded-keys="expandedKeys"
              :loading="permissionLoading"
              key-field="id"
              label-field="name"
              children-field="children"
              checkable
              cascade
              block-line
              @update:checked-keys="handlePermissionCheck"
              @update:expanded-keys="handleExpandedKeysChange"
              class="permission-tree"
            >
              <template #default="{ option }">
                <div class="permission-node">
                  <n-icon
                    v-if="option.icon"
                    :component="getPermissionIcon(option.icon)"
                    class="permission-icon"
                  />
                  <span class="permission-name">{{ option.name }}</span>
                  <n-tag
                    :type="getPermissionTypeColor(option.permission_type)"
                    size="small"
                    class="permission-type"
                  >
                    {{ getPermissionTypeLabel(option.permission_type) }}
                  </n-tag>
                  <span v-if="option.code" class="permission-code">{{ option.code }}</span>
                </div>
              </template>
            </n-tree>
          </div>
        </div>
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ mode === 'create' ? '创建' : '更新' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { SearchIcon } from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { roleApi } from '@/api/role'
import type { Role, RoleCreateForm, RoleEditForm, PermissionTree, PermissionType, DataScope } from '@/types/role'

// Props
interface Props {
  visible: boolean
  role?: Role | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  role: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)
const permissionLoading = ref(false)
const permissionSearchKeyword = ref('')
const expandedKeys = ref<number[]>([])

// 表单数据
const formData = ref<RoleCreateForm>({
  name: '',
  code: '',
  data_scope: 'DEPT_ONLY' as DataScope,
  description: '',
  is_active: true,
  sort_order: 0,
  permission_ids: []
})

// 计算属性
const title = computed(() => {
  return props.mode === 'create' ? '新增角色' : '编辑角色'
})

const dataScopeOptions = computed(() => roleStore.dataScopeOptions)

const filteredPermissionTree = computed(() => {
  if (!permissionSearchKeyword.value) {
    return roleStore.permissionTree
  }
  
  const filterTree = (nodes: PermissionTree[]): PermissionTree[] => {
    return nodes.filter(node => {
      const matchesKeyword = node.name.toLowerCase().includes(permissionSearchKeyword.value.toLowerCase()) ||
                           node.code.toLowerCase().includes(permissionSearchKeyword.value.toLowerCase())
      
      if (matchesKeyword) {
        return true
      }
      
      if (node.children && node.children.length > 0) {
        const filteredChildren = filterTree(node.children)
        if (filteredChildren.length > 0) {
          return true
        }
      }
      
      return false
    }).map(node => ({
      ...node,
      children: node.children ? filterTree(node.children) : []
    }))
  }
  
  return filterTree(roleStore.permissionTree)
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度为2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 20, message: '角色编码长度为2-20个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '角色编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  data_scope: [
    { required: true, message: '请选择数据范围', trigger: 'change' }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序权重范围为0-9999', trigger: 'blur' }
  ]
}

// 方法
const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    data_scope: 'DEPT_ONLY' as DataScope,
    description: '',
    is_active: true,
    sort_order: 0,
    permission_ids: []
  }
  expandedKeys.value = []
}

const loadRoleData = async () => {
  if (props.role && props.mode === 'edit') {
    // 获取角色详情（包含权限信息）
    const response = await roleStore.fetchRoleDetail(props.role.id)
    if (response.code === 200) {
      const roleDetail = response.data
      formData.value = {
        name: roleDetail.name,
        code: roleDetail.code,
        data_scope: roleDetail.data_scope,
        description: roleDetail.description,
        is_active: roleDetail.is_active,
        sort_order: roleDetail.sort_order,
        permission_ids: roleDetail.permission_ids
      }
    }
  }
}

const checkRoleName = async () => {
  if (!formData.value.name) return
  
  try {
    const response = await roleApi.checkRoleNameAvailable(
      formData.value.name,
      props.mode === 'edit' ? props.role?.id : undefined
    )
    if (!response.data.available) {
      message.error('角色名称已存在')
    }
  } catch (error) {
    console.error('检查角色名称失败:', error)
  }
}

const checkRoleCode = async () => {
  if (!formData.value.code) return
  
  try {
    const response = await roleApi.checkRoleCodeAvailable(
      formData.value.code,
      props.mode === 'edit' ? props.role?.id : undefined
    )
    if (!response.data.available) {
      message.error('角色编码已存在')
    }
  } catch (error) {
    console.error('检查角色编码失败:', error)
  }
}

const getPermissionIcon = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  return SearchIcon // 临时使用搜索图标
}

const getPermissionTypeColor = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return 'info'
    case 'BUTTON':
      return 'warning'
    case 'API':
      return 'success'
    default:
      return 'default'
  }
}

const getPermissionTypeLabel = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return '菜单'
    case 'BUTTON':
      return '按钮'
    case 'API':
      return 'API'
    default:
      return type
  }
}

const handlePermissionCheck = (keys: number[]) => {
  formData.value.permission_ids = keys
}

const handleExpandedKeysChange = (keys: number[]) => {
  expandedKeys.value = keys
}

const handleExpandAll = () => {
  const getAllKeys = (nodes: PermissionTree[]): number[] => {
    let keys: number[] = []
    nodes.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  
  expandedKeys.value = getAllKeys(roleStore.permissionTree)
}

const handleCollapseAll = () => {
  expandedKeys.value = []
}

const handleCheckAll = () => {
  const getAllKeys = (nodes: PermissionTree[]): number[] => {
    let keys: number[] = []
    nodes.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  
  formData.value.permission_ids = getAllKeys(roleStore.permissionTree)
}

const handleUncheckAll = () => {
  formData.value.permission_ids = []
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    if (props.mode === 'create') {
      await roleStore.createRole(formData.value)
    } else if (props.role) {
      const editData: RoleEditForm = {
        name: formData.value.name,
        code: formData.value.code,
        data_scope: formData.value.data_scope,
        description: formData.value.description,
        is_active: formData.value.is_active,
        sort_order: formData.value.sort_order,
        permission_ids: formData.value.permission_ids
      }
      await roleStore.updateRole(props.role.id, editData)
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (props.mode === 'create') {
        resetForm()
      } else {
        loadRoleData()
      }
    })
  }
})

// 生命周期
onMounted(async () => {
  // 获取权限树
  await roleStore.fetchPermissionTree()
  
  // 获取数据范围选项
  await roleStore.fetchDataScopeOptions()
})
</script>

<style scoped>
.role-form-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.w-full {
  width: 100%;
}

.permission-tree-container {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.permission-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.permission-tree-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

.permission-tree {
  width: 100%;
}

.permission-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.permission-icon {
  flex-shrink: 0;
  color: #6b7280;
}

.permission-name {
  flex: 1;
  font-weight: 500;
  color: #1f2937;
}

.permission-type {
  flex-shrink: 0;
}

.permission-code {
  flex-shrink: 0;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}
</style>
