<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="false"
    preset="dialog"
    :title="title"
    class="user-form-dialog"
    style="width: 800px"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="24">
        <!-- 基础信息 -->
        <n-form-item-gi label="用户名" path="username">
          <n-input
            v-model:value="formData.username"
            placeholder="请输入用户名"
            :disabled="mode === 'edit'"
            @blur="checkUsername"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="昵称" path="nickname">
          <n-input
            v-model:value="formData.nickname"
            placeholder="请输入昵称"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="邮箱" path="email">
          <n-input
            v-model:value="formData.email"
            placeholder="请输入邮箱"
            @blur="checkEmail"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="手机号" path="phone">
          <n-input
            v-model:value="formData.phone"
            placeholder="请输入手机号"
            @blur="checkPhone"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="企业微信ID" path="wechat_work_id">
          <n-input
            v-model:value="formData.wechat_work_id"
            placeholder="请输入企业微信ID"
          />
        </n-form-item-gi>
        
        <!-- 密码设置（仅新增时显示） -->
        <template v-if="mode === 'create'">
          <n-form-item-gi label="密码" path="password">
            <n-input
              v-model:value="formData.password"
              type="password"
              placeholder="请输入密码"
              show-password-on="mousedown"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="确认密码" path="confirm_password">
            <n-input
              v-model:value="formData.confirm_password"
              type="password"
              placeholder="请再次输入密码"
              show-password-on="mousedown"
            />
          </n-form-item-gi>
        </template>
        
        <!-- 状态设置 -->
        <n-form-item-gi label="用户状态" path="is_active">
          <n-switch
            v-model:value="formData.is_active"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item-gi>
        
        <n-form-item-gi label="管理员权限" path="is_staff">
          <n-switch
            v-model:value="formData.is_staff"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>是</template>
            <template #unchecked>否</template>
          </n-switch>
        </n-form-item-gi>
      </n-grid>
      
      <!-- 部门分配 -->
      <n-form-item label="部门分配" path="department_ids">
        <n-select
          v-model:value="formData.department_ids"
          :options="departmentOptions"
          multiple
          placeholder="请选择部门"
          clearable
          filterable
          @update:value="handleDepartmentChange"
        />
      </n-form-item>
      
      <!-- 主部门 -->
      <n-form-item 
        v-if="formData.department_ids.length > 1" 
        label="主部门" 
        path="primary_department_id"
      >
        <n-select
          v-model:value="formData.primary_department_id"
          :options="primaryDepartmentOptions"
          placeholder="请选择主部门"
          clearable
        />
      </n-form-item>
      
      <!-- 角色分配 -->
      <n-form-item label="角色分配" path="role_ids">
        <n-select
          v-model:value="formData.role_ids"
          :options="roleOptions"
          multiple
          placeholder="请选择角色"
          clearable
          filterable
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ mode === 'create' ? '创建' : '更新' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules, type SelectOption } from 'naive-ui'
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import { userApi } from '@/api/user'
import type { UserListItem, UserCreateForm, UserEditForm } from '@/types/user'

// Props
interface Props {
  visible: boolean
  user?: UserListItem | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  user: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const userStore = useUserStore()
const authStore = useAuthStore()
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)

// 表单数据
const formData = ref<UserCreateForm>({
  username: '',
  nickname: '',
  email: '',
  phone: '',
  wechat_work_id: '',
  password: '',
  confirm_password: '',
  is_active: true,
  is_staff: false,
  department_ids: [],
  role_ids: [],
  primary_department_id: undefined
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const title = computed(() => {
  return props.mode === 'create' ? '新增用户' : '编辑用户'
})

const departmentOptions = computed((): SelectOption[] => {
  return authStore.departments.map(dept => ({
    label: dept.name,
    value: dept.id
  }))
})

const primaryDepartmentOptions = computed((): SelectOption[] => {
  return formData.value.department_ids.map(deptId => {
    const dept = authStore.departments.find(d => d.id === deptId)
    return {
      label: dept?.name || '',
      value: deptId
    }
  })
})

const roleOptions = computed((): SelectOption[] => {
  // TODO: 从角色管理获取角色列表
  return []
})

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { max: 50, message: '昵称不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        return value === formData.value.password
      },
      message: '两次输入的密码不一致',
      trigger: 'blur'
    }
  ],
  department_ids: [
    { type: 'array', min: 1, message: '请至少选择一个部门', trigger: 'change' }
  ]
}

// 方法
const resetForm = () => {
  formData.value = {
    username: '',
    nickname: '',
    email: '',
    phone: '',
    wechat_work_id: '',
    password: '',
    confirm_password: '',
    is_active: true,
    is_staff: false,
    department_ids: [],
    role_ids: [],
    primary_department_id: undefined
  }
}

const loadUserData = () => {
  if (props.user && props.mode === 'edit') {
    formData.value = {
      username: props.user.username,
      nickname: props.user.nickname,
      email: props.user.email || '',
      phone: props.user.phone || '',
      wechat_work_id: props.user.wechat_work_id || '',
      password: '',
      confirm_password: '',
      is_active: props.user.is_active,
      is_staff: props.user.is_staff,
      department_ids: props.user.departments?.map(d => d.id) || [],
      role_ids: props.user.roles?.map(r => r.id) || [],
      primary_department_id: props.user.primary_department?.id
    }
  }
}

const checkUsername = async () => {
  if (props.mode === 'edit' || !formData.value.username) return
  
  try {
    const response = await userApi.checkUsernameAvailable(formData.value.username)
    if (!response.data.available) {
      message.error('用户名已存在')
    }
  } catch (error) {
    console.error('检查用户名失败:', error)
  }
}

const checkEmail = async () => {
  if (!formData.value.email) return
  
  try {
    const response = await userApi.checkEmailAvailable(
      formData.value.email,
      props.mode === 'edit' ? props.user?.id : undefined
    )
    if (!response.data.available) {
      message.error('邮箱已被使用')
    }
  } catch (error) {
    console.error('检查邮箱失败:', error)
  }
}

const checkPhone = async () => {
  if (!formData.value.phone) return
  
  try {
    const response = await userApi.checkPhoneAvailable(
      formData.value.phone,
      props.mode === 'edit' ? props.user?.id : undefined
    )
    if (!response.data.available) {
      message.error('手机号已被使用')
    }
  } catch (error) {
    console.error('检查手机号失败:', error)
  }
}

const handleDepartmentChange = () => {
  // 如果只有一个部门，自动设为主部门
  if (formData.value.department_ids.length === 1) {
    formData.value.primary_department_id = formData.value.department_ids[0]
  } else if (formData.value.department_ids.length === 0) {
    formData.value.primary_department_id = undefined
  } else if (formData.value.primary_department_id && 
             !formData.value.department_ids.includes(formData.value.primary_department_id)) {
    formData.value.primary_department_id = undefined
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    if (props.mode === 'create') {
      await userStore.createUser(formData.value)
    } else if (props.user) {
      const editData: UserEditForm = {
        nickname: formData.value.nickname,
        email: formData.value.email,
        phone: formData.value.phone,
        wechat_work_id: formData.value.wechat_work_id,
        is_active: formData.value.is_active,
        is_staff: formData.value.is_staff,
        department_ids: formData.value.department_ids,
        role_ids: formData.value.role_ids,
        primary_department_id: formData.value.primary_department_id
      }
      await userStore.updateUser(props.user.id, editData)
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (props.mode === 'create') {
        resetForm()
      } else {
        loadUserData()
      }
    })
  }
})
</script>

<style scoped>
.user-form-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
