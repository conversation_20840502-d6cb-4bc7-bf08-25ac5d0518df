<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="监控设置"
    class="monitor-settings-dialog"
    style="width: 600px"
    @update:show="$emit('update:visible', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="120px"
      require-mark-placement="right-hanging"
    >
      <n-tabs type="line">
        <n-tab-pane name="general" tab="常规设置">
          <n-form-item label="最大日志数" path="maxLogs">
            <n-input-number
              v-model:value="formData.maxLogs"
              :min="100"
              :max="10000"
              :step="100"
              placeholder="最大保留的日志条数"
              class="w-full"
            />
            <template #feedback>
              超过此数量的日志将被自动清理
            </template>
          </n-form-item>
          
          <n-form-item label="刷新间隔" path="refreshInterval">
            <n-select
              v-model:value="formData.refreshInterval"
              :options="refreshIntervalOptions"
              placeholder="选择刷新间隔"
            />
          </n-form-item>
          
          <n-form-item label="自动滚动" path="autoScroll">
            <n-switch
              v-model:value="formData.autoScroll"
              :checked-value="true"
              :unchecked-value="false"
            >
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </n-switch>
            <template #feedback>
              新日志到达时自动滚动到顶部
            </template>
          </n-form-item>
          
          <n-form-item label="显示列" path="visibleColumns">
            <n-checkbox-group v-model:value="formData.visibleColumns">
              <n-grid :cols="2" :x-gap="12" :y-gap="8">
                <n-checkbox-gi
                  v-for="column in columnOptions"
                  :key="column.value"
                  :value="column.value"
                  :label="column.label"
                />
              </n-grid>
            </n-checkbox-group>
          </n-form-item>
        </n-tab-pane>
        
        <n-tab-pane name="alerts" tab="告警设置">
          <n-form-item label="启用告警" path="enableAlerts">
            <n-switch
              v-model:value="formData.enableAlerts"
              :checked-value="true"
              :unchecked-value="false"
            >
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </n-switch>
          </n-form-item>
          
          <n-form-item label="错误率阈值" path="errorRateThreshold">
            <n-input-number
              v-model:value="formData.errorRateThreshold"
              :min="0"
              :max="100"
              :step="0.1"
              :precision="1"
              placeholder="错误率百分比"
              class="w-full"
            >
              <template #suffix>%</template>
            </n-input-number>
            <template #feedback>
              超过此错误率将触发告警
            </template>
          </n-form-item>
          
          <n-form-item label="响应时间阈值" path="responseTimeThreshold">
            <n-input-number
              v-model:value="formData.responseTimeThreshold"
              :min="100"
              :max="30000"
              :step="100"
              placeholder="响应时间毫秒数"
              class="w-full"
            >
              <template #suffix>ms</template>
            </n-input-number>
            <template #feedback>
              超过此响应时间将触发告警
            </template>
          </n-form-item>
          
          <n-form-item label="告警频率限制" path="alertRateLimit">
            <n-input-number
              v-model:value="formData.alertRateLimit"
              :min="1"
              :max="60"
              placeholder="每分钟最大告警数"
              class="w-full"
            />
            <template #feedback>
              限制每分钟的最大告警数量，避免告警风暴
            </template>
          </n-form-item>
          
          <n-form-item label="告警级别" path="alertLevels">
            <n-checkbox-group v-model:value="formData.alertLevels">
              <n-space>
                <n-checkbox value="info" label="信息" />
                <n-checkbox value="warning" label="警告" />
                <n-checkbox value="error" label="错误" />
              </n-space>
            </n-checkbox-group>
          </n-form-item>
        </n-tab-pane>
        
        <n-tab-pane name="notifications" tab="通知设置">
          <n-form-item label="声音提醒" path="enableSound">
            <n-switch
              v-model:value="formData.enableSound"
              :checked-value="true"
              :unchecked-value="false"
            >
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </n-switch>
          </n-form-item>
          
          <n-form-item label="桌面通知" path="enableDesktopNotification">
            <n-switch
              v-model:value="formData.enableDesktopNotification"
              :checked-value="true"
              :unchecked-value="false"
              @update:value="handleDesktopNotificationChange"
            >
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </n-switch>
            <template #feedback>
              需要浏览器授权桌面通知权限
            </template>
          </n-form-item>
          
          <n-form-item label="通知持续时间" path="notificationDuration">
            <n-input-number
              v-model:value="formData.notificationDuration"
              :min="1000"
              :max="30000"
              :step="1000"
              placeholder="通知显示时间"
              class="w-full"
            >
              <template #suffix>ms</template>
            </n-input-number>
          </n-form-item>
          
          <n-form-item label="邮件通知" path="enableEmailNotification">
            <n-switch
              v-model:value="formData.enableEmailNotification"
              :checked-value="true"
              :unchecked-value="false"
            >
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </n-switch>
          </n-form-item>
          
          <n-form-item
            v-if="formData.enableEmailNotification"
            label="邮件地址"
            path="emailAddress"
          >
            <n-input
              v-model:value="formData.emailAddress"
              placeholder="输入邮件地址"
              type="email"
            />
          </n-form-item>
        </n-tab-pane>
        
        <n-tab-pane name="filters" tab="过滤设置">
          <n-form-item label="默认过滤级别" path="defaultFilterLevel">
            <n-select
              v-model:value="formData.defaultFilterLevel"
              :options="filterLevelOptions"
              placeholder="选择默认过滤级别"
              clearable
            />
          </n-form-item>
          
          <n-form-item label="忽略的操作类型" path="ignoredOperationTypes">
            <n-select
              v-model:value="formData.ignoredOperationTypes"
              :options="operationTypeOptions"
              placeholder="选择要忽略的操作类型"
              multiple
              clearable
            />
          </n-form-item>
          
          <n-form-item label="忽略的状态码" path="ignoredStatusCodes">
            <n-dynamic-tags
              v-model:value="formData.ignoredStatusCodes"
              :render-tag="renderStatusCodeTag"
            />
            <template #feedback>
              输入要忽略的HTTP状态码，按回车添加
            </template>
          </n-form-item>
          
          <n-form-item label="关键词高亮" path="highlightKeywords">
            <n-dynamic-tags
              v-model:value="formData.highlightKeywords"
              :render-tag="renderKeywordTag"
            />
            <template #feedback>
              输入要高亮显示的关键词，按回车添加
            </template>
          </n-form-item>
        </n-tab-pane>
      </n-tabs>
    </n-form>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleReset">重置</n-button>
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          保存设置
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, h } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'

// Props
interface Props {
  visible: boolean
  settings: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'update:settings': [settings: any]
}>()

// 状态管理
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)

// 表单数据
const formData = ref({
  maxLogs: 1000,
  refreshInterval: 3000,
  autoScroll: true,
  visibleColumns: ['time', 'user', 'type', 'desc', 'method', 'ip', 'status', 'responseTime'],
  enableAlerts: true,
  errorRateThreshold: 5.0,
  responseTimeThreshold: 3000,
  alertRateLimit: 10,
  alertLevels: ['warning', 'error'],
  enableSound: true,
  enableDesktopNotification: false,
  notificationDuration: 5000,
  enableEmailNotification: false,
  emailAddress: '',
  defaultFilterLevel: '',
  ignoredOperationTypes: [],
  ignoredStatusCodes: [],
  highlightKeywords: []
})

// 选项数据
const refreshIntervalOptions = [
  { label: '1秒', value: 1000 },
  { label: '3秒', value: 3000 },
  { label: '5秒', value: 5000 },
  { label: '10秒', value: 10000 },
  { label: '30秒', value: 30000 }
]

const columnOptions = [
  { label: '时间', value: 'time' },
  { label: '用户', value: 'user' },
  { label: '操作类型', value: 'type' },
  { label: '操作描述', value: 'desc' },
  { label: '请求方法', value: 'method' },
  { label: 'IP地址', value: 'ip' },
  { label: '状态码', value: 'status' },
  { label: '响应时间', value: 'responseTime' }
]

const filterLevelOptions = [
  { label: '全部', value: '' },
  { label: '信息', value: 'info' },
  { label: '警告', value: 'warning' },
  { label: '错误', value: 'error' }
]

const operationTypeOptions = [
  { label: '登录', value: 'LOGIN' },
  { label: '登出', value: 'LOGOUT' },
  { label: '创建', value: 'CREATE' },
  { label: '更新', value: 'UPDATE' },
  { label: '删除', value: 'DELETE' },
  { label: '查询', value: 'QUERY' },
  { label: '系统异常', value: 'ERROR' }
]

// 表单验证规则
const formRules: FormRules = {
  maxLogs: [
    { type: 'number', min: 100, max: 10000, message: '最大日志数范围为100-10000', trigger: 'blur' }
  ],
  errorRateThreshold: [
    { type: 'number', min: 0, max: 100, message: '错误率阈值范围为0-100%', trigger: 'blur' }
  ],
  responseTimeThreshold: [
    { type: 'number', min: 100, max: 30000, message: '响应时间阈值范围为100-30000ms', trigger: 'blur' }
  ],
  alertRateLimit: [
    { type: 'number', min: 1, max: 60, message: '告警频率限制范围为1-60次/分钟', trigger: 'blur' }
  ],
  notificationDuration: [
    { type: 'number', min: 1000, max: 30000, message: '通知持续时间范围为1-30秒', trigger: 'blur' }
  ],
  emailAddress: [
    {
      validator: (rule, value) => {
        if (formData.value.enableEmailNotification && !value) {
          return false
        }
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return false
        }
        return true
      },
      message: '请输入有效的邮件地址',
      trigger: 'blur'
    }
  ]
}

// 方法
const renderStatusCodeTag = ({ option, handleClose }: any) => {
  return h(
    'n-tag',
    {
      closable: true,
      onClose: handleClose
    },
    option
  )
}

const renderKeywordTag = ({ option, handleClose }: any) => {
  return h(
    'n-tag',
    {
      type: 'info',
      closable: true,
      onClose: handleClose
    },
    option
  )
}

const handleDesktopNotificationChange = (enabled: boolean) => {
  if (enabled && 'Notification' in window) {
    if (Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission !== 'granted') {
          formData.value.enableDesktopNotification = false
          message.warning('桌面通知权限被拒绝')
        }
      })
    } else if (Notification.permission === 'denied') {
      formData.value.enableDesktopNotification = false
      message.warning('桌面通知权限被拒绝，请在浏览器设置中允许通知')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    emit('update:settings', { ...formData.value })
    emit('update:visible', false)
    
    message.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  formData.value = {
    maxLogs: 1000,
    refreshInterval: 3000,
    autoScroll: true,
    visibleColumns: ['time', 'user', 'type', 'desc', 'method', 'ip', 'status', 'responseTime'],
    enableAlerts: true,
    errorRateThreshold: 5.0,
    responseTimeThreshold: 3000,
    alertRateLimit: 10,
    alertLevels: ['warning', 'error'],
    enableSound: true,
    enableDesktopNotification: false,
    notificationDuration: 5000,
    enableEmailNotification: false,
    emailAddress: '',
    defaultFilterLevel: '',
    ignoredOperationTypes: [],
    ignoredStatusCodes: [],
    highlightKeywords: []
  }
  
  message.success('设置已重置为默认值')
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      // 加载当前设置
      formData.value = { ...props.settings }
    })
  }
})
</script>

<style scoped>
.monitor-settings-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.w-full {
  width: 100%;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
