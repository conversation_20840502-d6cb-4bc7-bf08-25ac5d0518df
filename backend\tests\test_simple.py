"""
简单测试 - 验证测试框架基础功能
"""
import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.users.models import UserProfile
from apps.departments.models import Department
from tests.factories import UserFactory, DepartmentFactory

User = get_user_model()


@pytest.mark.django_db
class TestSimpleFunctionality(TestCase):
    """简单功能测试"""
    
    def test_user_model_is_userprofile(self):
        """测试用户模型是UserProfile"""
        self.assertEqual(User, UserProfile)
    
    def test_create_user_directly(self):
        """测试直接创建用户"""
        user = UserProfile.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            nickname='测试用户'
        )
        
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.nickname, '测试用户')
        self.assertTrue(user.check_password('testpass123'))
    
    def test_user_factory_basic(self):
        """测试用户工厂基础功能"""
        user = UserFactory()
        
        self.assertIsNotNone(user)
        self.assertIsNotNone(user.username)
        self.assertIsNotNone(user.email)
        self.assertTrue(user.is_active)
    
    def test_department_factory_basic(self):
        """测试部门工厂基础功能"""
        department = DepartmentFactory()
        
        self.assertIsNotNone(department)
        self.assertIsNotNone(department.name)
        self.assertIsNotNone(department.code)
        self.assertTrue(department.is_active)
    
    def test_user_authentication(self):
        """测试用户认证"""
        user = UserProfile.objects.create_user(
            username='authtest',
            password='testpass123',
            nickname='认证测试'
        )
        
        # 测试正确密码
        self.assertTrue(user.check_password('testpass123'))
        
        # 测试错误密码
        self.assertFalse(user.check_password('wrongpass'))
    
    def test_database_operations(self):
        """测试数据库基本操作"""
        # 创建
        user = UserProfile.objects.create_user(
            username='dbtest',
            nickname='数据库测试',
            email='<EMAIL>'
        )
        user_id = user.id
        
        # 查询
        retrieved_user = UserProfile.objects.get(id=user_id)
        self.assertEqual(user.username, retrieved_user.username)
        
        # 更新
        retrieved_user.nickname = '更新的昵称'
        retrieved_user.save()
        
        # 验证更新
        updated_user = UserProfile.objects.get(id=user_id)
        self.assertEqual(updated_user.nickname, '更新的昵称')
        
        # 删除（软删除）
        updated_user.is_active = False
        updated_user.save()
        
        # 验证软删除
        inactive_user = UserProfile.objects.get(id=user_id)
        self.assertFalse(inactive_user.is_active)
    
    def test_department_hierarchy(self):
        """测试部门层级关系"""
        parent = Department.objects.create(
            name='父部门',
            code='PARENT',
            description='这是父部门'
        )
        
        child = Department.objects.create(
            name='子部门',
            code='CHILD',
            description='这是子部门',
            parent=parent
        )
        
        # 验证父子关系
        self.assertEqual(child.parent, parent)
        self.assertIn(child, parent.children.all())
    
    def test_user_str_representation(self):
        """测试用户字符串表示"""
        user = UserProfile.objects.create_user(
            username='strtest',
            nickname='字符串测试'
        )
        
        expected_str = '字符串测试 (strtest)'
        self.assertEqual(str(user), expected_str)
    
    def test_department_str_representation(self):
        """测试部门字符串表示"""
        department = Department.objects.create(
            name='测试部门',
            code='TEST_DEPT'
        )
        
        self.assertEqual(str(department), '测试部门')


@pytest.mark.unit
class TestFactoryBasics(TestCase):
    """工厂类基础测试"""
    
    def test_user_factory_sequence(self):
        """测试用户工厂序列生成"""
        users = UserFactory.create_batch(3)
        
        self.assertEqual(len(users), 3)
        
        # 验证用户名唯一性
        usernames = [user.username for user in users]
        self.assertEqual(len(usernames), len(set(usernames)))
    
    def test_department_factory_sequence(self):
        """测试部门工厂序列生成"""
        departments = DepartmentFactory.create_batch(3)
        
        self.assertEqual(len(departments), 3)
        
        # 验证部门编码唯一性
        codes = [dept.code for dept in departments]
        self.assertEqual(len(codes), len(set(codes)))


@pytest.mark.integration
class TestModelIntegrationBasic(TestCase):
    """基础模型集成测试"""
    
    def test_user_creation_and_retrieval(self):
        """测试用户创建和检索"""
        # 创建用户
        original_user = UserProfile.objects.create_user(
            username='integration_test',
            nickname='集成测试用户',
            email='<EMAIL>'
        )
        
        # 从数据库检索
        retrieved_user = UserProfile.objects.get(username='integration_test')
        
        # 验证数据一致性
        self.assertEqual(original_user.id, retrieved_user.id)
        self.assertEqual(original_user.username, retrieved_user.username)
        self.assertEqual(original_user.nickname, retrieved_user.nickname)
        self.assertEqual(original_user.email, retrieved_user.email)
    
    def test_department_creation_and_retrieval(self):
        """测试部门创建和检索"""
        # 创建部门
        original_dept = Department.objects.create(
            name='集成测试部门',
            code='INTEGRATION_DEPT',
            description='用于集成测试的部门'
        )
        
        # 从数据库检索
        retrieved_dept = Department.objects.get(code='INTEGRATION_DEPT')
        
        # 验证数据一致性
        self.assertEqual(original_dept.id, retrieved_dept.id)
        self.assertEqual(original_dept.name, retrieved_dept.name)
        self.assertEqual(original_dept.code, retrieved_dept.code)
        self.assertEqual(original_dept.description, retrieved_dept.description)
