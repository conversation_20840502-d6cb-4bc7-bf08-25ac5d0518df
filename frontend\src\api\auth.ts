/**
 * 认证相关 API
 */
import { request } from './index'
import type { ApiResponse } from '@/types/api'
import type {
  LoginForm,
  LoginResponse,
  RefreshTokenForm,
  RefreshTokenResponse,
  LogoutForm,
  CaptchaData,
  AuthStatusResponse
} from '@/types/auth'

// 注意：类型定义已移动到 @/types/auth.ts

// 认证 API 方法
export const authApi = {
  // 获取验证码
  getCaptcha(): Promise<ApiResponse<CaptchaData>> {
    return request.get('/auth/captcha/')
  },

  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return request.post('/auth/login/', data)
  },

  // 刷新令牌
  refreshToken(data: RefreshTokenForm): Promise<ApiResponse<RefreshTokenResponse>> {
    return request.post('/auth/refresh/', data)
  },

  // 用户登出
  logout(data: LogoutForm): Promise<ApiResponse<null>> {
    return request.post('/auth/logout/', data)
  },

  // 全设备登出
  logoutAll(): Promise<ApiResponse<null>> {
    return request.post('/auth/logout-all/')
  },

  // 获取认证状态
  getAuthStatus(): Promise<ApiResponse<AuthStatusResponse>> {
    return request.get('/auth/status/')
  },

  // 强制登出指定会话
  forceLogoutSession(sessionId: number): Promise<ApiResponse<null>> {
    return request.post(`/auth/sessions/${sessionId}/force-logout/`)
  },
}
