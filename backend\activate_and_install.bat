@echo off
echo === HEIM Backend Environment Setup ===
echo 使用uv包管理工具，遵循项目技术规范

echo 检查uv包管理工具...
uv --version
if %errorlevel% neq 0 (
    echo 错误: 未找到uv包管理工具，请先安装uv
    echo 安装命令: pip install uv
    pause
    exit /b 1
)

echo 使用uv同步项目依赖...
uv sync

echo 当前Python环境:
uv run python -c "import sys; print('Python executable:', sys.executable)"

echo 验证Django系统...
uv run python manage.py check

echo 验证Celery配置...
uv run python -c "from config.celery import app; print('✅ Celery应用导入成功:', app.main)"

echo 显示已安装的包:
uv pip list

echo 环境设置完成！
pause
