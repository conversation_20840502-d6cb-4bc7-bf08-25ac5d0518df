<template>
  <n-card title="部门详情" class="detail-card">
    <template #header-extra>
      <div class="detail-actions">
        <n-button
          v-permission="'department:update'"
          size="small"
          @click="handleEdit"
        >
          <template #icon>
            <n-icon><EditIcon /></n-icon>
          </template>
          编辑
        </n-button>
        <n-button
          v-permission="'department:update'"
          size="small"
          :type="departmentDetail?.is_active ? 'warning' : 'success'"
          @click="handleToggleStatus"
        >
          <template #icon>
            <n-icon><SettingsIcon /></n-icon>
          </template>
          {{ departmentDetail?.is_active ? '禁用' : '启用' }}
        </n-button>
        <n-button
          v-permission="'department:delete'"
          size="small"
          type="error"
          @click="handleDelete"
        >
          <template #icon>
            <n-icon><DeleteIcon /></n-icon>
          </template>
          删除
        </n-button>
      </div>
    </template>
    
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else-if="departmentDetail" class="detail-content">
      <!-- 基础信息 -->
      <div class="info-section">
        <h3 class="section-title">基础信息</h3>
        <n-grid :cols="2" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <div class="info-item">
              <label>部门名称</label>
              <span>{{ departmentDetail.name }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="info-item">
              <label>部门编码</label>
              <span>{{ departmentDetail.code }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="info-item">
              <label>部门状态</label>
              <n-tag :type="departmentDetail.is_active ? 'success' : 'error'" size="small">
                {{ departmentDetail.is_active ? '正常' : '禁用' }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="info-item">
              <label>部门层级</label>
              <span>第 {{ departmentDetail.level }} 级</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="info-item">
              <label>排序权重</label>
              <span>{{ departmentDetail.sort_order }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="info-item">
              <label>创建时间</label>
              <span>{{ formatDateTime(departmentDetail.created_at) }}</span>
            </div>
          </n-grid-item>
        </n-grid>
        
        <div v-if="departmentDetail.description" class="description">
          <label>部门描述</label>
          <p>{{ departmentDetail.description }}</p>
        </div>
      </div>

      <!-- 组织关系 -->
      <div class="info-section">
        <h3 class="section-title">组织关系</h3>
        <div class="org-info">
          <div v-if="departmentDetail.path" class="path-info">
            <label>部门路径</label>
            <n-breadcrumb>
              <n-breadcrumb-item
                v-for="ancestor in departmentDetail.ancestors"
                :key="ancestor.id"
              >
                {{ ancestor.name }}
              </n-breadcrumb-item>
              <n-breadcrumb-item>{{ departmentDetail.name }}</n-breadcrumb-item>
            </n-breadcrumb>
          </div>
          
          <div class="children-info">
            <label>子部门 ({{ departmentDetail.children?.length || 0 }})</label>
            <div v-if="departmentDetail.children && departmentDetail.children.length > 0" class="children-list">
              <n-tag
                v-for="child in departmentDetail.children"
                :key="child.id"
                class="child-tag"
                size="small"
              >
                {{ child.name }}
              </n-tag>
            </div>
            <span v-else class="empty-text">暂无子部门</span>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="info-section">
        <h3 class="section-title">统计信息</h3>
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <div class="stat-item">
              <div class="stat-value">{{ departmentDetail.statistics?.direct_member_count || 0 }}</div>
              <div class="stat-label">直属成员</div>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="stat-item">
              <div class="stat-value">{{ departmentDetail.statistics?.total_member_count || 0 }}</div>
              <div class="stat-label">总成员数</div>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="stat-item">
              <div class="stat-value">{{ departmentDetail.statistics?.manager_count || 0 }}</div>
              <div class="stat-label">主管数量</div>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="stat-item">
              <div class="stat-value">{{ departmentDetail.statistics?.children_count || 0 }}</div>
              <div class="stat-label">子部门数</div>
            </div>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 主管信息 -->
      <div v-if="departmentDetail.managers && departmentDetail.managers.length > 0" class="info-section">
        <h3 class="section-title">部门主管</h3>
        <div class="managers-list">
          <div
            v-for="manager in departmentDetail.managers"
            :key="manager.id"
            class="manager-item"
          >
            <div class="manager-info">
              <span class="manager-name">{{ manager.user_info.nickname || manager.user_info.username }}</span>
              <n-tag size="small" type="info">
                {{ getManagerLevelText(manager.manager_level) }}
              </n-tag>
              <span v-if="manager.position" class="manager-position">{{ manager.position }}</span>
            </div>
            <div class="manager-meta">
              <span class="manager-weight">权重: {{ manager.weight }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <n-empty description="未找到部门信息" />
    </div>
  </n-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { EditIcon, SettingsIcon, DeleteIcon } from '@vicons/tabler'
import { useDepartmentStore } from '@/stores/department'
import type { DepartmentDetail } from '@/types/department'

// Props
interface Props {
  departmentId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'edit': [department: DepartmentDetail]
  'delete': [departmentId: number]
  'toggle-status': [departmentId: number]
}>()

// 状态管理
const departmentStore = useDepartmentStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)

// 计算属性
const departmentDetail = computed(() => departmentStore.currentDepartment)

// 方法
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getManagerLevelText = (level?: number) => {
  switch (level) {
    case 1:
      return '一级主管'
    case 2:
      return '二级主管'
    case 3:
      return '三级主管'
    default:
      return '主管'
  }
}

const fetchDepartmentDetail = async () => {
  try {
    loading.value = true
    await departmentStore.fetchDepartmentDetail(props.departmentId)
  } catch (error) {
    message.error('获取部门详情失败')
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  if (departmentDetail.value) {
    emit('edit', departmentDetail.value)
  }
}

const handleDelete = () => {
  emit('delete', props.departmentId)
}

const handleToggleStatus = () => {
  emit('toggle-status', props.departmentId)
}

// 监听器
watch(() => props.departmentId, (newId) => {
  if (newId) {
    fetchDepartmentDetail()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.departmentId) {
    fetchDepartmentDetail()
  }
})
</script>

<style scoped>
.detail-card {
  margin-bottom: 24px;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20px;
}

.info-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #1f2937;
}

.description {
  margin-top: 16px;
}

.description label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  display: block;
  margin-bottom: 8px;
}

.description p {
  font-size: 14px;
  color: #1f2937;
  margin: 0;
  line-height: 1.5;
}

.org-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.path-info,
.children-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.path-info label,
.children-info label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.children-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.child-tag {
  margin: 0;
}

.empty-text {
  font-size: 14px;
  color: #9ca3af;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.managers-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.manager-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.manager-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.manager-name {
  font-weight: 500;
  color: #1f2937;
}

.manager-position {
  font-size: 12px;
  color: #6b7280;
}

.manager-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.manager-weight {
  font-size: 12px;
  color: #6b7280;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
