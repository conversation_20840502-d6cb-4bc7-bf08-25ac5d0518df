/**
 * API 相关类型定义
 */

// 统一API响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页响应类型
export interface PaginatedResponse<T> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  page_size?: number
  search?: string
  ordering?: string
}

// 错误响应类型
export interface ErrorResponse {
  code: number
  message: string
  details?: Record<string, string[]>
  timestamp: string
}

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

// 请求配置类型
export interface RequestConfig {
  headers?: Record<string, string>
  params?: Record<string, any>
  timeout?: number
}

// 文件上传响应类型
export interface FileUploadResponse {
  url: string
  filename: string
  size: number
  content_type: string
}

// 批量操作请求类型
export interface BatchOperationRequest {
  ids: number[]
  action: string
  params?: Record<string, any>
}

// 批量操作响应类型
export interface BatchOperationResponse {
  success_count: number
  failed_count: number
  errors: Array<{
    id: number
    error: string
  }>
}
