"""
数据备份和恢复服务
"""
import os
import subprocess
import gzip
import bz2
import lzma
import shutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.conf import settings
from django.db import connection
from django.utils import timezone
from django.core.management import call_command
from io import StringIO

from .models import Backup<PERSON>ob, RestoreJob, BackupSchedule, BackupStorage

logger = logging.getLogger(__name__)


class BackupService:
    """备份服务"""
    
    def __init__(self):
        self.backup_dir = getattr(settings, 'BACKUP_DIR', os.path.join(settings.BASE_DIR, 'backups'))
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """确保备份目录存在"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir, exist_ok=True)
            logger.info(f"创建备份目录: {self.backup_dir}")
    
    def create_backup(self, backup_job: BackupJob) -> bool:
        """执行备份任务"""
        try:
            # 更新任务状态
            backup_job.status = 'running'
            backup_job.started_at = timezone.now()
            backup_job.progress = 0
            backup_job.save()
            
            logger.info(f"开始执行备份任务: {backup_job.name}")
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{backup_job.name}_{backup_job.backup_type}_{timestamp}"
            
            # 执行数据库备份
            if backup_job.backup_type == 'full':
                backup_file = self._create_full_backup(backup_job, backup_filename)
            elif backup_job.backup_type == 'incremental':
                backup_file = self._create_incremental_backup(backup_job, backup_filename)
            else:
                backup_file = self._create_differential_backup(backup_job, backup_filename)
            
            if not backup_file:
                raise Exception("备份文件创建失败")
            
            # 压缩备份文件
            if backup_job.compression != 'none':
                backup_file = self._compress_backup(backup_file, backup_job.compression)
            
            # 计算文件哈希
            backup_job.backup_file_path = backup_file
            backup_job.backup_file_size = os.path.getsize(backup_file)
            backup_job.backup_file_hash = backup_job.calculate_file_hash()
            
            # 更新任务状态
            backup_job.status = 'completed'
            backup_job.completed_at = timezone.now()
            backup_job.progress = 100
            backup_job.save()
            
            logger.info(f"备份任务完成: {backup_job.name}, 文件: {backup_file}")
            return True
            
        except Exception as e:
            # 更新任务状态为失败
            backup_job.status = 'failed'
            backup_job.error_message = str(e)
            backup_job.completed_at = timezone.now()
            backup_job.save()
            
            logger.error(f"备份任务失败: {backup_job.name}, 错误: {e}")
            return False
    
    def _create_full_backup(self, backup_job: BackupJob, filename: str) -> str:
        """创建全量备份"""
        backup_file = os.path.join(self.backup_dir, f"{filename}.sql")
        
        # 使用Django的dumpdata命令
        if backup_job.include_tables:
            # 备份指定表
            apps_models = []
            for table in backup_job.include_tables:
                if '.' in table:
                    apps_models.append(table)
                else:
                    # 如果只提供表名，尝试找到对应的app.model
                    apps_models.append(f"*.{table}")
        else:
            # 备份所有数据
            apps_models = None
        
        try:
            with open(backup_file, 'w', encoding='utf-8') as f:
                if apps_models:
                    call_command('dumpdata', *apps_models, stdout=f, format='json', indent=2)
                else:
                    call_command('dumpdata', stdout=f, format='json', indent=2)
            
            # 更新进度
            backup_job.progress = 50
            backup_job.save()
            
            # 如果包含媒体文件，添加媒体文件备份
            if backup_job.include_media:
                self._backup_media_files(backup_job, filename)
            
            backup_job.progress = 80
            backup_job.save()
            
            return backup_file
            
        except Exception as e:
            logger.error(f"创建全量备份失败: {e}")
            raise
    
    def _create_incremental_backup(self, backup_job: BackupJob, filename: str) -> str:
        """创建增量备份"""
        # 增量备份：只备份自上次备份以来的变更
        backup_file = os.path.join(self.backup_dir, f"{filename}_incremental.sql")
        
        # 获取上次备份时间
        last_backup = BackupJob.objects.filter(
            backup_type__in=['full', 'incremental'],
            status='completed',
            created_at__lt=backup_job.created_at
        ).order_by('-created_at').first()
        
        if not last_backup:
            # 如果没有上次备份，执行全量备份
            logger.warning("没有找到上次备份记录，执行全量备份")
            return self._create_full_backup(backup_job, filename)
        
        # 这里简化处理，实际应该根据时间戳过滤数据
        # 由于Django ORM的限制，这里使用全量备份的方式
        return self._create_full_backup(backup_job, filename)
    
    def _create_differential_backup(self, backup_job: BackupJob, filename: str) -> str:
        """创建差异备份"""
        # 差异备份：备份自上次全量备份以来的所有变更
        backup_file = os.path.join(self.backup_dir, f"{filename}_differential.sql")
        
        # 获取上次全量备份时间
        last_full_backup = BackupJob.objects.filter(
            backup_type='full',
            status='completed',
            created_at__lt=backup_job.created_at
        ).order_by('-created_at').first()
        
        if not last_full_backup:
            # 如果没有全量备份，执行全量备份
            logger.warning("没有找到全量备份记录，执行全量备份")
            return self._create_full_backup(backup_job, filename)
        
        # 这里简化处理，实际应该根据时间戳过滤数据
        return self._create_full_backup(backup_job, filename)
    
    def _backup_media_files(self, backup_job: BackupJob, filename: str):
        """备份媒体文件"""
        try:
            media_root = settings.MEDIA_ROOT
            if os.path.exists(media_root):
                media_backup_dir = os.path.join(self.backup_dir, f"{filename}_media")
                shutil.copytree(media_root, media_backup_dir)
                logger.info(f"媒体文件备份完成: {media_backup_dir}")
        except Exception as e:
            logger.warning(f"媒体文件备份失败: {e}")
    
    def _compress_backup(self, backup_file: str, compression: str) -> str:
        """压缩备份文件"""
        try:
            if compression == 'gzip':
                compressed_file = f"{backup_file}.gz"
                with open(backup_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            elif compression == 'bzip2':
                compressed_file = f"{backup_file}.bz2"
                with open(backup_file, 'rb') as f_in:
                    with bz2.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            elif compression == 'lzma':
                compressed_file = f"{backup_file}.xz"
                with open(backup_file, 'rb') as f_in:
                    with lzma.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                return backup_file
            
            # 删除原始文件
            os.remove(backup_file)
            logger.info(f"备份文件压缩完成: {compressed_file}")
            return compressed_file
            
        except Exception as e:
            logger.error(f"压缩备份文件失败: {e}")
            return backup_file
    
    def verify_backup(self, backup_job: BackupJob) -> bool:
        """验证备份文件完整性"""
        try:
            if not backup_job.backup_file_path or not os.path.exists(backup_job.backup_file_path):
                logger.error(f"备份文件不存在: {backup_job.backup_file_path}")
                return False
            
            # 验证文件哈希
            if not backup_job.verify_integrity():
                logger.error(f"备份文件完整性验证失败: {backup_job.backup_file_path}")
                return False
            
            # 验证文件可读性
            try:
                if backup_job.backup_file_path.endswith('.gz'):
                    with gzip.open(backup_job.backup_file_path, 'rt') as f:
                        f.read(1024)  # 读取前1KB验证
                elif backup_job.backup_file_path.endswith('.bz2'):
                    with bz2.open(backup_job.backup_file_path, 'rt') as f:
                        f.read(1024)
                elif backup_job.backup_file_path.endswith('.xz'):
                    with lzma.open(backup_job.backup_file_path, 'rt') as f:
                        f.read(1024)
                else:
                    with open(backup_job.backup_file_path, 'r') as f:
                        f.read(1024)
            except Exception as e:
                logger.error(f"备份文件读取验证失败: {e}")
                return False
            
            logger.info(f"备份文件验证通过: {backup_job.backup_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"备份验证异常: {e}")
            return False
    
    def cleanup_old_backups(self, retention_days: int = 30):
        """清理过期备份"""
        try:
            cutoff_date = timezone.now() - timedelta(days=retention_days)
            
            # 查找过期的备份任务
            old_backups = BackupJob.objects.filter(
                created_at__lt=cutoff_date,
                auto_cleanup=True
            )
            
            deleted_count = 0
            for backup in old_backups:
                try:
                    # 删除备份文件
                    if backup.backup_file_path and os.path.exists(backup.backup_file_path):
                        os.remove(backup.backup_file_path)
                        logger.info(f"删除过期备份文件: {backup.backup_file_path}")
                    
                    # 删除数据库记录
                    backup.delete()
                    deleted_count += 1
                    
                except Exception as e:
                    logger.error(f"删除过期备份失败: {backup.name}, 错误: {e}")
            
            logger.info(f"清理过期备份完成，删除 {deleted_count} 个备份")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理过期备份异常: {e}")
            return 0


class RestoreService:
    """恢复服务"""
    
    def __init__(self):
        self.backup_service = BackupService()
    
    def restore_backup(self, restore_job: RestoreJob) -> bool:
        """执行恢复任务"""
        try:
            # 更新任务状态
            restore_job.status = 'running'
            restore_job.started_at = timezone.now()
            restore_job.progress = 0
            restore_job.save()
            
            logger.info(f"开始执行恢复任务: {restore_job.name}")
            
            # 恢复前验证
            if restore_job.pre_restore_verification:
                if not self._verify_backup_before_restore(restore_job):
                    raise Exception("恢复前验证失败")
            
            restore_job.progress = 20
            restore_job.save()
            
            # 执行恢复
            if restore_job.restore_mode == 'full':
                success = self._restore_full_backup(restore_job)
            elif restore_job.restore_mode == 'partial':
                success = self._restore_partial_backup(restore_job)
            elif restore_job.restore_mode == 'schema_only':
                success = self._restore_schema_only(restore_job)
            else:  # data_only
                success = self._restore_data_only(restore_job)
            
            if not success:
                raise Exception("数据恢复失败")
            
            restore_job.progress = 80
            restore_job.save()
            
            # 恢复后验证
            if restore_job.post_restore_verification:
                restore_job.verification_passed = self._verify_after_restore(restore_job)
            else:
                restore_job.verification_passed = True
            
            # 更新任务状态
            restore_job.status = 'completed'
            restore_job.completed_at = timezone.now()
            restore_job.progress = 100
            restore_job.save()
            
            logger.info(f"恢复任务完成: {restore_job.name}")
            return True
            
        except Exception as e:
            # 更新任务状态为失败
            restore_job.status = 'failed'
            restore_job.error_message = str(e)
            restore_job.completed_at = timezone.now()
            restore_job.save()
            
            logger.error(f"恢复任务失败: {restore_job.name}, 错误: {e}")
            return False
    
    def _verify_backup_before_restore(self, restore_job: RestoreJob) -> bool:
        """恢复前验证备份文件"""
        return self.backup_service.verify_backup(restore_job.backup_job)
    
    def _restore_full_backup(self, restore_job: RestoreJob) -> bool:
        """恢复完整备份"""
        try:
            backup_file = restore_job.backup_job.backup_file_path
            
            # 解压备份文件（如果需要）
            if backup_file.endswith('.gz'):
                with gzip.open(backup_file, 'rt') as f:
                    backup_data = f.read()
            elif backup_file.endswith('.bz2'):
                with bz2.open(backup_file, 'rt') as f:
                    backup_data = f.read()
            elif backup_file.endswith('.xz'):
                with lzma.open(backup_file, 'rt') as f:
                    backup_data = f.read()
            else:
                with open(backup_file, 'r') as f:
                    backup_data = f.read()
            
            # 使用Django的loaddata命令恢复数据
            # 这里简化处理，实际应该更仔细地处理数据恢复
            logger.info("开始恢复数据...")
            
            # 创建临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                temp_file.write(backup_data)
                temp_file_path = temp_file.name
            
            try:
                # 使用loaddata命令恢复
                call_command('loaddata', temp_file_path)
                logger.info("数据恢复完成")
                return True
            finally:
                # 清理临时文件
                os.unlink(temp_file_path)
            
        except Exception as e:
            logger.error(f"恢复完整备份失败: {e}")
            return False
    
    def _restore_partial_backup(self, restore_job: RestoreJob) -> bool:
        """恢复部分备份"""
        # 这里简化处理，实际应该根据include_tables和exclude_tables过滤
        return self._restore_full_backup(restore_job)
    
    def _restore_schema_only(self, restore_job: RestoreJob) -> bool:
        """仅恢复数据库结构"""
        # 这里简化处理，实际应该只恢复表结构
        logger.info("仅恢复数据库结构（简化实现）")
        return True
    
    def _restore_data_only(self, restore_job: RestoreJob) -> bool:
        """仅恢复数据"""
        # 这里简化处理，实际应该只恢复数据
        return self._restore_full_backup(restore_job)
    
    def _verify_after_restore(self, restore_job: RestoreJob) -> bool:
        """恢复后验证"""
        try:
            # 简单的验证：检查数据库连接
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            logger.info("恢复后验证通过")
            return True
            
        except Exception as e:
            logger.error(f"恢复后验证失败: {e}")
            return False


# 全局服务实例
backup_service = BackupService()
restore_service = RestoreService()
