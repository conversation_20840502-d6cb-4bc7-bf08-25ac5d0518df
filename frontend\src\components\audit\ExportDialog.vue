<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="导出操作日志"
    class="export-dialog"
    style="width: 600px"
    @update:show="$emit('update:visible', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="导出格式" path="export_format">
        <n-radio-group v-model:value="formData.export_format">
          <n-radio value="excel">Excel格式</n-radio>
          <n-radio value="pdf">PDF格式</n-radio>
        </n-radio-group>
      </n-form-item>
      
      <n-form-item label="时间范围" path="time_range">
        <n-radio-group v-model:value="timeRangeType" @update:value="handleTimeRangeChange">
          <n-radio value="current">当前筛选结果</n-radio>
          <n-radio value="custom">自定义时间</n-radio>
          <n-radio value="all">全部数据</n-radio>
        </n-radio-group>
      </n-form-item>
      
      <n-form-item v-if="timeRangeType === 'custom'" label="开始时间" path="start_date">
        <n-date-picker
          v-model:value="formData.start_date"
          type="datetime"
          placeholder="选择开始时间"
          class="w-full"
        />
      </n-form-item>
      
      <n-form-item v-if="timeRangeType === 'custom'" label="结束时间" path="end_date">
        <n-date-picker
          v-model:value="formData.end_date"
          type="datetime"
          placeholder="选择结束时间"
          class="w-full"
        />
      </n-form-item>
      
      <n-form-item label="导出字段">
        <n-checkbox-group v-model:value="selectedFields">
          <n-grid :cols="2" :x-gap="12" :y-gap="8">
            <n-checkbox-gi
              v-for="field in availableFields"
              :key="field.value"
              :value="field.value"
              :label="field.label"
            />
          </n-grid>
        </n-checkbox-group>
      </n-form-item>
      
      <n-form-item label="筛选条件">
        <div class="filter-summary">
          <div v-if="hasFilters" class="filter-items">
            <n-tag
              v-for="filter in filterSummary"
              :key="filter.key"
              size="small"
              closable
              @close="removeFilter(filter.key)"
            >
              {{ filter.label }}: {{ filter.value }}
            </n-tag>
          </div>
          <div v-else class="no-filters">
            <span class="text-gray-500">无筛选条件</span>
          </div>
        </div>
      </n-form-item>
      
      <n-form-item label="导出选项">
        <n-space vertical>
          <n-checkbox v-model:checked="formData.include_headers">
            包含表头
          </n-checkbox>
          <n-checkbox v-model:checked="formData.include_summary">
            包含统计摘要
          </n-checkbox>
          <n-checkbox v-if="formData.export_format === 'excel'" v-model:checked="formData.split_sheets">
            按操作类型分工作表
          </n-checkbox>
        </n-space>
      </n-form-item>
      
      <n-form-item label="文件名称" path="filename">
        <n-input
          v-model:value="formData.filename"
          placeholder="输入文件名（不含扩展名）"
        />
      </n-form-item>
      
      <!-- 预览信息 -->
      <n-form-item label="预览信息">
        <div class="export-preview">
          <div class="preview-item">
            <span class="preview-label">预计记录数:</span>
            <span class="preview-value">{{ estimatedCount.toLocaleString() }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">预计文件大小:</span>
            <span class="preview-value">{{ estimatedSize }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">预计耗时:</span>
            <span class="preview-value">{{ estimatedTime }}</span>
          </div>
        </div>
      </n-form-item>
    </n-form>
    
    <!-- 导出模板 -->
    <div v-if="exportTemplates.length > 0" class="export-templates">
      <n-divider>导出模板</n-divider>
      <div class="template-list">
        <n-button
          v-for="template in exportTemplates"
          :key="template.id"
          size="small"
          @click="handleLoadTemplate(template)"
        >
          {{ template.name }}
        </n-button>
        <n-button
          size="small"
          type="primary"
          dashed
          @click="showSaveTemplateDialog = true"
        >
          保存为模板
        </n-button>
      </div>
    </div>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          开始导出
        </n-button>
      </div>
    </template>
    
    <!-- 保存模板对话框 -->
    <n-modal
      :show="showSaveTemplateDialog"
      preset="dialog"
      title="保存导出模板"
      @update:show="showSaveTemplateDialog = $event"
    >
      <n-form>
        <n-form-item label="模板名称">
          <n-input
            v-model:value="templateName"
            placeholder="输入模板名称"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showSaveTemplateDialog = false">取消</n-button>
          <n-button
            type="primary"
            @click="handleSaveTemplate"
          >
            保存
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { auditApi } from '@/api/audit'
import type { 
  OperationLogSearchParams, 
  OperationLogExportParams, 
  ExportFormat,
  LogExportTemplate 
} from '@/types/audit'

// Props
interface Props {
  visible: boolean
  searchParams?: OperationLogSearchParams
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)
const timeRangeType = ref<'current' | 'custom' | 'all'>('current')
const selectedFields = ref<string[]>([
  'created_at_formatted',
  'user_nickname',
  'operation_type_display',
  'operation_desc',
  'method',
  'ip_address',
  'status_code',
  'response_time'
])
const showSaveTemplateDialog = ref(false)
const templateName = ref('')
const exportTemplates = ref<LogExportTemplate[]>([])

// 表单数据
const formData = ref<OperationLogExportParams & {
  include_headers: boolean
  include_summary: boolean
  split_sheets: boolean
  filename: string
}>({
  export_format: 'excel' as ExportFormat,
  start_date: undefined,
  end_date: undefined,
  user_id: undefined,
  operation_type: undefined,
  include_headers: true,
  include_summary: true,
  split_sheets: false,
  filename: `操作日志_${new Date().toISOString().split('T')[0]}`
})

// 可用字段
const availableFields = [
  { value: 'created_at_formatted', label: '操作时间' },
  { value: 'user_nickname', label: '操作用户' },
  { value: 'operation_type_display', label: '操作类型' },
  { value: 'operation_desc', label: '操作描述' },
  { value: 'method', label: '请求方法' },
  { value: 'path', label: '请求路径' },
  { value: 'ip_address', label: 'IP地址' },
  { value: 'status_code', label: '状态码' },
  { value: 'response_time', label: '响应时间' },
  { value: 'user_agent', label: '用户代理' }
]

// 表单验证规则
const formRules: FormRules = {
  export_format: [
    { required: true, message: '请选择导出格式', trigger: 'change' }
  ],
  start_date: [
    {
      validator: (rule, value) => {
        if (timeRangeType.value === 'custom' && !value) {
          return false
        }
        return true
      },
      message: '请选择开始时间',
      trigger: 'blur'
    }
  ],
  end_date: [
    {
      validator: (rule, value) => {
        if (timeRangeType.value === 'custom' && !value) {
          return false
        }
        if (timeRangeType.value === 'custom' && formData.value.start_date && value) {
          return new Date(value) > new Date(formData.value.start_date)
        }
        return true
      },
      message: '结束时间必须大于开始时间',
      trigger: 'blur'
    }
  ],
  filename: [
    { required: true, message: '请输入文件名', trigger: 'blur' },
    { min: 1, max: 100, message: '文件名长度为1-100个字符', trigger: 'blur' }
  ]
}

// 计算属性
const hasFilters = computed(() => {
  return Object.values(props.searchParams).some(value => 
    value !== undefined && value !== null && value !== ''
  )
})

const filterSummary = computed(() => {
  const summary = []
  
  if (props.searchParams.search) {
    summary.push({
      key: 'search',
      label: '关键词',
      value: props.searchParams.search
    })
  }
  
  if (props.searchParams.operation_type) {
    summary.push({
      key: 'operation_type',
      label: '操作类型',
      value: props.searchParams.operation_type
    })
  }
  
  if (props.searchParams.user_id) {
    summary.push({
      key: 'user_id',
      label: '用户',
      value: `用户ID: ${props.searchParams.user_id}`
    })
  }
  
  if (props.searchParams.ip_address) {
    summary.push({
      key: 'ip_address',
      label: 'IP地址',
      value: props.searchParams.ip_address
    })
  }
  
  if (props.searchParams.start_date || props.searchParams.end_date) {
    const start = props.searchParams.start_date ? new Date(props.searchParams.start_date).toLocaleDateString() : '不限'
    const end = props.searchParams.end_date ? new Date(props.searchParams.end_date).toLocaleDateString() : '不限'
    summary.push({
      key: 'date_range',
      label: '时间范围',
      value: `${start} 至 ${end}`
    })
  }
  
  return summary
})

const estimatedCount = computed(() => {
  // 这里应该根据筛选条件估算记录数
  // 简化处理，返回固定值
  return 1000
})

const estimatedSize = computed(() => {
  const recordCount = estimatedCount.value
  const fieldCount = selectedFields.value.length
  const avgFieldSize = 50 // 平均字段大小（字节）
  
  const sizeBytes = recordCount * fieldCount * avgFieldSize
  
  if (sizeBytes < 1024) {
    return `${sizeBytes} B`
  } else if (sizeBytes < 1024 * 1024) {
    return `${(sizeBytes / 1024).toFixed(1)} KB`
  } else {
    return `${(sizeBytes / (1024 * 1024)).toFixed(1)} MB`
  }
})

const estimatedTime = computed(() => {
  const recordCount = estimatedCount.value
  
  if (recordCount < 1000) {
    return '< 1分钟'
  } else if (recordCount < 10000) {
    return '1-3分钟'
  } else if (recordCount < 100000) {
    return '3-10分钟'
  } else {
    return '> 10分钟'
  }
})

// 方法
const handleTimeRangeChange = (value: string) => {
  if (value === 'current') {
    // 使用当前搜索参数的时间范围
    formData.value.start_date = props.searchParams.start_date
    formData.value.end_date = props.searchParams.end_date
  } else if (value === 'all') {
    // 清除时间限制
    formData.value.start_date = undefined
    formData.value.end_date = undefined
  }
}

const removeFilter = (key: string) => {
  // 这里可以实现移除特定筛选条件的逻辑
  message.info('筛选条件移除功能开发中')
}

const handleLoadTemplate = (template: LogExportTemplate) => {
  formData.value.export_format = template.format
  selectedFields.value = [...template.fields]
  
  if (template.filters) {
    Object.assign(formData.value, template.filters)
  }
  
  message.success(`已加载模板: ${template.name}`)
}

const handleSaveTemplate = async () => {
  if (!templateName.value.trim()) {
    message.error('请输入模板名称')
    return
  }
  
  try {
    const templateData: Partial<LogExportTemplate> = {
      name: templateName.value,
      fields: [...selectedFields.value],
      format: formData.value.export_format,
      filters: {
        start_date: formData.value.start_date,
        end_date: formData.value.end_date,
        user_id: formData.value.user_id,
        operation_type: formData.value.operation_type
      }
    }
    
    await auditApi.createExportTemplate(templateData)
    
    message.success('模板保存成功')
    showSaveTemplateDialog.value = false
    templateName.value = ''
    
    // 重新加载模板列表
    await loadExportTemplates()
  } catch (error) {
    message.error('模板保存失败')
  }
}

const loadExportTemplates = async () => {
  try {
    const response = await auditApi.getExportTemplateList()
    if (response.code === 200) {
      exportTemplates.value = response.data
    }
  } catch (error) {
    console.error('加载导出模板失败:', error)
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    const exportParams: OperationLogExportParams = {
      export_format: formData.value.export_format,
      start_date: formData.value.start_date,
      end_date: formData.value.end_date,
      user_id: formData.value.user_id,
      operation_type: formData.value.operation_type
    }
    
    // 根据时间范围类型设置参数
    if (timeRangeType.value === 'current') {
      Object.assign(exportParams, props.searchParams)
    }
    
    await auditApi.exportOperationLogs(exportParams)
    
    emit('success')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      // 重置表单
      formData.value.filename = `操作日志_${new Date().toISOString().split('T')[0]}`
      
      // 加载导出模板
      loadExportTemplates()
    })
  }
})
</script>

<style scoped>
.export-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.w-full {
  width: 100%;
}

.filter-summary {
  width: 100%;
}

.filter-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.no-filters {
  padding: 8px 0;
}

.text-gray-500 {
  color: #6b7280;
}

.export-preview {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-label {
  font-size: 14px;
  color: #6b7280;
}

.preview-value {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.export-templates {
  margin-top: 16px;
}

.template-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
