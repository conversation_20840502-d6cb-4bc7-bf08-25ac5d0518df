import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useMessage } from 'naive-ui'
import HomeView from '../views/HomeView.vue'

// 路由元信息类型定义
declare module 'vue-router' {
  interface RouteMeta {
    requiresAuth?: boolean
    permissions?: string[]
    roles?: string[]
    title?: string
    icon?: string
    hidden?: boolean
    noCache?: boolean
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        requiresAuth: true,
        title: '首页',
        icon: 'home'
      }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        requiresAuth: false,
        title: '登录',
        hidden: true
      }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: {
        requiresAuth: true,
        title: '关于',
        icon: 'info'
      }
    },
    {
      path: '/permission-test',
      name: 'permission-test',
      component: () => import('../views/PermissionTest.vue'),
      meta: {
        requiresAuth: true,
        title: '权限测试',
        icon: 'key'
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/profile/ProfileView.vue'),
      meta: {
        requiresAuth: true,
        title: '个人资料',
        icon: 'user'
      }
    },
    {
      path: '/user-manage-test',
      name: 'user-manage-test',
      component: () => import('../views/UserManageTest.vue'),
      meta: {
        requiresAuth: true,
        title: '用户管理测试',
        icon: 'test'
      }
    },
    // 系统管理模块
    {
      path: '/system',
      name: 'system',
      redirect: '/system/users',
      meta: {
        requiresAuth: true,
        permissions: ['system'],
        title: '系统管理',
        icon: 'setting'
      },
      children: [
        {
          path: 'users',
          name: 'system-users',
          component: () => import('../views/system/UserManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['user:manage'],
            title: '用户管理',
            icon: 'user'
          }
        },
        {
          path: 'departments',
          name: 'system-departments',
          component: () => import('../views/system/DepartmentManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['department:manage'],
            title: '部门管理',
            icon: 'building'
          }
        },
        {
          path: 'roles',
          name: 'system-roles',
          component: () => import('../views/system/RoleManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['role:manage'],
            title: '角色管理',
            icon: 'shield'
          }
        },
        {
          path: 'permissions',
          name: 'system-permissions',
          component: () => import('../views/system/PermissionManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['permission:manage'],
            title: '权限管理',
            icon: 'key'
          }
        },
        {
          path: 'audit-logs',
          name: 'system-audit-logs',
          component: () => import('../views/system/AuditLogManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['audit:view'],
            title: '审计日志',
            icon: 'file-text'
          }
        },
        {
          path: 'realtime-monitor',
          name: 'system-realtime-monitor',
          component: () => import('../views/system/RealTimeLogMonitor.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['audit:monitor'],
            title: '实时监控',
            icon: 'activity'
          }
        },
        {
          path: 'security-settings',
          name: 'system-security-settings',
          component: () => import('../views/system/SecuritySettings.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['system:security:manage'],
            title: '安全设置',
            icon: 'shield'
          }
        },
        {
          path: 'session-management',
          name: 'system-session-management',
          component: () => import('../views/system/SessionManagement.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['system:session:manage'],
            title: '会话管理',
            icon: 'users'
          }
        },
        {
          path: 'roles',
          name: 'system-roles',
          component: () => import('../views/system/RoleManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['role:manage'],
            title: '角色管理',
            icon: 'team'
          }
        },
        {
          path: 'permissions',
          name: 'system-permissions',
          component: () => import('../views/system/PermissionManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['permission:manage'],
            title: '权限管理',
            icon: 'key'
          }
        },
        {
          path: 'departments',
          name: 'system-departments',
          component: () => import('../views/system/DepartmentManage.vue'),
          meta: {
            requiresAuth: true,
            permissions: ['department:manage'],
            title: '部门管理',
            icon: 'apartment'
          }
        }
      ]
    },
    // 错误页面
    {
      path: '/403',
      name: 'error-403',
      component: () => import('../views/error/403.vue'),
      meta: {
        requiresAuth: false,
        title: '无权限',
        hidden: true
      }
    },
    {
      path: '/404',
      name: 'error-404',
      component: () => import('../views/error/404.vue'),
      meta: {
        requiresAuth: false,
        title: '页面未找到',
        hidden: true
      }
    },
    // 404 通配符路由，必须放在最后
    {
      path: '/:pathMatch(.*)*',
      redirect: '/404'
    }
  ]
})

// 权限检查函数
function hasPermission(userPermissions: string[], requiredPermissions?: string[]): boolean {
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true
  }

  // 检查是否有任意一个所需权限
  return requiredPermissions.some(permission => userPermissions.includes(permission))
}

// 角色检查函数
function hasRole(userRoles: string[], requiredRoles?: string[]): boolean {
  if (!requiredRoles || requiredRoles.length === 0) {
    return true
  }

  // 检查是否有任意一个所需角色
  return requiredRoles.some(role => userRoles.includes(role))
}

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const message = useMessage()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - HEIM企业管理平台`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    message.warning('请先登录')
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 已登录用户访问登录页，重定向到首页或原来要访问的页面
  if (to.name === 'login' && authStore.isLoggedIn) {
    const redirect = to.query.redirect as string
    next(redirect || '/')
    return
  }

  // 权限检查
  if (to.meta.requiresAuth && authStore.isLoggedIn) {
    const userPermissions = authStore.permissions
    const userRoles: string[] = [] // TODO: 从authStore获取用户角色

    // 检查权限
    if (to.meta.permissions && !hasPermission(userPermissions, to.meta.permissions)) {
      message.error('您没有权限访问此页面')
      next({
        path: '/403',
        query: {
          permission: to.meta.permissions.join(', '),
          from: from.fullPath
        }
      })
      return
    }

    // 检查角色
    if (to.meta.roles && !hasRole(userRoles, to.meta.roles)) {
      message.error('您的角色权限不足')
      next({
        path: '/403',
        query: {
          role: to.meta.roles.join(', '),
          from: from.fullPath
        }
      })
      return
    }
  }

  next()
})

// 路由后置守卫
router.afterEach((to, from) => {
  // 记录路由跳转日志
  console.log(`Route changed: ${from.path} -> ${to.path}`)

  // 可以在这里添加页面访问统计等功能
})

export default router
