"""
角色权限管理测试用例
包含角色CRUD操作、权限分配撤销、权限继承机制、权限验证装饰器的完整测试
"""
import pytest
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock

from apps.permissions.models import Role, Permission, UserRole
from apps.permissions.services import PermissionService
from apps.permissions.decorators import require_permission
from apps.users.models import UserProfile
from apps.departments.models import Department
from tests.factories import (
    UserFactory, UserProfileFactory, DepartmentFactory, 
    RoleFactory, PermissionFactory, create_user_with_profile
)

User = get_user_model()


@pytest.mark.django_db
@pytest.mark.permissions
class TestRoleManagementAPI(APITestCase):
    """角色管理API测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建管理员用户
        self.admin_user = UserProfileFactory(is_staff=True, is_superuser=True)
        self.admin_user.set_password('admin123')
        self.admin_user.save()
        
        # 创建普通用户
        self.normal_user = UserProfileFactory()
        self.normal_user.set_password('user123')
        self.normal_user.save()
        
        # API端点
        self.roles_url = reverse('permissions:role-list')
        self.role_detail_url = lambda pk: reverse('permissions:role-detail', kwargs={'pk': pk})
        
        # 管理员认证
        refresh = RefreshToken.for_user(self.admin_user)
        self.admin_token = str(refresh.access_token)
    
    def test_create_role_success(self):
        """测试成功创建角色"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        data = {
            'name': '测试角色',
            'code': 'TEST_ROLE',
            'description': '这是一个测试角色',
            'data_scope': 'DEPT_ONLY',
            'sort_order': 1
        }
        
        response = self.client.post(self.roles_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], '测试角色')
        self.assertEqual(response.data['code'], 'TEST_ROLE')
        self.assertEqual(response.data['data_scope'], 'DEPT_ONLY')
        
        # 验证角色已创建
        role = Role.objects.get(code='TEST_ROLE')
        self.assertIsNotNone(role)
        self.assertEqual(role.name, '测试角色')
    
    def test_create_role_duplicate_code(self):
        """测试创建重复编码的角色"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 先创建一个角色
        existing_role = RoleFactory(code='EXISTING_ROLE')
        
        data = {
            'name': '重复编码角色',
            'code': 'EXISTING_ROLE',  # 重复的编码
            'description': '测试重复编码'
        }
        
        response = self.client.post(self.roles_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('code', response.data)
    
    def test_get_role_list(self):
        """测试获取角色列表"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建测试角色
        roles = RoleFactory.create_batch(5)
        
        response = self.client.get(self.roles_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['results']), 5)
    
    def test_get_role_detail(self):
        """测试获取角色详情"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        role = RoleFactory()
        url = self.role_detail_url(role.id)
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], role.id)
        self.assertEqual(response.data['name'], role.name)
    
    def test_update_role_success(self):
        """测试成功更新角色"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        role = RoleFactory()
        url = self.role_detail_url(role.id)
        
        data = {
            'name': '更新的角色名称',
            'description': '更新的角色描述',
            'data_scope': 'ALL'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证更新
        role.refresh_from_db()
        self.assertEqual(role.name, '更新的角色名称')
        self.assertEqual(role.description, '更新的角色描述')
        self.assertEqual(role.data_scope, 'ALL')
    
    def test_delete_role_soft_delete(self):
        """测试软删除角色"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        role = RoleFactory()
        url = self.role_detail_url(role.id)
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # 验证软删除
        role.refresh_from_db()
        self.assertFalse(role.is_active)
    
    def test_role_permission_assignment(self):
        """测试角色权限分配"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        role = RoleFactory()
        permissions = PermissionFactory.create_batch(3)
        
        # 分配权限
        url = reverse('permissions:role-assign-permissions', kwargs={'pk': role.id})
        data = {
            'permission_ids': [p.id for p in permissions]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证权限分配
        role.refresh_from_db()
        self.assertEqual(role.permissions.count(), 3)
        for permission in permissions:
            self.assertIn(permission, role.permissions.all())
    
    def test_role_permission_revocation(self):
        """测试角色权限撤销"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        role = RoleFactory()
        permissions = PermissionFactory.create_batch(3)
        role.permissions.set(permissions)
        
        # 撤销部分权限
        permissions_to_revoke = permissions[:2]
        url = reverse('permissions:role-revoke-permissions', kwargs={'pk': role.id})
        data = {
            'permission_ids': [p.id for p in permissions_to_revoke]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证权限撤销
        role.refresh_from_db()
        self.assertEqual(role.permissions.count(), 1)
        self.assertNotIn(permissions_to_revoke[0], role.permissions.all())
        self.assertNotIn(permissions_to_revoke[1], role.permissions.all())
        self.assertIn(permissions[2], role.permissions.all())
    
    def test_role_user_assignment(self):
        """测试角色用户分配"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        role = RoleFactory()
        department = DepartmentFactory()
        
        # 分配用户到角色
        url = reverse('permissions:role-assign-users', kwargs={'pk': role.id})
        data = {
            'user_ids': [self.normal_user.id],
            'department_id': department.id
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证用户角色关联
        user_role = UserRole.objects.filter(
            user=self.normal_user,
            role=role,
            department=department
        ).first()
        self.assertIsNotNone(user_role)
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        # 不设置认证头
        response = self.client.get(self.roles_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_insufficient_permission(self):
        """测试权限不足"""
        # 使用普通用户
        refresh = RefreshToken.for_user(self.normal_user)
        normal_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {normal_token}')
        
        data = {
            'name': '无权限角色',
            'code': 'NO_PERM_ROLE'
        }
        
        response = self.client.post(self.roles_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


@pytest.mark.django_db
@pytest.mark.permissions
class TestPermissionManagementAPI(APITestCase):
    """权限管理API测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建管理员用户
        self.admin_user = UserProfileFactory(is_staff=True, is_superuser=True)
        self.admin_user.set_password('admin123')
        self.admin_user.save()
        
        # API端点
        self.permissions_url = reverse('permissions:permission-list')
        self.permission_detail_url = lambda pk: reverse('permissions:permission-detail', kwargs={'pk': pk})
        
        # 管理员认证
        refresh = RefreshToken.for_user(self.admin_user)
        self.admin_token = str(refresh.access_token)
    
    def test_create_permission_success(self):
        """测试成功创建权限"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        data = {
            'name': '测试权限',
            'code': 'test:permission',
            'permission_type': 'API',
            'path': '/api/test/',
            'http_method': 'GET',
            'sort_order': 1
        }
        
        response = self.client.post(self.permissions_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], '测试权限')
        self.assertEqual(response.data['code'], 'test:permission')
        
        # 验证权限已创建
        permission = Permission.objects.get(code='test:permission')
        self.assertIsNotNone(permission)
    
    def test_create_permission_hierarchy(self):
        """测试创建权限层级"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建父权限
        parent_permission = PermissionFactory(name='父权限', code='parent:permission')
        
        # 创建子权限
        data = {
            'name': '子权限',
            'code': 'child:permission',
            'parent': parent_permission.id,
            'permission_type': 'BUTTON'
        }
        
        response = self.client.post(self.permissions_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证父子关系
        child_permission = Permission.objects.get(code='child:permission')
        self.assertEqual(child_permission.parent, parent_permission)
        self.assertIn(child_permission, parent_permission.children.all())
    
    def test_get_permission_tree(self):
        """测试获取权限树"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建权限层级
        parent = PermissionFactory(name='父权限')
        child1 = PermissionFactory(name='子权限1', parent=parent)
        child2 = PermissionFactory(name='子权限2', parent=parent)
        
        url = reverse('permissions:permission-tree')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证树形结构
        tree_data = response.data
        self.assertIsInstance(tree_data, list)


@pytest.mark.django_db
@pytest.mark.unit
class TestPermissionService(TestCase):
    """权限服务单元测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user = UserProfileFactory()
        self.department = DepartmentFactory()
        self.role = RoleFactory()
        self.permission = PermissionFactory(code='test:permission')

        # 建立用户部门关系
        from apps.users.models import UserDepartment
        UserDepartment.objects.create(
            user=self.user,
            department=self.department,
            is_primary=True,
            is_active=True
        )

        # 建立用户角色关系
        UserRole.objects.create(
            user=self.user,
            role=self.role,
            department=self.department
        )

        # 角色分配权限
        self.role.permissions.add(self.permission)
    
    def test_check_user_permission_success(self):
        """测试用户权限检查成功"""
        has_permission = PermissionService.check_user_permission(
            self.user, 'test:permission'
        )
        
        self.assertTrue(has_permission)
    
    def test_check_user_permission_failure(self):
        """测试用户权限检查失败"""
        has_permission = PermissionService.check_user_permission(
            self.user, 'nonexistent:permission'
        )
        
        self.assertFalse(has_permission)
    
    def test_get_user_permissions(self):
        """测试获取用户权限列表"""
        permissions = PermissionService.get_user_permissions(self.user)
        
        permission_codes = [p.code for p in permissions]
        self.assertIn('test:permission', permission_codes)
        self.assertIsInstance(permissions, list)
    
    def test_get_user_roles(self):
        """测试获取用户角色列表"""
        roles = PermissionService.get_user_roles(self.user)
        
        self.assertIn(self.role, roles)
        self.assertEqual(len(roles), 1)
    
    def test_permission_inheritance(self):
        """测试权限继承机制"""
        # 创建父子权限
        parent_permission = PermissionFactory(code='parent:permission')
        child_permission = PermissionFactory(code='child:permission')
        child_permission.parent = parent_permission
        child_permission.save()
        
        # 角色只分配父权限
        self.role.permissions.add(parent_permission)
        
        # 检查是否继承子权限
        has_parent = PermissionService.check_user_permission(
            self.user, 'parent:permission'
        )
        has_child = PermissionService.check_user_permission(
            self.user, 'child:permission'
        )

        # 调试信息
        child_permission.refresh_from_db()
        print(f"父权限: {parent_permission.code} (ID: {parent_permission.id})")
        print(f"子权限: {child_permission.code} (ID: {child_permission.id})")
        print(f"子权限的父权限: {child_permission.parent} (ID: {child_permission.parent.id if child_permission.parent else None})")
        print(f"父权限对象: {parent_permission}")
        print(f"用户权限: {[p.code for p in PermissionService.get_user_permissions(self.user)]}")

        self.assertTrue(has_parent)
        self.assertTrue(has_child)  # 应该继承父权限


@pytest.mark.django_db
@pytest.mark.unit
class TestPermissionDecorator(TestCase):
    """权限验证装饰器测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user = UserProfileFactory()
        self.permission = PermissionFactory(code='test:view')
        self.role = RoleFactory()
        
        # 建立权限关系
        self.role.permissions.add(self.permission)
        UserRole.objects.create(user=self.user, role=self.role)
    
    @patch('apps.permissions.decorators.PermissionService.check_user_permission')
    def test_permission_decorator_success(self, mock_check_permission):
        """测试权限装饰器验证成功"""
        mock_check_permission.return_value = True
        
        @require_permission('test:view')
        def test_view(request):
            return {'success': True}
        
        # 模拟请求对象
        request = Mock()
        request.user = Mock()
        request.user.is_authenticated = True
        request.user.is_superuser = False
        request.user.is_superuser = False
        
        result = test_view(request)
        
        self.assertEqual(result, {'success': True})
        mock_check_permission.assert_called_once_with(self.user, 'test:view')
    
    @patch('apps.permissions.decorators.PermissionService.check_user_permission')
    def test_permission_decorator_failure(self, mock_check_permission):
        """测试权限装饰器验证失败"""
        mock_check_permission.return_value = False
        
        @require_permission('test:admin')
        def test_view(request):
            return {'success': True}
        
        # 模拟请求对象
        request = Mock()
        request.user = Mock()
        request.user.is_authenticated = True
        request.user.is_superuser = False
        request.user.is_superuser = False
        
        result = test_view(request)
        
        # 应该返回403错误响应
        self.assertEqual(result.status_code, 403)
    
    def test_superuser_bypass(self):
        """测试超级用户绕过权限检查"""
        superuser = UserProfileFactory(is_superuser=True)
        
        @require_permission('any:permission')
        def test_view(request):
            return {'success': True}
        
        # 模拟请求对象
        request = Mock()
        request.user = Mock()
        request.user.is_authenticated = True
        request.user.is_superuser = True
        request.user.is_superuser = True
        
        result = test_view(request)
        
        self.assertEqual(result, {'success': True})
    
    def test_unauthenticated_user(self):
        """测试未认证用户"""
        @require_permission('test:view')
        def test_view(request):
            return {'success': True}
        
        # 模拟未认证请求
        request = Mock()
        request.user.is_authenticated = False
        
        result = test_view(request)
        
        # 应该返回401错误响应
        self.assertEqual(result.status_code, 401)
