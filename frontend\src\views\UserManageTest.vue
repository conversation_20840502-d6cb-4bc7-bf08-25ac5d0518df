<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">用户管理功能测试</h1>
      <p class="text-gray-600">测试用户管理的各项功能</p>
    </div>
    
    <div class="bg-white shadow rounded-lg p-6">
      <div class="space-y-6">
        <!-- API 测试 -->
        <div>
          <h2 class="text-lg font-semibold mb-4">API 接口测试</h2>
          <div class="grid grid-cols-2 gap-4">
            <n-button @click="testGetUserList" :loading="loading.userList">
              测试获取用户列表
            </n-button>
            <n-button @click="testGetUserStats" :loading="loading.userStats">
              测试获取用户统计
            </n-button>
            <n-button @click="testGetCurrentProfile" :loading="loading.profile">
              测试获取当前用户信息
            </n-button>
            <n-button @click="testCheckUsername" :loading="loading.checkUsername">
              测试检查用户名
            </n-button>
          </div>
        </div>

        <!-- 状态管理测试 -->
        <div>
          <h2 class="text-lg font-semibold mb-4">状态管理测试</h2>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">用户列表数量:</label>
              <span class="text-lg">{{ userStore.userList.length }}</span>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">加载状态:</label>
              <n-tag :type="userStore.loading ? 'warning' : 'success'">
                {{ userStore.loading ? '加载中' : '空闲' }}
              </n-tag>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">选中用户数:</label>
              <span class="text-lg">{{ userStore.selectedUserIds.length }}</span>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">分页信息:</label>
              <span class="text-sm">
                第 {{ userStore.pagination.page }} 页，
                共 {{ userStore.pagination.total }} 条
              </span>
            </div>
          </div>
        </div>

        <!-- 组件测试 -->
        <div>
          <h2 class="text-lg font-semibold mb-4">组件功能测试</h2>
          <div class="space-x-4">
            <n-button @click="showUserForm" type="primary">
              测试用户表单
            </n-button>
            <n-button @click="showUserDetail" type="info">
              测试用户详情
            </n-button>
            <n-button @click="showPasswordReset" type="warning">
              测试密码重置
            </n-button>
          </div>
        </div>

        <!-- 测试结果 -->
        <div>
          <h2 class="text-lg font-semibold mb-4">测试结果</h2>
          <div class="bg-gray-50 p-4 rounded max-h-60 overflow-y-auto">
            <div v-if="testResults.length === 0" class="text-gray-500">
              暂无测试结果
            </div>
            <div v-else class="space-y-2">
              <div
                v-for="(result, index) in testResults"
                :key="index"
                class="text-sm"
                :class="result.success ? 'text-green-600' : 'text-red-600'"
              >
                [{{ result.time }}] {{ result.message }}
              </div>
            </div>
          </div>
          <div class="mt-4">
            <n-button @click="clearResults" size="small">
              清除结果
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试对话框 -->
    <UserFormDialog
      v-model:visible="formVisible"
      :mode="'create'"
      @success="handleFormSuccess"
    />

    <UserDetailDialog
      v-model:visible="detailVisible"
      :user-id="1"
    />

    <PasswordResetDialog
      v-model:visible="passwordVisible"
      :user-id="1"
      @success="handlePasswordSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useUserStore } from '@/stores/user'
import { userApi } from '@/api/user'
import UserFormDialog from '@/components/user/UserFormDialog.vue'
import UserDetailDialog from '@/components/user/UserDetailDialog.vue'
import PasswordResetDialog from '@/components/user/PasswordResetDialog.vue'

// 状态管理
const userStore = useUserStore()
const message = useMessage()

// 响应式数据
const loading = ref({
  userList: false,
  userStats: false,
  profile: false,
  checkUsername: false
})

const testResults = ref<Array<{ time: string; message: string; success: boolean }>>([])

const formVisible = ref(false)
const detailVisible = ref(false)
const passwordVisible = ref(false)

// 添加测试结果
const addResult = (message: string, success: boolean) => {
  testResults.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    success
  })
}

// 测试方法
const testGetUserList = async () => {
  try {
    loading.value.userList = true
    await userStore.fetchUserList()
    addResult('获取用户列表成功', true)
    message.success('用户列表获取成功')
  } catch (error) {
    addResult(`获取用户列表失败: ${error}`, false)
    message.error('用户列表获取失败')
  } finally {
    loading.value.userList = false
  }
}

const testGetUserStats = async () => {
  try {
    loading.value.userStats = true
    await userStore.fetchUserStats()
    addResult('获取用户统计成功', true)
    message.success('用户统计获取成功')
  } catch (error) {
    addResult(`获取用户统计失败: ${error}`, false)
    message.error('用户统计获取失败')
  } finally {
    loading.value.userStats = false
  }
}

const testGetCurrentProfile = async () => {
  try {
    loading.value.profile = true
    const response = await userApi.getCurrentUserProfile()
    addResult('获取当前用户信息成功', true)
    message.success('当前用户信息获取成功')
  } catch (error) {
    addResult(`获取当前用户信息失败: ${error}`, false)
    message.error('当前用户信息获取失败')
  } finally {
    loading.value.profile = false
  }
}

const testCheckUsername = async () => {
  try {
    loading.value.checkUsername = true
    const response = await userApi.checkUsernameAvailable('test_user')
    addResult(`检查用户名可用性: ${response.data.available ? '可用' : '不可用'}`, true)
    message.success('用户名检查完成')
  } catch (error) {
    addResult(`检查用户名失败: ${error}`, false)
    message.error('用户名检查失败')
  } finally {
    loading.value.checkUsername = false
  }
}

const showUserForm = () => {
  formVisible.value = true
  addResult('打开用户表单对话框', true)
}

const showUserDetail = () => {
  detailVisible.value = true
  addResult('打开用户详情对话框', true)
}

const showPasswordReset = () => {
  passwordVisible.value = true
  addResult('打开密码重置对话框', true)
}

const handleFormSuccess = () => {
  addResult('用户表单操作成功', true)
  message.success('用户表单操作成功')
}

const handlePasswordSuccess = () => {
  addResult('密码重置成功', true)
  message.success('密码重置成功')
}

const clearResults = () => {
  testResults.value = []
}

// 生命周期
onMounted(() => {
  addResult('用户管理测试页面初始化完成', true)
})
</script>
