/**
 * 用户管理 API 接口
 */
import { request } from './index'
import type { ApiResponse } from '@/types/api'
import type {
  User,
  UserListItem,
  UserCreateForm,
  UserEditForm,
  UserSearchParams,
  UserListResponse,
  UserDetail,
  PasswordResetForm,
  ChangePasswordForm,
  ProfileForm,
  BatchOperationParams,
  UserStats,
  UserSession,
  LoginLog
} from '@/types/user'

/**
 * 获取用户列表
 */
export function getUserList(params?: UserSearchParams): Promise<ApiResponse<UserListResponse>> {
  return request.get('/api/users/', { params })
}

/**
 * 获取用户详情
 */
export function getUserDetail(id: number): Promise<ApiResponse<UserDetail>> {
  return request.get(`/api/users/${id}/`)
}

/**
 * 创建用户
 */
export function createUser(data: UserCreateForm): Promise<ApiResponse<User>> {
  return request.post('/api/users/', data)
}

/**
 * 更新用户信息
 */
export function updateUser(id: number, data: UserEditForm): Promise<ApiResponse<User>> {
  return request.put(`/api/users/${id}/`, data)
}

/**
 * 部分更新用户信息
 */
export function patchUser(id: number, data: Partial<UserEditForm>): Promise<ApiResponse<User>> {
  return request.patch(`/api/users/${id}/`, data)
}

/**
 * 删除用户（软删除）
 */
export function deleteUser(id: number): Promise<ApiResponse<void>> {
  return request.delete(`/api/users/${id}/`)
}

/**
 * 恢复已删除的用户
 */
export function restoreUser(id: number): Promise<ApiResponse<User>> {
  return request.post(`/api/users/${id}/restore/`)
}

/**
 * 切换用户激活状态
 */
export function toggleUserActive(id: number): Promise<ApiResponse<{ is_active: boolean }>> {
  return request.post(`/api/users/${id}/toggle_active/`)
}

/**
 * 重置用户密码（管理员功能）
 */
export function resetUserPassword(id: number, data: PasswordResetForm): Promise<ApiResponse<void>> {
  return request.post(`/api/users/${id}/reset_password/`, data)
}

/**
 * 获取当前用户信息
 */
export function getCurrentUserProfile(): Promise<ApiResponse<User>> {
  return request.get('/api/users/profile/')
}

/**
 * 更新当前用户个人资料
 */
export function updateCurrentUserProfile(data: ProfileForm): Promise<ApiResponse<User>> {
  return request.put('/api/users/update_profile/', data)
}

/**
 * 修改当前用户密码
 */
export function changeCurrentUserPassword(data: ChangePasswordForm): Promise<ApiResponse<void>> {
  return request.post('/api/users/change_password/', data)
}

/**
 * 批量操作用户
 */
export function batchOperateUsers(data: BatchOperationParams): Promise<ApiResponse<void>> {
  return request.post('/api/users/batch_operation/', data)
}

/**
 * 获取用户统计信息
 */
export function getUserStats(): Promise<ApiResponse<UserStats>> {
  return request.get('/api/users/stats/')
}

/**
 * 获取用户会话列表
 */
export function getUserSessions(userId: number): Promise<ApiResponse<UserSession[]>> {
  return request.get(`/api/users/${userId}/sessions/`)
}

/**
 * 强制下线用户会话
 */
export function forceLogoutSession(userId: number, sessionId: number): Promise<ApiResponse<void>> {
  return request.post(`/api/users/${userId}/sessions/${sessionId}/logout/`)
}

/**
 * 获取用户登录日志
 */
export function getUserLoginLogs(userId: number, params?: { page?: number; page_size?: number }): Promise<ApiResponse<LoginLog[]>> {
  return request.get(`/api/users/${userId}/login_logs/`, { params })
}

/**
 * 检查用户名是否可用
 */
export function checkUsernameAvailable(username: string): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/users/check_username/', { params: { username } })
}

/**
 * 检查邮箱是否可用
 */
export function checkEmailAvailable(email: string, excludeUserId?: number): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/users/check_email/', { 
    params: { 
      email, 
      exclude_user_id: excludeUserId 
    } 
  })
}

/**
 * 检查手机号是否可用
 */
export function checkPhoneAvailable(phone: string, excludeUserId?: number): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/users/check_phone/', { 
    params: { 
      phone, 
      exclude_user_id: excludeUserId 
    } 
  })
}

/**
 * 上传用户头像
 */
export function uploadUserAvatar(file: File): Promise<ApiResponse<{ url: string }>> {
  const formData = new FormData()
  formData.append('avatar', file)
  
  return request.post('/api/users/upload_avatar/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出用户数据
 */
export function exportUsers(params?: UserSearchParams): Promise<Blob> {
  return request.get('/api/users/export/', {
    params,
    responseType: 'blob'
  }) as Promise<Blob>
}

/**
 * 导入用户数据
 */
export function importUsers(file: File): Promise<ApiResponse<{ success_count: number; failed_count: number; errors: string[] }>> {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/api/users/import/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 用户管理API对象
export const userApi = {
  getUserList,
  getUserDetail,
  createUser,
  updateUser,
  patchUser,
  deleteUser,
  restoreUser,
  toggleUserActive,
  resetUserPassword,
  getCurrentUserProfile,
  updateCurrentUserProfile,
  changeCurrentUserPassword,
  batchOperateUsers,
  getUserStats,
  getUserSessions,
  forceLogoutSession,
  getUserLoginLogs,
  checkUsernameAvailable,
  checkEmailAvailable,
  checkPhoneAvailable,
  uploadUserAvatar,
  exportUsers,
  importUsers
}
