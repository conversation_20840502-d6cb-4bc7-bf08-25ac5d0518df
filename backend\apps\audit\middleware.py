"""
审计日志中间件
自动记录HTTP请求的操作日志
"""
import time
import logging
from django.utils import timezone
from django.urls import resolve
from apps.audit.services import AuditService

logger = logging.getLogger(__name__)


class AuditLogMiddleware:
    """审计日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # 不记录日志的路径
        self.exclude_paths = [
            '/static/',
            '/media/',
            '/favicon.ico',
            '/health/',
            '/api/auth/refresh/',  # 令牌刷新请求太频繁
        ]
        
        # 不记录日志的HTTP方法
        self.exclude_methods = ['OPTIONS']
    
    def __call__(self, request):
        # 记录请求开始时间
        start_time = time.time()
        
        # 处理请求
        response = self.get_response(request)
        
        # 计算响应时间
        response_time = int((time.time() - start_time) * 1000)  # 毫秒
        
        # 记录操作日志
        self._log_operation(request, response, response_time)
        
        return response
    
    def _should_log_request(self, request):
        """判断是否应该记录请求"""
        # 排除特定路径
        for exclude_path in self.exclude_paths:
            if request.path.startswith(exclude_path):
                return False
        
        # 排除特定HTTP方法
        if request.method in self.exclude_methods:
            return False
        
        return True
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    def _get_operation_type(self, request, response):
        """根据请求方法和响应状态码确定操作类型"""
        method = request.method.upper()
        status_code = response.status_code
        
        # 登录相关
        if 'login' in request.path.lower():
            if status_code == 200:
                return 'LOGIN'
            else:
                return 'ERROR'
        
        # 登出相关
        if 'logout' in request.path.lower():
            return 'LOGOUT'
        
        # 根据HTTP方法判断
        if method == 'GET':
            return 'QUERY'
        elif method == 'POST':
            if status_code in [200, 201]:
                return 'CREATE'
            else:
                return 'ERROR'
        elif method in ['PUT', 'PATCH']:
            if status_code == 200:
                return 'UPDATE'
            else:
                return 'ERROR'
        elif method == 'DELETE':
            if status_code in [200, 204]:
                return 'DELETE'
            else:
                return 'ERROR'
        
        # 错误状态码
        if status_code >= 400:
            return 'ERROR'
        
        return 'QUERY'  # 默认
    
    def _get_operation_description(self, request, response):
        """生成操作描述"""
        method = request.method.upper()
        path = request.path
        status_code = response.status_code
        
        # 尝试解析URL名称
        try:
            url_match = resolve(path)
            url_name = url_match.url_name or ''
            app_name = url_match.app_name or ''
            
            if app_name and url_name:
                operation_desc = f"{app_name}:{url_name}"
            elif url_name:
                operation_desc = url_name
            else:
                operation_desc = f"{method} {path}"
        except:
            operation_desc = f"{method} {path}"
        
        # 添加状态码信息
        if status_code >= 400:
            operation_desc += f" (错误: {status_code})"
        
        return operation_desc
    
    def _log_operation(self, request, response, response_time):
        """记录操作日志"""
        try:
            # 检查是否应该记录
            if not self._should_log_request(request):
                return
            
            # 获取用户信息
            user = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user = request.user
            
            # 获取操作信息
            operation_type = self._get_operation_type(request, response)
            operation_desc = self._get_operation_description(request, response)
            
            # 获取请求信息
            ip_address = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # 记录日志
            AuditService.log_operation(
                user=user,
                operation_type=operation_type,
                operation_desc=operation_desc,
                method=request.method,
                path=request.path,
                ip_address=ip_address,
                user_agent=user_agent,
                status_code=response.status_code,
                response_time=response_time
            )
            
        except Exception as e:
            # 记录日志失败不应该影响正常请求
            logger.error(f"审计日志记录失败: {e}")
    
    def process_exception(self, request, exception):
        """处理异常"""
        try:
            # 记录异常日志
            user = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user = request.user
            
            operation_desc = f"异常: {exception.__class__.__name__} - {str(exception)}"
            ip_address = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            AuditService.log_operation(
                user=user,
                operation_type='ERROR',
                operation_desc=operation_desc,
                method=request.method,
                path=request.path,
                ip_address=ip_address,
                user_agent=user_agent,
                status_code=500,
                response_time=0
            )
            
        except Exception as e:
            logger.error(f"异常日志记录失败: {e}")
        
        # 不处理异常，让其他中间件处理
        return None
