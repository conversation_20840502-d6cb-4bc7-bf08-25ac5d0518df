/**
 * 部门管理 API 接口
 */
import { request } from './index'
import type { ApiResponse } from '@/types/api'
import type {
  Department,
  DepartmentTree,
  DepartmentListItem,
  DepartmentDetail,
  DepartmentPath,
  DepartmentCreateForm,
  DepartmentEditForm,
  DepartmentSearchParams,
  DepartmentMemberSearchParams,
  DepartmentMemberForm,
  DepartmentStats,
  DepartmentMoveOperation,
  DepartmentBatchOperationParams,
  UserDepartment,
  DepartmentListResponse,
  DepartmentTreeResponse,
  DepartmentMemberResponse
} from '@/types/department'

/**
 * 获取部门列表
 */
export function getDepartmentList(params?: DepartmentSearchParams): Promise<ApiResponse<DepartmentListResponse>> {
  return request.get('/api/departments/', { params })
}

/**
 * 获取部门树形结构
 */
export function getDepartmentTree(): Promise<ApiResponse<DepartmentTree[]>> {
  return request.get('/api/departments/tree/')
}

/**
 * 获取部门详情
 */
export function getDepartmentDetail(id: number): Promise<ApiResponse<DepartmentDetail>> {
  return request.get(`/api/departments/${id}/`)
}

/**
 * 创建部门
 */
export function createDepartment(data: DepartmentCreateForm): Promise<ApiResponse<Department>> {
  return request.post('/api/departments/', data)
}

/**
 * 更新部门信息
 */
export function updateDepartment(id: number, data: DepartmentEditForm): Promise<ApiResponse<Department>> {
  return request.put(`/api/departments/${id}/`, data)
}

/**
 * 部分更新部门信息
 */
export function patchDepartment(id: number, data: Partial<DepartmentEditForm>): Promise<ApiResponse<Department>> {
  return request.patch(`/api/departments/${id}/`, data)
}

/**
 * 删除部门（软删除）
 */
export function deleteDepartment(id: number): Promise<ApiResponse<void>> {
  return request.delete(`/api/departments/${id}/`)
}

/**
 * 恢复已删除的部门
 */
export function restoreDepartment(id: number): Promise<ApiResponse<Department>> {
  return request.post(`/api/departments/${id}/restore/`)
}

/**
 * 切换部门激活状态
 */
export function toggleDepartmentActive(id: number): Promise<ApiResponse<{ is_active: boolean }>> {
  return request.post(`/api/departments/${id}/toggle_active/`)
}

/**
 * 获取部门路径
 */
export function getDepartmentPath(id: number): Promise<ApiResponse<DepartmentPath>> {
  return request.get(`/api/departments/${id}/path/`)
}

/**
 * 获取部门统计信息
 */
export function getDepartmentStats(): Promise<ApiResponse<DepartmentStats>> {
  return request.get('/api/departments/statistics/')
}

/**
 * 获取部门成员列表
 */
export function getDepartmentMembers(
  departmentId: number, 
  params?: DepartmentMemberSearchParams
): Promise<ApiResponse<UserDepartment[]>> {
  return request.get(`/api/departments/${departmentId}/members/`, { params })
}

/**
 * 添加部门成员
 */
export function addDepartmentMember(
  departmentId: number, 
  data: DepartmentMemberForm
): Promise<ApiResponse<UserDepartment>> {
  return request.post(`/api/departments/${departmentId}/add_member/`, data)
}

/**
 * 更新部门成员信息
 */
export function updateDepartmentMember(
  departmentId: number, 
  data: Partial<DepartmentMemberForm> & { user_id: number }
): Promise<ApiResponse<UserDepartment>> {
  return request.put(`/api/departments/${departmentId}/update_member/`, data)
}

/**
 * 移除部门成员
 */
export function removeDepartmentMember(
  departmentId: number, 
  userId: number
): Promise<ApiResponse<void>> {
  return request.delete(`/api/departments/${departmentId}/remove_member/`, {
    data: { user_id: userId }
  })
}

/**
 * 移动部门位置
 */
export function moveDepartment(data: DepartmentMoveOperation): Promise<ApiResponse<void>> {
  return request.post('/api/departments/move/', data)
}

/**
 * 批量操作部门
 */
export function batchOperateDepartments(data: DepartmentBatchOperationParams): Promise<ApiResponse<void>> {
  return request.post('/api/departments/batch_operation/', data)
}

/**
 * 检查部门编码是否可用
 */
export function checkDepartmentCodeAvailable(
  code: string, 
  excludeDepartmentId?: number
): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/departments/check_code/', { 
    params: { 
      code, 
      exclude_department_id: excludeDepartmentId 
    } 
  })
}

/**
 * 检查部门名称是否可用
 */
export function checkDepartmentNameAvailable(
  name: string, 
  parentId?: number | null,
  excludeDepartmentId?: number
): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/departments/check_name/', { 
    params: { 
      name,
      parent_id: parentId,
      exclude_department_id: excludeDepartmentId 
    } 
  })
}

/**
 * 获取可选的父部门列表
 */
export function getAvailableParentDepartments(
  excludeDepartmentId?: number
): Promise<ApiResponse<DepartmentListItem[]>> {
  return request.get('/api/departments/available_parents/', {
    params: { exclude_department_id: excludeDepartmentId }
  })
}

/**
 * 获取部门的所有子部门
 */
export function getDepartmentChildren(
  departmentId: number,
  includeInactive?: boolean
): Promise<ApiResponse<Department[]>> {
  return request.get(`/api/departments/${departmentId}/children/`, {
    params: { include_inactive: includeInactive }
  })
}

/**
 * 获取部门的所有祖先部门
 */
export function getDepartmentAncestors(departmentId: number): Promise<ApiResponse<Department[]>> {
  return request.get(`/api/departments/${departmentId}/ancestors/`)
}

/**
 * 获取部门的所有后代部门
 */
export function getDepartmentDescendants(
  departmentId: number,
  includeInactive?: boolean
): Promise<ApiResponse<Department[]>> {
  return request.get(`/api/departments/${departmentId}/descendants/`, {
    params: { include_inactive: includeInactive }
  })
}

/**
 * 导出部门数据
 */
export function exportDepartments(params?: DepartmentSearchParams): Promise<Blob> {
  return request.get('/api/departments/export/', { 
    params,
    responseType: 'blob'
  }) as Promise<Blob>
}

/**
 * 导入部门数据
 */
export function importDepartments(file: File): Promise<ApiResponse<{ 
  success_count: number
  failed_count: number
  errors: string[] 
}>> {
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post('/api/departments/import/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取用户可管理的部门列表
 */
export function getUserManageableDepartments(): Promise<ApiResponse<Department[]>> {
  return request.get('/api/departments/manageable/')
}

/**
 * 获取用户所属的部门列表
 */
export function getUserDepartments(userId?: number): Promise<ApiResponse<UserDepartment[]>> {
  const url = userId ? `/api/departments/user_departments/${userId}/` : '/api/departments/user_departments/'
  return request.get(url)
}

// 部门管理API对象
export const departmentApi = {
  getDepartmentList,
  getDepartmentTree,
  getDepartmentDetail,
  createDepartment,
  updateDepartment,
  patchDepartment,
  deleteDepartment,
  restoreDepartment,
  toggleDepartmentActive,
  getDepartmentPath,
  getDepartmentStats,
  getDepartmentMembers,
  addDepartmentMember,
  updateDepartmentMember,
  removeDepartmentMember,
  moveDepartment,
  batchOperateDepartments,
  checkDepartmentCodeAvailable,
  checkDepartmentNameAvailable,
  getAvailableParentDepartments,
  getDepartmentChildren,
  getDepartmentAncestors,
  getDepartmentDescendants,
  exportDepartments,
  importDepartments,
  getUserManageableDepartments,
  getUserDepartments
}
