/**
 * 角色权限管理 API 接口
 */
import { request } from './index'
import type { ApiResponse } from '@/types/api'
import type {
  Role,
  RoleDetail,
  Permission,
  PermissionTree,
  UserRole,
  RoleCreateForm,
  RoleEditForm,
  PermissionCreateForm,
  PermissionEditForm,
  RoleAssignmentForm,
  RoleSearchParams,
  PermissionSearchParams,
  UserRoleSearchParams,
  DataScopeOption,
  PermissionTypeOption,
  RoleStats,
  PermissionStats,
  RoleBatchOperationParams,
  PermissionBatchOperationParams,
  RoleCopyForm,
  PermissionComparison,
  RoleUsage,
  PermissionUsage,
  RoleListResponse,
  PermissionListResponse,
  UserRoleListResponse
} from '@/types/role'

/**
 * 角色管理 API
 */

// 获取角色列表
export function getRoleList(params?: RoleSearchParams): Promise<ApiResponse<RoleListResponse>> {
  return request.get('/api/roles/', { params })
}

// 获取角色详情
export function getRoleDetail(id: number): Promise<ApiResponse<RoleDetail>> {
  return request.get(`/api/roles/${id}/`)
}

// 创建角色
export function createRole(data: RoleCreateForm): Promise<ApiResponse<Role>> {
  return request.post('/api/roles/', data)
}

// 更新角色
export function updateRole(id: number, data: RoleEditForm): Promise<ApiResponse<Role>> {
  return request.put(`/api/roles/${id}/`, data)
}

// 部分更新角色
export function patchRole(id: number, data: Partial<RoleEditForm>): Promise<ApiResponse<Role>> {
  return request.patch(`/api/roles/${id}/`, data)
}

// 删除角色
export function deleteRole(id: number): Promise<ApiResponse<void>> {
  return request.delete(`/api/roles/${id}/`)
}

// 切换角色激活状态
export function toggleRoleActive(id: number): Promise<ApiResponse<{ is_active: boolean }>> {
  return request.post(`/api/roles/${id}/toggle_active/`)
}

// 获取数据范围选项
export function getDataScopeOptions(): Promise<ApiResponse<DataScopeOption[]>> {
  return request.get('/api/roles/data_scopes/')
}

// 获取角色权限
export function getRolePermissions(id: number): Promise<ApiResponse<Permission[]>> {
  return request.get(`/api/roles/${id}/permissions/`)
}

// 分配权限给角色
export function assignPermissionsToRole(id: number, permissionIds: number[]): Promise<ApiResponse<void>> {
  return request.post(`/api/roles/${id}/assign_permissions/`, {
    permission_ids: permissionIds
  })
}

// 获取角色用户
export function getRoleUsers(id: number): Promise<ApiResponse<UserRole[]>> {
  return request.get(`/api/roles/${id}/users/`)
}

// 分配用户给角色
export function assignUsersToRole(id: number, data: RoleAssignmentForm): Promise<ApiResponse<void>> {
  return request.post(`/api/roles/${id}/assign_users/`, data)
}

// 复制角色
export function copyRole(data: RoleCopyForm): Promise<ApiResponse<Role>> {
  return request.post('/api/roles/copy/', data)
}

// 批量操作角色
export function batchOperateRoles(data: RoleBatchOperationParams): Promise<ApiResponse<void>> {
  return request.post('/api/roles/batch_operation/', data)
}

// 获取角色统计信息
export function getRoleStats(): Promise<ApiResponse<RoleStats>> {
  return request.get('/api/roles/statistics/')
}

// 获取角色使用情况
export function getRoleUsage(id: number): Promise<ApiResponse<RoleUsage>> {
  return request.get(`/api/roles/${id}/usage/`)
}

// 比较角色权限
export function compareRolePermissions(role1Id: number, role2Id: number): Promise<ApiResponse<PermissionComparison>> {
  return request.get('/api/roles/compare/', {
    params: { role1_id: role1Id, role2_id: role2Id }
  })
}

/**
 * 权限管理 API
 */

// 获取权限列表
export function getPermissionList(params?: PermissionSearchParams): Promise<ApiResponse<PermissionListResponse>> {
  return request.get('/api/permissions/', { params })
}

// 获取权限树形结构
export function getPermissionTree(): Promise<ApiResponse<PermissionTree[]>> {
  return request.get('/api/permissions/tree/')
}

// 获取权限详情
export function getPermissionDetail(id: number): Promise<ApiResponse<Permission>> {
  return request.get(`/api/permissions/${id}/`)
}

// 创建权限
export function createPermission(data: PermissionCreateForm): Promise<ApiResponse<Permission>> {
  return request.post('/api/permissions/', data)
}

// 更新权限
export function updatePermission(id: number, data: PermissionEditForm): Promise<ApiResponse<Permission>> {
  return request.put(`/api/permissions/${id}/`, data)
}

// 部分更新权限
export function patchPermission(id: number, data: Partial<PermissionEditForm>): Promise<ApiResponse<Permission>> {
  return request.patch(`/api/permissions/${id}/`, data)
}

// 删除权限
export function deletePermission(id: number): Promise<ApiResponse<void>> {
  return request.delete(`/api/permissions/${id}/`)
}

// 切换权限激活状态
export function togglePermissionActive(id: number): Promise<ApiResponse<{ is_active: boolean }>> {
  return request.post(`/api/permissions/${id}/toggle_active/`)
}

// 获取权限类型选项
export function getPermissionTypeOptions(): Promise<ApiResponse<PermissionTypeOption[]>> {
  return request.get('/api/permissions/types/')
}

// 批量操作权限
export function batchOperatePermissions(data: PermissionBatchOperationParams): Promise<ApiResponse<void>> {
  return request.post('/api/permissions/batch_operation/', data)
}

// 获取权限统计信息
export function getPermissionStats(): Promise<ApiResponse<PermissionStats>> {
  return request.get('/api/permissions/statistics/')
}

// 获取权限使用情况
export function getPermissionUsage(id: number): Promise<ApiResponse<PermissionUsage>> {
  return request.get(`/api/permissions/${id}/usage/`)
}

/**
 * 用户角色关联 API
 */

// 获取用户角色关联列表
export function getUserRoleList(params?: UserRoleSearchParams): Promise<ApiResponse<UserRoleListResponse>> {
  return request.get('/api/user-roles/', { params })
}

// 获取用户角色关联详情
export function getUserRoleDetail(id: number): Promise<ApiResponse<UserRole>> {
  return request.get(`/api/user-roles/${id}/`)
}

// 创建用户角色关联
export function createUserRole(data: Partial<UserRole>): Promise<ApiResponse<UserRole>> {
  return request.post('/api/user-roles/', data)
}

// 更新用户角色关联
export function updateUserRole(id: number, data: Partial<UserRole>): Promise<ApiResponse<UserRole>> {
  return request.put(`/api/user-roles/${id}/`, data)
}

// 删除用户角色关联
export function deleteUserRole(id: number): Promise<ApiResponse<void>> {
  return request.delete(`/api/user-roles/${id}/`)
}

// 批量删除用户角色关联
export function batchDeleteUserRoles(ids: number[]): Promise<ApiResponse<void>> {
  return request.post('/api/user-roles/batch_delete/', { ids })
}

// 获取用户权限
export function getUserPermissions(userId?: number): Promise<ApiResponse<Permission[]>> {
  const url = userId ? `/api/user-roles/user_permissions/${userId}/` : '/api/user-roles/user_permissions/'
  return request.get(url)
}

/**
 * 检查和验证 API
 */

// 检查角色编码是否可用
export function checkRoleCodeAvailable(code: string, excludeRoleId?: number): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/roles/check_code/', {
    params: {
      code,
      exclude_role_id: excludeRoleId
    }
  })
}

// 检查角色名称是否可用
export function checkRoleNameAvailable(name: string, excludeRoleId?: number): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/roles/check_name/', {
    params: {
      name,
      exclude_role_id: excludeRoleId
    }
  })
}

// 检查权限编码是否可用
export function checkPermissionCodeAvailable(code: string, excludePermissionId?: number): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/permissions/check_code/', {
    params: {
      code,
      exclude_permission_id: excludePermissionId
    }
  })
}

// 检查权限名称是否可用
export function checkPermissionNameAvailable(name: string, excludePermissionId?: number): Promise<ApiResponse<{ available: boolean }>> {
  return request.get('/api/permissions/check_name/', {
    params: {
      name,
      exclude_permission_id: excludePermissionId
    }
  })
}

// 角色权限管理API对象
export const roleApi = {
  // 角色管理
  getRoleList,
  getRoleDetail,
  createRole,
  updateRole,
  patchRole,
  deleteRole,
  toggleRoleActive,
  getDataScopeOptions,
  getRolePermissions,
  assignPermissionsToRole,
  getRoleUsers,
  assignUsersToRole,
  copyRole,
  batchOperateRoles,
  getRoleStats,
  getRoleUsage,
  compareRolePermissions,
  
  // 权限管理
  getPermissionList,
  getPermissionTree,
  getPermissionDetail,
  createPermission,
  updatePermission,
  patchPermission,
  deletePermission,
  togglePermissionActive,
  getPermissionTypeOptions,
  batchOperatePermissions,
  getPermissionStats,
  getPermissionUsage,
  
  // 用户角色关联
  getUserRoleList,
  getUserRoleDetail,
  createUserRole,
  updateUserRole,
  deleteUserRole,
  batchDeleteUserRoles,
  getUserPermissions,
  
  // 检查和验证
  checkRoleCodeAvailable,
  checkRoleNameAvailable,
  checkPermissionCodeAvailable,
  checkPermissionNameAvailable
}
