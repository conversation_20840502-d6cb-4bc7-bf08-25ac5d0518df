"""
用户认证功能单元测试
"""
import pytest
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.cache import cache
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock
from freezegun import freeze_time
from datetime import datetime, timedelta

from apps.authentication.models import UserSession, LoginAttempt
from apps.authentication.services import AuthenticationService
from apps.users.models import UserProfile
from tests.factories import (
    UserFactory, UserProfileFactory, UserSessionFactory, 
    LoginAttemptFactory, create_user_with_profile
)

User = get_user_model()


@pytest.mark.django_db
class TestAuthenticationAPI(APITestCase):
    """认证API测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        self.user, self.profile = create_user_with_profile()
        self.user.set_password('testpass123')
        self.user.save()
        
        # API端点
        self.login_url = reverse('authentication:login')
        self.logout_url = reverse('authentication:logout')
        self.refresh_url = reverse('authentication:refresh')
        # 注意：change_password可能在users应用中
        # self.change_password_url = reverse('users:change_password')
        
    def test_successful_login(self):
        """测试成功登录"""
        data = {
            'username': self.user.username,
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        
        # 验证用户信息
        user_data = response.data['user']
        self.assertEqual(user_data['id'], self.user.id)
        self.assertEqual(user_data['username'], self.user.username)
        
        # 验证会话记录
        session = UserSession.objects.filter(user=self.user).first()
        self.assertIsNotNone(session)
        self.assertTrue(session.is_active)
        
        # 验证登录尝试记录
        attempt = LoginAttempt.objects.filter(username=self.user.username).first()
        self.assertIsNotNone(attempt)
        self.assertTrue(attempt.success)
    
    def test_login_with_invalid_credentials(self):
        """测试无效凭据登录"""
        data = {
            'username': self.user.username,
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('error', response.data)
        
        # 验证失败的登录尝试记录
        attempt = LoginAttempt.objects.filter(username=self.user.username).first()
        self.assertIsNotNone(attempt)
        self.assertFalse(attempt.success)
        self.assertIn('密码错误', attempt.failure_reason)
    
    def test_login_with_inactive_user(self):
        """测试非活跃用户登录"""
        self.user.is_active = False
        self.user.save()
        
        data = {
            'username': self.user.username,
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('账户已被禁用', str(response.data))
    
    @patch('apps.authentication.views.get_captcha_key')
    def test_login_with_captcha_verification(self, mock_captcha):
        """测试验证码验证登录"""
        mock_captcha.return_value = True
        
        data = {
            'username': self.user.username,
            'password': 'testpass123',
            'captcha_key': 'test_key',
            'captcha_value': 'test_value'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_captcha.assert_called_once()
    
    def test_account_lockout_after_failed_attempts(self):
        """测试多次失败登录后账户锁定"""
        # 创建多次失败的登录尝试
        for i in range(5):
            LoginAttemptFactory(
                username=self.user.username,
                success=False,
                failure_reason='密码错误'
            )
        
        data = {
            'username': self.user.username,
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_423_LOCKED)
        self.assertIn('账户已被锁定', str(response.data))
    
    def test_successful_logout(self):
        """测试成功登出"""
        # 先登录获取token
        refresh = RefreshToken.for_user(self.user)
        access_token = str(refresh.access_token)
        
        # 创建会话记录
        session = UserSessionFactory(user=self.user, is_active=True)
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.post(self.logout_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证会话已失效
        session.refresh_from_db()
        self.assertFalse(session.is_active)
    
    def test_token_refresh(self):
        """测试令牌刷新"""
        refresh = RefreshToken.for_user(self.user)
        
        data = {
            'refresh': str(refresh)
        }
        
        response = self.client.post(self.refresh_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
    
    def test_token_refresh_with_invalid_token(self):
        """测试无效令牌刷新"""
        data = {
            'refresh': 'invalid_token'
        }
        
        response = self.client.post(self.refresh_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    # TODO: 修改密码功能测试 - 需要确认API端点
    # def test_change_password_success(self):
    #     """测试成功修改密码"""
    #     pass


@pytest.mark.django_db
class TestAuthenticationService(TestCase):
    """认证服务测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user, self.profile = create_user_with_profile()
        self.user.set_password('testpass123')
        self.user.save()
        self.auth_service = AuthenticationService()
    
    def test_authenticate_user_success(self):
        """测试用户认证成功"""
        result = self.auth_service.authenticate_user(
            username=self.user.username,
            password='testpass123',
            ip_address='127.0.0.1',
            user_agent='Test Agent'
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['user'], self.user)
        self.assertIn('tokens', result)
    
    def test_authenticate_user_failure(self):
        """测试用户认证失败"""
        result = self.auth_service.authenticate_user(
            username=self.user.username,
            password='wrongpass',
            ip_address='127.0.0.1',
            user_agent='Test Agent'
        )
        
        self.assertFalse(result['success'])
        self.assertIn('error', result)
    
    def test_create_user_session(self):
        """测试创建用户会话"""
        session = self.auth_service.create_user_session(
            user=self.user,
            ip_address='127.0.0.1',
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        self.assertIsNotNone(session)
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.ip_address, '127.0.0.1')
        self.assertTrue(session.is_active)
    
    def test_logout_user(self):
        """测试用户登出"""
        # 创建活跃会话
        session = UserSessionFactory(user=self.user, is_active=True)
        
        result = self.auth_service.logout_user(self.user)
        
        self.assertTrue(result)
        session.refresh_from_db()
        self.assertFalse(session.is_active)
    
    def test_check_account_lockout(self):
        """测试账户锁定检查"""
        # 创建多次失败尝试
        for _ in range(5):
            LoginAttemptFactory(
                username=self.user.username,
                success=False
            )
        
        is_locked = self.auth_service.is_account_locked(self.user.username)
        self.assertTrue(is_locked)
    
    @freeze_time("2024-01-01 12:00:00")
    def test_password_reset_token_generation(self):
        """测试密码重置令牌生成"""
        token = self.auth_service.generate_password_reset_token(self.user)
        
        self.assertIsNotNone(token)
        self.assertTrue(len(token) > 20)  # 确保令牌足够长
    
    def test_validate_password_strength(self):
        """测试密码强度验证"""
        # 强密码
        strong_password = 'StrongPass123!'
        is_valid, errors = self.auth_service.validate_password_strength(strong_password)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # 弱密码
        weak_password = '123'
        is_valid, errors = self.auth_service.validate_password_strength(weak_password)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)


@pytest.mark.django_db
class TestUserSessionModel(TestCase):
    """用户会话模型测试"""
    
    def test_session_creation(self):
        """测试会话创建"""
        user = UserFactory()
        session = UserSessionFactory(user=user)
        
        self.assertEqual(session.user, user)
        self.assertTrue(session.is_active)
        self.assertIsNotNone(session.login_time)
    
    def test_session_deactivation(self):
        """测试会话失效"""
        session = UserSessionFactory(is_active=True)
        
        session.deactivate()
        
        self.assertFalse(session.is_active)
        self.assertIsNotNone(session.logout_time)
    
    def test_session_duration_calculation(self):
        """测试会话持续时间计算"""
        with freeze_time("2024-01-01 12:00:00") as frozen_time:
            session = UserSessionFactory()
            
            # 模拟1小时后
            frozen_time.tick(delta=timedelta(hours=1))
            session.last_activity = frozen_time()
            session.save()
            
            duration = session.get_duration()
            self.assertEqual(duration.total_seconds(), 3600)  # 1小时


@pytest.mark.django_db
class TestLoginAttemptModel(TestCase):
    """登录尝试模型测试"""
    
    def test_successful_attempt_creation(self):
        """测试成功登录尝试创建"""
        attempt = LoginAttemptFactory(success=True)
        
        self.assertTrue(attempt.success)
        self.assertEqual(attempt.failure_reason, '')
    
    def test_failed_attempt_creation(self):
        """测试失败登录尝试创建"""
        attempt = LoginAttemptFactory(
            success=False,
            failure_reason='密码错误'
        )
        
        self.assertFalse(attempt.success)
        self.assertEqual(attempt.failure_reason, '密码错误')
    
    def test_recent_failed_attempts_count(self):
        """测试最近失败尝试次数统计"""
        username = 'testuser'
        
        # 创建3次失败尝试
        for _ in range(3):
            LoginAttemptFactory(
                username=username,
                success=False
            )
        
        # 创建1次成功尝试
        LoginAttemptFactory(
            username=username,
            success=True
        )
        
        failed_count = LoginAttempt.get_recent_failed_attempts(username)
        self.assertEqual(failed_count, 3)
