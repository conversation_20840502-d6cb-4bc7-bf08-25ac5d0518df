"""
配置管理相关视图
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth.decorators import user_passes_test
import logging

from .config_manager import config_manager
from .response import ApiResponse
from .exceptions import BusinessException, ErrorCode
from apps.permissions.decorators import require_permissions

logger = logging.getLogger(__name__)

def is_admin_user(user):
    """检查是否为管理员用户"""
    return user.is_authenticated and (user.is_superuser or user.has_perm('common.manage_config'))

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:view'])
def get_config_categories(request):
    """
    获取配置分类列表
    """
    try:
        categories = config_manager.get_categories()
        
        # 添加分类描述
        category_info = {
            'system': {'name': '系统配置', 'description': '系统基础设置'},
            'security': {'name': '安全配置', 'description': '安全策略和认证设置'},
            'upload': {'name': '上传配置', 'description': '文件上传相关设置'},
            'email': {'name': '邮件配置', 'description': '邮件服务设置'},
            'logging': {'name': '日志配置', 'description': '日志记录和审计设置'},
        }
        
        result = []
        for category in categories:
            info = category_info.get(category, {'name': category, 'description': ''})
            result.append({
                'key': category,
                'name': info['name'],
                'description': info['description']
            })
        
        return ApiResponse.success(data={'categories': result}, message="获取配置分类成功")
        
    except Exception as e:
        logger.error(f"获取配置分类异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取配置分类失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:view'])
def get_configs(request):
    """
    获取配置列表
    """
    try:
        category = request.GET.get('category')
        configs = config_manager.get_all_configs(category)
        
        # 过滤敏感信息
        filtered_configs = {}
        for key, config_data in configs.items():
            if key not in config_manager.SENSITIVE_KEYS:
                filtered_configs[key] = config_data
        
        return ApiResponse.success(data={'configs': filtered_configs}, message="获取配置成功")
        
    except Exception as e:
        logger.error(f"获取配置异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取配置失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:view'])
def get_config(request, key):
    """
    获取单个配置项
    """
    try:
        # 检查是否为敏感配置
        if key in config_manager.SENSITIVE_KEYS:
            raise BusinessException(ErrorCode.PERMISSION_DENIED, "无权访问敏感配置")
        
        # 检查配置项是否存在
        if key not in config_manager.CONFIGURABLE_ITEMS:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "配置项不存在")
        
        value = config_manager.get_config(key)
        definition = config_manager.CONFIGURABLE_ITEMS[key]
        
        return ApiResponse.success(
            data={
                'key': key,
                'value': value,
                'definition': definition
            },
            message="获取配置成功"
        )
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取配置异常: {key}, 错误: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取配置失败")

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:manage'])
def update_config(request, key):
    """
    更新配置项
    """
    try:
        value = request.data.get('value')
        if value is None:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请提供配置值")
        
        # 检查是否为敏感配置
        if key in config_manager.SENSITIVE_KEYS:
            raise BusinessException(ErrorCode.PERMISSION_DENIED, "无权修改敏感配置")
        
        # 更新配置
        success = config_manager.set_config(key, value, request.user.id)
        if not success:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "配置更新失败")
        
        # 获取更新后的值
        new_value = config_manager.get_config(key)
        
        logger.info(f"用户 {request.user.username} 更新配置: {key}={value}")
        
        return ApiResponse.success(
            data={
                'key': key,
                'value': new_value
            },
            message="配置更新成功"
        )
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"更新配置异常: {key}, 错误: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "更新配置失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:manage'])
def reset_config(request, key):
    """
    重置配置为默认值
    """
    try:
        # 检查是否为敏感配置
        if key in config_manager.SENSITIVE_KEYS:
            raise BusinessException(ErrorCode.PERMISSION_DENIED, "无权重置敏感配置")
        
        # 重置配置
        success = config_manager.reset_config(key, request.user.id)
        if not success:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "配置重置失败")
        
        # 获取重置后的值
        new_value = config_manager.get_config(key)
        
        logger.info(f"用户 {request.user.username} 重置配置: {key}")
        
        return ApiResponse.success(
            data={
                'key': key,
                'value': new_value
            },
            message="配置重置成功"
        )
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"重置配置异常: {key}, 错误: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "重置配置失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:manage'])
def batch_update_configs(request):
    """
    批量更新配置
    """
    try:
        configs = request.data.get('configs', {})
        if not configs:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请提供要更新的配置")
        
        results = {}
        success_count = 0
        
        for key, value in configs.items():
            try:
                # 检查是否为敏感配置
                if key in config_manager.SENSITIVE_KEYS:
                    results[key] = {'success': False, 'error': '无权修改敏感配置'}
                    continue
                
                # 更新配置
                success = config_manager.set_config(key, value, request.user.id)
                if success:
                    success_count += 1
                    results[key] = {'success': True, 'value': config_manager.get_config(key)}
                else:
                    results[key] = {'success': False, 'error': '配置更新失败'}
                    
            except Exception as e:
                results[key] = {'success': False, 'error': str(e)}
        
        logger.info(f"用户 {request.user.username} 批量更新配置: 成功 {success_count}/{len(configs)}")
        
        return ApiResponse.success(
            data={
                'total': len(configs),
                'success_count': success_count,
                'results': results
            },
            message=f"批量更新完成，成功 {success_count} 项配置"
        )
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"批量更新配置异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "批量更新配置失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:view'])
def validate_configs(request):
    """
    验证所有配置
    """
    try:
        errors = config_manager.validate_all_configs()
        
        is_valid = len(errors) == 0
        
        return ApiResponse.success(
            data={
                'is_valid': is_valid,
                'errors': errors,
                'total_configs': len(config_manager.CONFIGURABLE_ITEMS),
                'error_count': len(errors)
            },
            message="配置验证完成" if is_valid else f"发现 {len(errors)} 个配置错误"
        )
        
    except Exception as e:
        logger.error(f"验证配置异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "验证配置失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:view'])
def export_configs(request):
    """
    导出配置
    """
    try:
        category = request.GET.get('category')
        configs = config_manager.get_all_configs(category)
        
        # 过滤敏感信息
        export_data = {}
        for key, config_data in configs.items():
            if key not in config_manager.SENSITIVE_KEYS:
                export_data[key] = {
                    'value': config_data['value'],
                    'description': config_data['definition'].get('description', ''),
                    'category': config_data['definition'].get('category', ''),
                    'type': config_data['definition'].get('type', 'string')
                }
        
        from django.utils import timezone
        export_info = {
            'export_time': timezone.now().isoformat(),
            'export_user': request.user.username,
            'category': category or 'all',
            'total_configs': len(export_data)
        }
        
        return ApiResponse.success(
            data={
                'export_info': export_info,
                'configs': export_data
            },
            message="配置导出成功"
        )
        
    except Exception as e:
        logger.error(f"导出配置异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "导出配置失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['system:config:manage'])
def import_configs(request):
    """
    导入配置
    """
    try:
        configs = request.data.get('configs', {})
        if not configs:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请提供要导入的配置")
        
        results = {}
        success_count = 0
        
        for key, config_data in configs.items():
            try:
                value = config_data.get('value')
                if value is None:
                    results[key] = {'success': False, 'error': '配置值不能为空'}
                    continue
                
                # 检查是否为敏感配置
                if key in config_manager.SENSITIVE_KEYS:
                    results[key] = {'success': False, 'error': '无权导入敏感配置'}
                    continue
                
                # 导入配置
                success = config_manager.set_config(key, value, request.user.id)
                if success:
                    success_count += 1
                    results[key] = {'success': True}
                else:
                    results[key] = {'success': False, 'error': '配置导入失败'}
                    
            except Exception as e:
                results[key] = {'success': False, 'error': str(e)}
        
        logger.info(f"用户 {request.user.username} 导入配置: 成功 {success_count}/{len(configs)}")
        
        return ApiResponse.success(
            data={
                'total': len(configs),
                'success_count': success_count,
                'results': results
            },
            message=f"配置导入完成，成功 {success_count} 项配置"
        )
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"导入配置异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "导入配置失败")
