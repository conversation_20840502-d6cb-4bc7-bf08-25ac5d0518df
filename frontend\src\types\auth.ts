/**
 * 认证相关类型定义
 */

// 用户信息类型
export interface UserInfo {
  id: number
  username: string
  nickname: string
  email: string
  avatar: string
  phone?: string
  wechat_work_id?: string
  is_active: boolean
  last_login_time: string
  last_login_ip: string
  created_at: string
  updated_at: string
}

// 登录表单类型
export interface LoginForm {
  username: string
  password: string
  captcha: string
  captcha_key: string
}

// 登录响应类型
export interface LoginResponse {
  access: string
  refresh: string
  user: UserInfo
  permissions: string[]
  departments: DepartmentInfo[]
}

// 刷新令牌表单类型
export interface RefreshTokenForm {
  refresh: string
}

// 刷新令牌响应类型
export interface RefreshTokenResponse {
  access: string
  refresh: string
}

// 登出表单类型
export interface LogoutForm {
  refresh: string
}

// 验证码数据类型
export interface CaptchaData {
  captcha_key: string
  captcha_question: string
}

// 认证状态响应类型
export interface AuthStatusResponse {
  is_authenticated: boolean
  user_info?: UserInfo
  permissions?: string[]
  current_department?: number
}

// 部门信息类型
export interface DepartmentInfo {
  id: number
  name: string
  code: string
  is_primary: boolean
  is_manager: boolean
  position?: string
  manager_level?: number
}

// 权限类型
export interface Permission {
  id: number
  name: string
  code: string
  permission_type: 'MENU' | 'BUTTON' | 'API'
  path?: string
  component?: string
  icon?: string
  http_method?: string
  parent?: number
  children?: Permission[]
}

// 角色类型
export interface Role {
  id: number
  name: string
  code: string
  data_scope: 'ALL' | 'DEPT_AND_SUB' | 'DEPT_ONLY' | 'SELF_ONLY' | 'CUSTOM'
  description?: string
  permissions: Permission[]
}

// 会话信息类型
export interface SessionInfo {
  id: number
  session_key: string
  ip_address: string
  user_agent: string
  device_type: string
  browser: string
  os: string
  is_active: boolean
  last_activity: string
  expires_at: string
  created_at: string
}

// 安全信息类型
export interface SecurityInfo {
  login_fail_count: number
  locked_until?: string
  last_login_ip: string
  last_login_time: string
  active_sessions: SessionInfo[]
}
