/**
 * 权限指令
 * 用法：v-permission="'user:create'" 或 v-permission="['user:create', 'user:update']"
 */
import type { Directive, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/auth'
import {
  onPermissionChange,
  offPermissionChange,
  onDepartmentChange,
  offDepartmentChange,
  onLoginStatusChange,
  offLoginStatusChange,
  type PermissionEventListener
} from '@/utils/permission-events'

// 权限检查函数
function checkPermission(permissions: string | string[]): boolean {
  const authStore = useAuthStore()
  
  if (!authStore.isLoggedIn) {
    return false
  }
  
  const userPermissions = authStore.permissions
  
  if (typeof permissions === 'string') {
    return userPermissions.includes(permissions)
  }
  
  if (Array.isArray(permissions)) {
    // 检查是否拥有任意一个权限
    return permissions.some(permission => userPermissions.includes(permission))
  }
  
  return false
}

// 存储元素的权限监听器，用于清理
const elementListeners = new WeakMap<HTMLElement, PermissionEventListener[]>()

// 权限指令
export const vPermission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const permissions = binding.value

    // 初始权限检查
    if (!checkPermission(permissions)) {
      el.parentNode?.removeChild(el)
      return
    }

    // 设置实时更新监听器
    setupPermissionListeners(el, permissions, 'remove')
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const permissions = binding.value

    // 清理旧的监听器
    cleanupListeners(el)

    // 权限检查
    if (!checkPermission(permissions)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }

    // 重新设置监听器
    setupPermissionListeners(el, permissions, 'hide')
  },

  unmounted(el: HTMLElement) {
    // 清理监听器
    cleanupListeners(el)
  }
}

// 权限检查指令（只隐藏，不移除）
export const vPermissionHide: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const permissions = binding.value

    if (!checkPermission(permissions)) {
      el.style.display = 'none'
    }

    // 设置实时更新监听器
    setupPermissionListeners(el, permissions, 'hide')
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const permissions = binding.value

    // 清理旧的监听器
    cleanupListeners(el)

    if (!checkPermission(permissions)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }

    // 重新设置监听器
    setupPermissionListeners(el, permissions, 'hide')
  },

  unmounted(el: HTMLElement) {
    cleanupListeners(el)
  }
}

// 权限禁用指令
export const vPermissionDisable: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const permissions = binding.value
    
    if (!checkPermission(permissions)) {
      el.setAttribute('disabled', 'true')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    const permissions = binding.value
    
    if (!checkPermission(permissions)) {
      el.setAttribute('disabled', 'true')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
    } else {
      el.removeAttribute('disabled')
      el.style.opacity = ''
      el.style.cursor = ''
    }
  }
}

// 角色检查指令
export const vRole: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const roles = binding.value
    const authStore = useAuthStore()
    
    // 这里需要根据实际的角色数据结构来实现
    // 暂时使用权限检查的逻辑
    if (!checkPermission(roles)) {
      el.parentNode?.removeChild(el)
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    const roles = binding.value
    
    if (!checkPermission(roles)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// 设置权限监听器
function setupPermissionListeners(
  el: HTMLElement,
  permissions: string | string[],
  action: 'remove' | 'hide' | 'disable'
) {
  const listeners: PermissionEventListener[] = []

  // 权限变化监听器
  const permissionListener: PermissionEventListener = () => {
    updateElementPermission(el, permissions, action)
  }

  // 部门变化监听器
  const departmentListener: PermissionEventListener = () => {
    updateElementPermission(el, permissions, action)
  }

  // 登录状态变化监听器
  const loginListener: PermissionEventListener = () => {
    updateElementPermission(el, permissions, action)
  }

  // 注册监听器
  onPermissionChange(permissionListener)
  onDepartmentChange(departmentListener)
  onLoginStatusChange(loginListener)

  listeners.push(permissionListener, departmentListener, loginListener)

  // 存储监听器引用
  elementListeners.set(el, listeners)
}

// 更新元素权限状态
function updateElementPermission(
  el: HTMLElement,
  permissions: string | string[],
  action: 'remove' | 'hide' | 'disable'
) {
  const hasPermission = checkPermission(permissions)

  switch (action) {
    case 'remove':
      if (!hasPermission && el.parentNode) {
        el.parentNode.removeChild(el)
      }
      break
    case 'hide':
      el.style.display = hasPermission ? '' : 'none'
      break
    case 'disable':
      if (hasPermission) {
        el.removeAttribute('disabled')
        el.style.opacity = ''
        el.style.cursor = ''
      } else {
        el.setAttribute('disabled', 'true')
        el.style.opacity = '0.5'
        el.style.cursor = 'not-allowed'
      }
      break
  }
}

// 清理监听器
function cleanupListeners(el: HTMLElement) {
  const listeners = elementListeners.get(el)
  if (listeners) {
    listeners.forEach(listener => {
      offPermissionChange(listener)
      offDepartmentChange(listener)
      offLoginStatusChange(listener)
    })
    elementListeners.delete(el)
  }
}

// 导出所有指令
export default {
  permission: vPermission,
  'permission-hide': vPermissionHide,
  'permission-disable': vPermissionDisable,
  role: vRole
}
