"""
文件安全验证测试
"""
import os
import tempfile
from django.test import TestCase, override_settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from .file_security import FileSecurityValidator, SecureFileUploadHandler
from .exceptions import BusinessException

User = get_user_model()

class FileSecurityValidatorTest(TestCase):
    """文件安全验证器测试"""
    
    def setUp(self):
        self.validator = FileSecurityValidator('image')
    
    def test_valid_image_file(self):
        """测试有效的图片文件"""
        # 创建一个简单的PNG文件
        png_content = b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a' + b'\x00' * 100
        uploaded_file = SimpleUploadedFile(
            "test.png",
            png_content,
            content_type="image/png"
        )
        
        result = self.validator.validate_file(uploaded_file)
        self.assertTrue(result['is_valid'])
        self.assertEqual(result['file_info']['extension'], '.png')
    
    def test_invalid_file_extension(self):
        """测试无效的文件扩展名"""
        uploaded_file = SimpleUploadedFile(
            "test.exe",
            b"fake exe content",
            content_type="application/x-executable"
        )
        
        result = self.validator.validate_file(uploaded_file)
        self.assertFalse(result['is_valid'])
        self.assertIn('不允许的文件类型', str(result['errors']))
    
    def test_file_size_limit(self):
        """测试文件大小限制"""
        # 创建超过限制的文件
        large_content = b'x' * (11 * 1024 * 1024)  # 11MB
        uploaded_file = SimpleUploadedFile(
            "large.png",
            large_content,
            content_type="image/png"
        )
        
        result = self.validator.validate_file(uploaded_file)
        self.assertFalse(result['is_valid'])
        self.assertIn('文件大小超过限制', str(result['errors']))
    
    def test_dangerous_filename(self):
        """测试危险的文件名"""
        uploaded_file = SimpleUploadedFile(
            "../../../etc/passwd",
            b"fake content",
            content_type="text/plain"
        )
        
        result = self.validator.validate_file(uploaded_file)
        self.assertFalse(result['is_valid'])
        self.assertIn('文件名包含非法路径', str(result['errors']))
    
    def test_empty_file(self):
        """测试空文件"""
        uploaded_file = SimpleUploadedFile(
            "empty.png",
            b"",
            content_type="image/png"
        )
        
        result = self.validator.validate_file(uploaded_file)
        self.assertFalse(result['is_valid'])
        self.assertIn('文件不能为空', str(result['errors']))
    
    def test_malicious_content(self):
        """测试恶意内容"""
        malicious_content = b'<script>alert("xss")</script>'
        uploaded_file = SimpleUploadedFile(
            "malicious.txt",
            malicious_content,
            content_type="text/plain"
        )
        
        validator = FileSecurityValidator('document')
        result = validator.validate_file(uploaded_file)
        self.assertFalse(result['is_valid'])
        self.assertIn('文件包含可疑内容', str(result['errors']))

class SecureFileUploadHandlerTest(TestCase):
    """安全文件上传处理器测试"""
    
    def setUp(self):
        self.handler = SecureFileUploadHandler()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        # 清理临时文件
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @override_settings(MEDIA_ROOT=tempfile.gettempdir())
    def test_handle_upload_success(self):
        """测试成功上传文件"""
        png_content = b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a' + b'\x00' * 100
        uploaded_file = SimpleUploadedFile(
            "test.png",
            png_content,
            content_type="image/png"
        )
        
        result = self.handler.handle_upload(uploaded_file, 'image')
        
        self.assertTrue(result['is_valid'])
        self.assertIn('saved_path', result['file_info'])
        self.assertIn('safe_filename', result['file_info'])
    
    def test_generate_safe_filename(self):
        """测试生成安全文件名"""
        original_name = "test file with spaces.png"
        safe_name = self.handler._generate_safe_filename(original_name)
        
        self.assertTrue(safe_name.endswith('.png'))
        self.assertNotIn(' ', safe_name)
        self.assertTrue(len(safe_name) > len('.png'))

class FileUploadAPITest(APITestCase):
    """文件上传API测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_upload_config_api(self):
        """测试获取上传配置API"""
        url = '/api/common/upload/config/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['code'], 200)
        self.assertIn('allowed_types', data['data'])
        self.assertIn('image', data['data']['allowed_types'])
    
    @override_settings(MEDIA_ROOT=tempfile.gettempdir())
    def test_upload_file_api(self):
        """测试文件上传API"""
        png_content = b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a' + b'\x00' * 100
        uploaded_file = SimpleUploadedFile(
            "test.png",
            png_content,
            content_type="image/png"
        )
        
        url = '/api/common/upload/'
        data = {
            'file': uploaded_file,
            'file_type': 'image'
        }
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['code'], 200)
        self.assertIn('file_info', response_data['data'])
    
    def test_upload_invalid_file(self):
        """测试上传无效文件"""
        exe_file = SimpleUploadedFile(
            "malware.exe",
            b"fake exe content",
            content_type="application/x-executable"
        )
        
        url = '/api/common/upload/'
        data = {
            'file': exe_file,
            'file_type': 'image'
        }
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        response_data = response.json()
        self.assertNotEqual(response_data['code'], 200)
    
    def test_validate_file_api(self):
        """测试文件验证API"""
        png_content = b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a' + b'\x00' * 100
        uploaded_file = SimpleUploadedFile(
            "test.png",
            png_content,
            content_type="image/png"
        )
        
        url = '/api/common/upload/validate/'
        data = {
            'file': uploaded_file,
            'file_type': 'image'
        }
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['code'], 200)
        self.assertTrue(response_data['data']['is_valid'])
    
    @override_settings(MEDIA_ROOT=tempfile.gettempdir())
    def test_batch_upload_api(self):
        """测试批量上传API"""
        files = []
        for i in range(3):
            png_content = b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a' + b'\x00' * 100
            files.append(SimpleUploadedFile(
                f"test{i}.png",
                png_content,
                content_type="image/png"
            ))
        
        url = '/api/common/upload/batch/'
        data = {
            'files': files,
            'file_type': 'image',
            'max_files': 5
        }
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['code'], 200)
        self.assertEqual(response_data['data']['total_files'], 3)
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        self.client.credentials()  # 清除认证信息
        
        url = '/api/common/upload/config/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

class FileSecurityIntegrationTest(TestCase):
    """文件安全集成测试"""
    
    def test_file_type_detection(self):
        """测试文件类型检测"""
        # 测试伪装的文件（扩展名与内容不匹配）
        fake_png = SimpleUploadedFile(
            "fake.png",
            b"This is not a PNG file",
            content_type="image/png"
        )
        
        validator = FileSecurityValidator('image')
        result = validator.validate_file(fake_png)
        
        # 应该检测到文件类型不匹配
        self.assertFalse(result['is_valid'])
    
    def test_security_scan_comprehensive(self):
        """测试综合安全扫描"""
        # 创建包含多种安全问题的文件
        malicious_content = b'''
        <script>alert("xss")</script>
        javascript:void(0)
        eval(malicious_code)
        '''
        
        uploaded_file = SimpleUploadedFile(
            "malicious.txt",
            malicious_content,
            content_type="text/plain"
        )
        
        validator = FileSecurityValidator('document')
        result = validator.validate_file(uploaded_file)
        
        self.assertFalse(result['is_valid'])
        self.assertTrue(result['security_info']['has_malicious_content'])
        self.assertGreater(len(result['security_info']['suspicious_patterns']), 0)
