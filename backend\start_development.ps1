# HEIM项目开发启动脚本
# 自动检查环境并启动开发服务器

Write-Host "🚀 HEIM项目开发启动" -ForegroundColor Green
Write-Host "自动检查环境并启动开发服务器" -ForegroundColor Blue

# 1. 检查uv是否安装
Write-Host "`n📦 检查uv包管理工具..." -ForegroundColor Blue
try {
    $uvVersion = uv --version
    Write-Host "✅ uv已安装: $uvVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到uv包管理工具" -ForegroundColor Red
    Write-Host "请先安装uv: pip install uv" -ForegroundColor Yellow
    exit 1
}

# 2. 同步依赖
Write-Host "`n📚 同步项目依赖..." -ForegroundColor Blue
try {
    uv sync
    Write-Host "✅ 依赖同步完成" -ForegroundColor Green
} catch {
    Write-Host "❌ 依赖同步失败" -ForegroundColor Red
    exit 1
}

# 3. 运行环境检查
Write-Host "`n🔍 运行环境检查..." -ForegroundColor Blue
try {
    $checkResult = uv run python scripts/check_environment.py
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 环境检查未通过，请修复问题后重试" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 环境检查通过" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 环境检查脚本执行失败，继续启动..." -ForegroundColor Yellow
}

# 4. 检查数据库迁移
Write-Host "`n🗄️ 检查数据库迁移..." -ForegroundColor Blue
try {
    uv run python manage.py showmigrations --plan | Out-Null
    Write-Host "✅ 数据库连接正常" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 数据库连接问题，请检查配置" -ForegroundColor Yellow
}

# 5. 显示开发规范提醒
Write-Host "`n📋 开发规范提醒:" -ForegroundColor Yellow
Write-Host "  ✅ 使用uv管理依赖 (严禁使用pip)" -ForegroundColor White
Write-Host "  ✅ 所有沟通使用中文" -ForegroundColor White
Write-Host "  ✅ 代码注释使用中文" -ForegroundColor White
Write-Host "  ✅ 遵循pyproject.toml配置" -ForegroundColor White

# 6. 启动选项
Write-Host "`n🎯 选择启动选项:" -ForegroundColor Blue
Write-Host "1. 启动Django开发服务器" -ForegroundColor White
Write-Host "2. 启动Celery Worker" -ForegroundColor White
Write-Host "3. 运行测试" -ForegroundColor White
Write-Host "4. 创建数据库迁移" -ForegroundColor White
Write-Host "5. 执行数据库迁移" -ForegroundColor White
Write-Host "6. 仅检查环境" -ForegroundColor White

$choice = Read-Host "请选择 (1-6)"

switch ($choice) {
    "1" {
        Write-Host "`n🌐 启动Django开发服务器..." -ForegroundColor Green
        Write-Host "访问地址: http://127.0.0.1:8000" -ForegroundColor Blue
        uv run python manage.py runserver
    }
    "2" {
        Write-Host "`n⚡ 启动Celery Worker..." -ForegroundColor Green
        uv run celery -A config worker -l info
    }
    "3" {
        Write-Host "`n🧪 运行测试..." -ForegroundColor Green
        uv run python manage.py test
    }
    "4" {
        Write-Host "`n📝 创建数据库迁移..." -ForegroundColor Green
        uv run python manage.py makemigrations
    }
    "5" {
        Write-Host "`n🔄 执行数据库迁移..." -ForegroundColor Green
        uv run python manage.py migrate
    }
    "6" {
        Write-Host "`n✅ 环境检查完成" -ForegroundColor Green
    }
    default {
        Write-Host "`n❌ 无效选择" -ForegroundColor Red
    }
}

Write-Host "`n🎉 感谢使用HEIM开发环境！" -ForegroundColor Green
