[project]
name = "heim-auth-backend"
version = "0.1.0"
description = "HEIM企业管理平台用户认证与权限管理系统后端"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    {name = "HEIM Team"}
]
keywords = ["django", "authentication", "rbac", "permissions"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Framework :: Django",
    "Framework :: Django :: 4.2",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Django核心框架
    "django>=4.2,<5.0",
    "djangorestframework>=3.16.0",
    "django-cors-headers>=4.7.0",
    # JWT认证
    "djangorestframework-simplejwt>=5.5.1",
    # 数据库和模型
    "django-mptt>=0.17.0",
    "django-guardian>=3.0.3",
    # 异步任务和缓存
    "celery>=5.5.3",
    "redis>=6.2.0",
    # 配置管理
    "python-decouple>=3.8",
    # 图像处理和验证码
    "pillow>=11.3.0",
    "django-simple-captcha>=0.6.0",
    "requests>=2.32.4",
    # 报告导出
    "xlsxwriter>=3.2.0",
    "reportlab>=4.2.5",
    # 文件类型检测
    "python-magic-bin>=0.4.14",

    # 系统监控
    "psutil>=5.9.0",
]

[project.optional-dependencies]
dev = [
    "django-extensions>=3.2.0",
    "django-debug-toolbar>=4.0.0",
]

# Django项目不需要构建系统配置

[tool.uv]
dev-dependencies = [
    "django-extensions>=3.2.0",
    "django-debug-toolbar>=4.0.0",
    # 测试相关依赖
    "pytest>=8.0.0",
    "pytest-django>=4.8.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.5.0",
    "factory-boy>=3.3.0",
    "freezegun>=1.4.0",
    "responses>=0.24.0",
    "coverage>=7.4.0",
]
