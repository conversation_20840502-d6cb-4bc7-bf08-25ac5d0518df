/**
 * 角色权限管理相关类型定义
 */

// 权限类型枚举
export enum PermissionType {
  MENU = 'MENU',
  BUTTON = 'BUTTON',
  API = 'API'
}

// 数据范围枚举
export enum DataScope {
  ALL = 'ALL',
  DEPT_AND_SUB = 'DEPT_AND_SUB',
  DEPT_ONLY = 'DEPT_ONLY',
  SELF_ONLY = 'SELF_ONLY',
  CUSTOM = 'CUSTOM'
}

// 权限基础信息
export interface Permission {
  id: number
  name: string
  code: string
  permission_type: PermissionType
  parent?: number | null
  parent_name?: string
  path: string
  component: string
  icon: string
  http_method: string
  sort_order: number
  is_active: boolean
  children_count: number
  permission_path: string
  created_at: string
  updated_at: string
}

// 权限树形结构
export interface PermissionTree {
  id: number
  name: string
  code: string
  permission_type: PermissionType
  path: string
  icon: string
  sort_order: number
  is_active: boolean
  children: PermissionTree[]
}

// 角色基础信息
export interface Role {
  id: number
  name: string
  code: string
  data_scope: DataScope
  description: string
  is_active: boolean
  sort_order: number
  user_count: number
  created_at: string
  updated_at: string
}

// 角色详情（包含权限信息）
export interface RoleDetail extends Role {
  permissions_info: Permission[]
  permission_ids: number[]
}

// 用户角色关联
export interface UserRole {
  id: number
  user: number
  role: number
  department?: number | null
  user_info: UserInfo
  role_info: RoleInfo
  department_info?: DepartmentInfo | null
  created_at: string
  updated_at: string
}

// 用户信息（简化）
export interface UserInfo {
  id: number
  username: string
  nickname: string
  email?: string
  is_active: boolean
}

// 角色信息（简化）
export interface RoleInfo {
  id: number
  name: string
  code: string
  data_scope: DataScope
  is_active: boolean
}

// 部门信息（简化）
export interface DepartmentInfo {
  id: number
  name: string
  code: string
  is_active: boolean
}

// 角色创建表单
export interface RoleCreateForm {
  name: string
  code: string
  data_scope: DataScope
  description: string
  is_active: boolean
  sort_order: number
  permission_ids: number[]
}

// 角色编辑表单
export interface RoleEditForm {
  name: string
  code: string
  data_scope: DataScope
  description: string
  is_active: boolean
  sort_order: number
  permission_ids: number[]
}

// 权限创建表单
export interface PermissionCreateForm {
  name: string
  code: string
  permission_type: PermissionType
  parent?: number | null
  path: string
  component: string
  icon: string
  http_method: string
  sort_order: number
  is_active: boolean
}

// 权限编辑表单
export interface PermissionEditForm {
  name: string
  code: string
  permission_type: PermissionType
  parent?: number | null
  path: string
  component: string
  icon: string
  http_method: string
  sort_order: number
  is_active: boolean
}

// 角色分配表单
export interface RoleAssignmentForm {
  user_ids: number[]
  role_id: number
  department_id?: number | null
}

// 角色搜索参数
export interface RoleSearchParams {
  search?: string
  data_scope?: DataScope
  is_active?: boolean
  ordering?: string
  page?: number
  page_size?: number
}

// 权限搜索参数
export interface PermissionSearchParams {
  search?: string
  permission_type?: PermissionType
  parent_id?: number | null
  is_active?: boolean
  ordering?: string
  page?: number
  page_size?: number
}

// 用户角色搜索参数
export interface UserRoleSearchParams {
  user_id?: number
  role_id?: number
  department_id?: number
  page?: number
  page_size?: number
}

// 数据范围选项
export interface DataScopeOption {
  value: DataScope
  label: string
}

// 权限类型选项
export interface PermissionTypeOption {
  value: PermissionType
  label: string
}

// 角色统计信息
export interface RoleStats {
  total_roles: number
  active_roles: number
  inactive_roles: number
  total_permissions: number
  total_user_roles: number
}

// 权限统计信息
export interface PermissionStats {
  total_permissions: number
  menu_permissions: number
  button_permissions: number
  api_permissions: number
  active_permissions: number
}

// 批量操作类型
export type RoleBatchOperationType = 'activate' | 'deactivate' | 'delete'
export type PermissionBatchOperationType = 'activate' | 'deactivate' | 'delete'

// 批量操作参数
export interface RoleBatchOperationParams {
  role_ids: number[]
  operation: RoleBatchOperationType
}

export interface PermissionBatchOperationParams {
  permission_ids: number[]
  operation: PermissionBatchOperationType
}

// 角色权限
export interface RolePermissions {
  canView: boolean
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canAssignPermissions: boolean
  canAssignUsers: boolean
  canViewUsers: boolean
}

// 权限权限
export interface PermissionPermissions {
  canView: boolean
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canManageTree: boolean
}

// 角色复制表单
export interface RoleCopyForm {
  source_role_id: number
  name: string
  code: string
  description: string
  copy_permissions: boolean
}

// 权限对比结果
export interface PermissionComparison {
  role1: Role
  role2: Role
  common_permissions: Permission[]
  role1_only_permissions: Permission[]
  role2_only_permissions: Permission[]
}

// API响应类型
export interface RoleListResponse {
  results: Role[]
  count: number
  next?: string
  previous?: string
}

export interface PermissionListResponse {
  results: Permission[]
  count: number
  next?: string
  previous?: string
}

export interface UserRoleListResponse {
  results: UserRole[]
  count: number
  next?: string
  previous?: string
}

// 分页信息
export interface PaginationInfo {
  page: number
  page_size: number
  total: number
  total_pages: number
}

// 表单验证规则
export interface FormValidationRule {
  required?: boolean
  message?: string
  pattern?: RegExp
  min?: number
  max?: number
  validator?: (value: any) => boolean | string
}

// 权限树节点
export interface PermissionTreeNode {
  id: number
  name: string
  code: string
  permission_type: PermissionType
  is_active: boolean
  children?: PermissionTreeNode[]
  parent?: PermissionTreeNode | null
  checked?: boolean
  indeterminate?: boolean
  expanded?: boolean
}

// 角色使用情况
export interface RoleUsage {
  role: Role
  user_count: number
  department_count: number
  users: UserInfo[]
  departments: DepartmentInfo[]
}

// 权限使用情况
export interface PermissionUsage {
  permission: Permission
  role_count: number
  user_count: number
  roles: RoleInfo[]
}
