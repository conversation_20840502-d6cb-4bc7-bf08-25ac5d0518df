"""
用户管理功能测试
"""
import pytest
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock

from apps.users.models import UserProfile
from apps.departments.models import Department
from apps.permissions.models import Role
from apps.users.services import UserService
from tests.factories import (
    UserFactory, UserProfileFactory, DepartmentFactory, 
    RoleFactory, create_user_with_profile, create_user_with_roles_and_permissions
)

User = get_user_model()


@pytest.mark.django_db
class TestUserManagementAPI(APITestCase):
    """用户管理API测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建管理员用户
        self.admin_user, self.admin_profile = create_user_with_profile()
        self.admin_user.is_staff = True
        self.admin_user.save()
        
        # 创建普通用户
        self.normal_user, self.normal_profile = create_user_with_profile()
        
        # API端点
        self.users_url = reverse('users:user-list')
        self.user_detail_url = lambda pk: reverse('users:user-detail', kwargs={'pk': pk})
        
        # 管理员认证
        refresh = RefreshToken.for_user(self.admin_user)
        self.admin_token = str(refresh.access_token)
    
    def test_create_user_success(self):
        """测试成功创建用户"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        department = DepartmentFactory()
        role = RoleFactory()
        
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'first_name': '新',
            'last_name': '用户',
            'password': 'newpass123',
            'profile': {
                'nickname': '新用户',
                'phone': '13800000001',
                'department': department.id,
                'roles': [role.id]
            }
        }
        
        response = self.client.post(self.users_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['username'], 'newuser')
        
        # 验证用户已创建
        user = User.objects.get(username='newuser')
        self.assertIsNotNone(user)
        self.assertTrue(user.check_password('newpass123'))
        
        # 验证用户档案已创建
        profile = UserProfile.objects.get(user=user)
        self.assertEqual(profile.nickname, '新用户')
        self.assertEqual(profile.department, department)
        self.assertIn(role, profile.roles.all())
    
    def test_create_user_duplicate_username(self):
        """测试创建重复用户名的用户"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        data = {
            'username': self.normal_user.username,  # 重复的用户名
            'email': '<EMAIL>',
            'password': 'newpass123'
        }
        
        response = self.client.post(self.users_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('username', response.data)
    
    def test_get_user_list(self):
        """测试获取用户列表"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建额外的测试用户
        UserFactory.create_batch(5)
        
        response = self.client.get(self.users_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['results']), 7)  # 至少7个用户
    
    def test_get_user_detail(self):
        """测试获取用户详情"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        url = self.user_detail_url(self.normal_user.id)
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.normal_user.id)
        self.assertEqual(response.data['username'], self.normal_user.username)
    
    def test_update_user_success(self):
        """测试成功更新用户"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        url = self.user_detail_url(self.normal_user.id)
        data = {
            'first_name': '更新的',
            'last_name': '名字',
            'email': '<EMAIL>'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证更新
        self.normal_user.refresh_from_db()
        self.assertEqual(self.normal_user.first_name, '更新的')
        self.assertEqual(self.normal_user.email, '<EMAIL>')
    
    def test_delete_user_soft_delete(self):
        """测试软删除用户"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        url = self.user_detail_url(self.normal_user.id)
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # 验证软删除
        self.normal_user.refresh_from_db()
        self.assertFalse(self.normal_user.is_active)
    
    def test_restore_deleted_user(self):
        """测试恢复已删除用户"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 先软删除用户
        self.normal_user.is_active = False
        self.normal_user.save()
        
        url = reverse('users:user-restore', kwargs={'pk': self.normal_user.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证恢复
        self.normal_user.refresh_from_db()
        self.assertTrue(self.normal_user.is_active)
    
    def test_user_status_management(self):
        """测试用户状态管理"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 禁用用户
        url = reverse('users:user-disable', kwargs={'pk': self.normal_user.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.normal_user.refresh_from_db()
        self.assertFalse(self.normal_user.is_active)
        
        # 启用用户
        url = reverse('users:user-enable', kwargs={'pk': self.normal_user.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.normal_user.refresh_from_db()
        self.assertTrue(self.normal_user.is_active)
    
    def test_user_permission_verification(self):
        """测试用户权限验证"""
        # 使用普通用户尝试访问管理功能
        refresh = RefreshToken.for_user(self.normal_user)
        normal_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {normal_token}')
        
        # 尝试创建用户
        data = {
            'username': 'unauthorized',
            'email': '<EMAIL>',
            'password': 'pass123'
        }
        
        response = self.client.post(self.users_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_user_data_scope_permissions(self):
        """测试用户数据范围权限"""
        # 创建不同部门的用户
        dept1 = DepartmentFactory(name='部门1')
        dept2 = DepartmentFactory(name='部门2')
        
        user1, profile1 = create_user_with_profile()
        profile1.department = dept1
        profile1.save()
        
        user2, profile2 = create_user_with_profile()
        profile2.department = dept2
        profile2.save()
        
        # 使用部门1的用户登录
        refresh = RefreshToken.for_user(user1)
        token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 根据数据范围权限，应该只能看到本部门的用户
        response = self.client.get(self.users_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 具体的数据过滤逻辑需要根据实际的权限实现来验证


@pytest.mark.django_db
class TestUserService(TestCase):
    """用户服务测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user_service = UserService()
        self.department = DepartmentFactory()
        self.role = RoleFactory()
    
    def test_create_user_with_profile(self):
        """测试创建用户和档案"""
        user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': '测试',
            'last_name': '用户',
            'password': 'testpass123'
        }
        
        profile_data = {
            'nickname': '测试用户',
            'phone': '13800000001',
            'department_id': self.department.id,
            'role_ids': [self.role.id]
        }
        
        user, profile = self.user_service.create_user_with_profile(
            user_data, profile_data
        )
        
        self.assertIsNotNone(user)
        self.assertIsNotNone(profile)
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(profile.nickname, '测试用户')
        self.assertEqual(profile.department, self.department)
        self.assertIn(self.role, profile.roles.all())
    
    def test_update_user_profile(self):
        """测试更新用户档案"""
        user, profile = create_user_with_profile()
        
        update_data = {
            'nickname': '更新的昵称',
            'phone': '13900000001',
            'bio': '更新的个人简介'
        }
        
        updated_profile = self.user_service.update_user_profile(
            profile, update_data
        )
        
        self.assertEqual(updated_profile.nickname, '更新的昵称')
        self.assertEqual(updated_profile.phone, '13900000001')
        self.assertEqual(updated_profile.bio, '更新的个人简介')
    
    def test_assign_roles_to_user(self):
        """测试为用户分配角色"""
        user, profile = create_user_with_profile()
        roles = RoleFactory.create_batch(3)
        
        self.user_service.assign_roles_to_user(profile, [r.id for r in roles])
        
        self.assertEqual(profile.roles.count(), 3)
        for role in roles:
            self.assertIn(role, profile.roles.all())
    
    def test_remove_roles_from_user(self):
        """测试移除用户角色"""
        user, profile = create_user_with_profile()
        roles = RoleFactory.create_batch(3)
        profile.roles.set(roles)
        
        # 移除其中2个角色
        roles_to_remove = roles[:2]
        self.user_service.remove_roles_from_user(
            profile, [r.id for r in roles_to_remove]
        )
        
        self.assertEqual(profile.roles.count(), 1)
        self.assertNotIn(roles_to_remove[0], profile.roles.all())
        self.assertNotIn(roles_to_remove[1], profile.roles.all())
        self.assertIn(roles[2], profile.roles.all())
    
    def test_get_users_by_department(self):
        """测试按部门获取用户"""
        dept1 = DepartmentFactory()
        dept2 = DepartmentFactory()
        
        # 创建不同部门的用户
        users_dept1 = []
        for _ in range(3):
            user, profile = create_user_with_profile()
            profile.department = dept1
            profile.save()
            users_dept1.append(user)
        
        users_dept2 = []
        for _ in range(2):
            user, profile = create_user_with_profile()
            profile.department = dept2
            profile.save()
            users_dept2.append(user)
        
        # 获取部门1的用户
        dept1_users = self.user_service.get_users_by_department(dept1.id)
        self.assertEqual(len(dept1_users), 3)
        
        # 获取部门2的用户
        dept2_users = self.user_service.get_users_by_department(dept2.id)
        self.assertEqual(len(dept2_users), 2)
    
    def test_get_users_by_role(self):
        """测试按角色获取用户"""
        role1 = RoleFactory()
        role2 = RoleFactory()
        
        # 创建不同角色的用户
        users_role1 = []
        for _ in range(3):
            user, profile = create_user_with_profile()
            profile.roles.add(role1)
            users_role1.append(user)
        
        users_role2 = []
        for _ in range(2):
            user, profile = create_user_with_profile()
            profile.roles.add(role2)
            users_role2.append(user)
        
        # 获取角色1的用户
        role1_users = self.user_service.get_users_by_role(role1.id)
        self.assertEqual(len(role1_users), 3)
        
        # 获取角色2的用户
        role2_users = self.user_service.get_users_by_role(role2.id)
        self.assertEqual(len(role2_users), 2)
    
    def test_deactivate_user(self):
        """测试停用用户"""
        user, profile = create_user_with_profile()
        
        result = self.user_service.deactivate_user(user.id)
        
        self.assertTrue(result)
        user.refresh_from_db()
        self.assertFalse(user.is_active)
    
    def test_activate_user(self):
        """测试激活用户"""
        user, profile = create_user_with_profile()
        user.is_active = False
        user.save()
        
        result = self.user_service.activate_user(user.id)
        
        self.assertTrue(result)
        user.refresh_from_db()
        self.assertTrue(user.is_active)


@pytest.mark.django_db
class TestUserProfileModel(TestCase):
    """用户档案模型测试"""
    
    def test_profile_creation(self):
        """测试档案创建"""
        user = UserFactory()
        profile = UserProfileFactory(user=user)
        
        self.assertEqual(profile.user, user)
        self.assertIsNotNone(profile.nickname)
        self.assertTrue(profile.is_active)
    
    def test_profile_str_representation(self):
        """测试档案字符串表示"""
        profile = UserProfileFactory(nickname='测试用户')
        
        self.assertEqual(str(profile), '测试用户')
    
    def test_get_full_name(self):
        """测试获取全名"""
        user = UserFactory(first_name='张', last_name='三')
        profile = UserProfileFactory(user=user)
        
        full_name = profile.get_full_name()
        self.assertEqual(full_name, '张三')
    
    def test_get_user_permissions(self):
        """测试获取用户权限"""
        user_data = create_user_with_roles_and_permissions()
        profile = user_data['profile']
        
        permissions = profile.get_all_permissions()
        self.assertGreater(len(permissions), 0)
    
    def test_has_permission(self):
        """测试权限检查"""
        user_data = create_user_with_roles_and_permissions()
        profile = user_data['profile']
        permission = user_data['permissions'][0]
        
        has_perm = profile.has_permission(permission.code)
        self.assertTrue(has_perm)
        
        # 测试不存在的权限
        has_fake_perm = profile.has_permission('fake:permission')
        self.assertFalse(has_fake_perm)
