/**
 * 角色权限管理状态管理
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { roleApi } from '@/api/role'
import type {
  Role,
  RoleDetail,
  Permission,
  PermissionTree,
  UserRole,
  RoleSearchParams,
  PermissionSearchParams,
  UserRoleSearchParams,
  RoleCreateForm,
  RoleEditForm,
  PermissionCreateForm,
  PermissionEditForm,
  RoleStats,
  PermissionStats,
  RoleBatchOperationParams,
  PermissionBatchOperationParams,
  DataScopeOption,
  PermissionTypeOption
} from '@/types/role'

// 分页信息类型
interface PaginationInfo {
  page: number
  page_size: number
  total: number
  total_pages: number
}

export const useRoleStore = defineStore('role', () => {
  // 角色相关状态
  const roleList = ref<Role[]>([])
  const currentRole = ref<RoleDetail | null>(null)
  const roleStats = ref<RoleStats | null>(null)
  const roleLoading = ref(false)
  
  // 权限相关状态
  const permissionList = ref<Permission[]>([])
  const permissionTree = ref<PermissionTree[]>([])
  const currentPermission = ref<Permission | null>(null)
  const permissionStats = ref<PermissionStats | null>(null)
  const permissionLoading = ref(false)
  
  // 用户角色关联状态
  const userRoleList = ref<UserRole[]>([])
  const userRoleLoading = ref(false)
  
  // 搜索和分页状态
  const roleSearchParams = ref<RoleSearchParams>({
    page: 1,
    page_size: 20,
    ordering: '-created_at'
  })
  const permissionSearchParams = ref<PermissionSearchParams>({
    page: 1,
    page_size: 20,
    ordering: 'sort_order'
  })
  const userRoleSearchParams = ref<UserRoleSearchParams>({
    page: 1,
    page_size: 20
  })
  
  const rolePagination = ref<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0
  })
  const permissionPagination = ref<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0
  })
  const userRolePagination = ref<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0
  })
  
  // 选中状态
  const selectedRoleIds = ref<number[]>([])
  const selectedPermissionIds = ref<number[]>([])
  const selectedUserRoleIds = ref<number[]>([])
  
  // 选项数据
  const dataScopeOptions = ref<DataScopeOption[]>([])
  const permissionTypeOptions = ref<PermissionTypeOption[]>([])
  
  // 计算属性
  const hasSelectedRoles = computed(() => selectedRoleIds.value.length > 0)
  const selectedRolesCount = computed(() => selectedRoleIds.value.length)
  const hasSelectedPermissions = computed(() => selectedPermissionIds.value.length > 0)
  const selectedPermissionsCount = computed(() => selectedPermissionIds.value.length)
  
  // 角色管理方法
  const fetchRoleList = async (params?: RoleSearchParams) => {
    try {
      roleLoading.value = true
      
      const mergedParams = { ...roleSearchParams.value, ...params }
      roleSearchParams.value = mergedParams
      
      const response = await roleApi.getRoleList(mergedParams)
      
      if (response.code === 200) {
        roleList.value = response.data.results
        
        rolePagination.value = {
          page: mergedParams.page || 1,
          page_size: mergedParams.page_size || 20,
          total: response.data.count,
          total_pages: Math.ceil(response.data.count / (mergedParams.page_size || 20))
        }
      }
      
      return response
    } catch (error) {
      console.error('获取角色列表失败:', error)
      throw error
    } finally {
      roleLoading.value = false
    }
  }
  
  const fetchRoleDetail = async (id: number) => {
    try {
      roleLoading.value = true
      const response = await roleApi.getRoleDetail(id)
      
      if (response.code === 200) {
        currentRole.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取角色详情失败:', error)
      throw error
    } finally {
      roleLoading.value = false
    }
  }
  
  const createRole = async (data: RoleCreateForm) => {
    try {
      roleLoading.value = true
      const response = await roleApi.createRole(data)
      
      if (response.code === 200 || response.code === 1001) {
        await fetchRoleList()
      }
      
      return response
    } catch (error) {
      console.error('创建角色失败:', error)
      throw error
    } finally {
      roleLoading.value = false
    }
  }
  
  const updateRole = async (id: number, data: RoleEditForm) => {
    try {
      roleLoading.value = true
      const response = await roleApi.updateRole(id, data)
      
      if (response.code === 200) {
        const index = roleList.value.findIndex(role => role.id === id)
        if (index !== -1) {
          roleList.value[index] = { ...roleList.value[index], ...response.data }
        }
        
        if (currentRole.value?.id === id) {
          currentRole.value = { ...currentRole.value, ...response.data }
        }
      }
      
      return response
    } catch (error) {
      console.error('更新角色失败:', error)
      throw error
    } finally {
      roleLoading.value = false
    }
  }
  
  const deleteRole = async (id: number) => {
    try {
      roleLoading.value = true
      const response = await roleApi.deleteRole(id)
      
      if (response.code === 200) {
        roleList.value = roleList.value.filter(role => role.id !== id)
        rolePagination.value.total -= 1
        
        if (currentRole.value?.id === id) {
          currentRole.value = null
        }
      }
      
      return response
    } catch (error) {
      console.error('删除角色失败:', error)
      throw error
    } finally {
      roleLoading.value = false
    }
  }
  
  const toggleRoleActive = async (id: number) => {
    try {
      const response = await roleApi.toggleRoleActive(id)
      
      if (response.code === 200) {
        const index = roleList.value.findIndex(role => role.id === id)
        if (index !== -1) {
          roleList.value[index].is_active = response.data.is_active
        }
        
        if (currentRole.value?.id === id) {
          currentRole.value.is_active = response.data.is_active
        }
      }
      
      return response
    } catch (error) {
      console.error('切换角色状态失败:', error)
      throw error
    }
  }
  
  const batchOperateRoles = async (params: RoleBatchOperationParams) => {
    try {
      roleLoading.value = true
      const response = await roleApi.batchOperateRoles(params)
      
      if (response.code === 200) {
        await fetchRoleList()
        selectedRoleIds.value = []
      }
      
      return response
    } catch (error) {
      console.error('批量操作角色失败:', error)
      throw error
    } finally {
      roleLoading.value = false
    }
  }
  
  const fetchRoleStats = async () => {
    try {
      const response = await roleApi.getRoleStats()
      
      if (response.code === 200) {
        roleStats.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取角色统计失败:', error)
      throw error
    }
  }
  
  // 权限管理方法
  const fetchPermissionList = async (params?: PermissionSearchParams) => {
    try {
      permissionLoading.value = true
      
      const mergedParams = { ...permissionSearchParams.value, ...params }
      permissionSearchParams.value = mergedParams
      
      const response = await roleApi.getPermissionList(mergedParams)
      
      if (response.code === 200) {
        permissionList.value = response.data.results
        
        permissionPagination.value = {
          page: mergedParams.page || 1,
          page_size: mergedParams.page_size || 20,
          total: response.data.count,
          total_pages: Math.ceil(response.data.count / (mergedParams.page_size || 20))
        }
      }
      
      return response
    } catch (error) {
      console.error('获取权限列表失败:', error)
      throw error
    } finally {
      permissionLoading.value = false
    }
  }
  
  const fetchPermissionTree = async () => {
    try {
      permissionLoading.value = true
      const response = await roleApi.getPermissionTree()
      
      if (response.code === 200) {
        permissionTree.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取权限树失败:', error)
      throw error
    } finally {
      permissionLoading.value = false
    }
  }
  
  const fetchPermissionDetail = async (id: number) => {
    try {
      permissionLoading.value = true
      const response = await roleApi.getPermissionDetail(id)
      
      if (response.code === 200) {
        currentPermission.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取权限详情失败:', error)
      throw error
    } finally {
      permissionLoading.value = false
    }
  }
  
  const createPermission = async (data: PermissionCreateForm) => {
    try {
      permissionLoading.value = true
      const response = await roleApi.createPermission(data)
      
      if (response.code === 200 || response.code === 1001) {
        await Promise.all([
          fetchPermissionList(),
          fetchPermissionTree()
        ])
      }
      
      return response
    } catch (error) {
      console.error('创建权限失败:', error)
      throw error
    } finally {
      permissionLoading.value = false
    }
  }
  
  const updatePermission = async (id: number, data: PermissionEditForm) => {
    try {
      permissionLoading.value = true
      const response = await roleApi.updatePermission(id, data)
      
      if (response.code === 200) {
        const index = permissionList.value.findIndex(permission => permission.id === id)
        if (index !== -1) {
          permissionList.value[index] = { ...permissionList.value[index], ...response.data }
        }
        
        if (currentPermission.value?.id === id) {
          currentPermission.value = { ...currentPermission.value, ...response.data }
        }
        
        await fetchPermissionTree()
      }
      
      return response
    } catch (error) {
      console.error('更新权限失败:', error)
      throw error
    } finally {
      permissionLoading.value = false
    }
  }
  
  const deletePermission = async (id: number) => {
    try {
      permissionLoading.value = true
      const response = await roleApi.deletePermission(id)
      
      if (response.code === 200) {
        permissionList.value = permissionList.value.filter(permission => permission.id !== id)
        permissionPagination.value.total -= 1
        
        if (currentPermission.value?.id === id) {
          currentPermission.value = null
        }
        
        await fetchPermissionTree()
      }
      
      return response
    } catch (error) {
      console.error('删除权限失败:', error)
      throw error
    } finally {
      permissionLoading.value = false
    }
  }
  
  // 获取选项数据
  const fetchDataScopeOptions = async () => {
    try {
      const response = await roleApi.getDataScopeOptions()
      
      if (response.code === 200) {
        dataScopeOptions.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取数据范围选项失败:', error)
      throw error
    }
  }
  
  const fetchPermissionTypeOptions = async () => {
    try {
      const response = await roleApi.getPermissionTypeOptions()

      if (response.code === 200) {
        permissionTypeOptions.value = response.data
      }

      return response
    } catch (error) {
      console.error('获取权限类型选项失败:', error)
      throw error
    }
  }

  const fetchPermissionStats = async () => {
    try {
      const response = await roleApi.getPermissionStats()

      if (response.code === 200) {
        permissionStats.value = response.data
      }

      return response
    } catch (error) {
      console.error('获取权限统计失败:', error)
      throw error
    }
  }

  const batchOperatePermissions = async (params: PermissionBatchOperationParams) => {
    try {
      permissionLoading.value = true
      const response = await roleApi.batchOperatePermissions(params)

      if (response.code === 200) {
        await Promise.all([
          fetchPermissionList(),
          fetchPermissionTree()
        ])
        selectedPermissionIds.value = []
      }

      return response
    } catch (error) {
      console.error('批量操作权限失败:', error)
      throw error
    } finally {
      permissionLoading.value = false
    }
  }
  
  // 选择操作
  const selectRole = (roleId: number) => {
    if (!selectedRoleIds.value.includes(roleId)) {
      selectedRoleIds.value.push(roleId)
    }
  }
  
  const unselectRole = (roleId: number) => {
    const index = selectedRoleIds.value.indexOf(roleId)
    if (index > -1) {
      selectedRoleIds.value.splice(index, 1)
    }
  }
  
  const toggleRoleSelection = (roleId: number) => {
    if (selectedRoleIds.value.includes(roleId)) {
      unselectRole(roleId)
    } else {
      selectRole(roleId)
    }
  }
  
  const clearRoleSelection = () => {
    selectedRoleIds.value = []
  }
  
  const selectPermission = (permissionId: number) => {
    if (!selectedPermissionIds.value.includes(permissionId)) {
      selectedPermissionIds.value.push(permissionId)
    }
  }
  
  const unselectPermission = (permissionId: number) => {
    const index = selectedPermissionIds.value.indexOf(permissionId)
    if (index > -1) {
      selectedPermissionIds.value.splice(index, 1)
    }
  }
  
  const togglePermissionSelection = (permissionId: number) => {
    if (selectedPermissionIds.value.includes(permissionId)) {
      unselectPermission(permissionId)
    } else {
      selectPermission(permissionId)
    }
  }
  
  const clearPermissionSelection = () => {
    selectedPermissionIds.value = []
  }
  
  // 重置状态
  const resetState = () => {
    roleList.value = []
    currentRole.value = null
    roleStats.value = null
    roleLoading.value = false
    
    permissionList.value = []
    permissionTree.value = []
    currentPermission.value = null
    permissionStats.value = null
    permissionLoading.value = false
    
    userRoleList.value = []
    userRoleLoading.value = false
    
    selectedRoleIds.value = []
    selectedPermissionIds.value = []
    selectedUserRoleIds.value = []
    
    roleSearchParams.value = {
      page: 1,
      page_size: 20,
      ordering: '-created_at'
    }
    permissionSearchParams.value = {
      page: 1,
      page_size: 20,
      ordering: 'sort_order'
    }
    userRoleSearchParams.value = {
      page: 1,
      page_size: 20
    }
    
    rolePagination.value = {
      page: 1,
      page_size: 20,
      total: 0,
      total_pages: 0
    }
    permissionPagination.value = {
      page: 1,
      page_size: 20,
      total: 0,
      total_pages: 0
    }
    userRolePagination.value = {
      page: 1,
      page_size: 20,
      total: 0,
      total_pages: 0
    }
  }
  
  return {
    // 状态
    roleList,
    currentRole,
    roleStats,
    roleLoading,
    permissionList,
    permissionTree,
    currentPermission,
    permissionStats,
    permissionLoading,
    userRoleList,
    userRoleLoading,
    roleSearchParams,
    permissionSearchParams,
    userRoleSearchParams,
    rolePagination,
    permissionPagination,
    userRolePagination,
    selectedRoleIds,
    selectedPermissionIds,
    selectedUserRoleIds,
    dataScopeOptions,
    permissionTypeOptions,
    
    // 计算属性
    hasSelectedRoles,
    selectedRolesCount,
    hasSelectedPermissions,
    selectedPermissionsCount,
    
    // 方法
    fetchRoleList,
    fetchRoleDetail,
    createRole,
    updateRole,
    deleteRole,
    toggleRoleActive,
    batchOperateRoles,
    fetchRoleStats,
    fetchPermissionList,
    fetchPermissionTree,
    fetchPermissionDetail,
    createPermission,
    updatePermission,
    deletePermission,
    fetchDataScopeOptions,
    fetchPermissionTypeOptions,
    fetchPermissionStats,
    batchOperatePermissions,
    selectRole,
    unselectRole,
    toggleRoleSelection,
    clearRoleSelection,
    selectPermission,
    unselectPermission,
    togglePermissionSelection,
    clearPermissionSelection,
    resetState
  }
})
