"""
环境变量和配置管理模块
提供安全的配置管理功能
"""
import os
import json
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from decouple import config, Csv
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    # 敏感配置项（不允许通过API修改）
    SENSITIVE_KEYS = {
        'SECRET_KEY',
        'DATABASE_PASSWORD',
        'REDIS_PASSWORD',
        'JWT_SIGNING_KEY',
        'EMAIL_HOST_PASSWORD',
        'AWS_SECRET_ACCESS_KEY',
        'SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET',
        'SOCIAL_AUTH_GITHUB_SECRET',
    }
    
    # 可配置项定义
    CONFIGURABLE_ITEMS = {
        # 系统基础配置
        'SYSTEM_NAME': {
            'type': 'string',
            'default': 'HEIM企业管理平台',
            'description': '系统名称',
            'category': 'system'
        },
        'SYSTEM_VERSION': {
            'type': 'string',
            'default': '1.0.0',
            'description': '系统版本',
            'category': 'system'
        },
        'DEBUG': {
            'type': 'boolean',
            'default': False,
            'description': '调试模式',
            'category': 'system'
        },
        'ALLOWED_HOSTS': {
            'type': 'list',
            'default': ['localhost', '127.0.0.1'],
            'description': '允许的主机列表',
            'category': 'system'
        },
        
        # 安全配置
        'SESSION_TIMEOUT': {
            'type': 'integer',
            'default': 3600,
            'description': '会话超时时间（秒）',
            'category': 'security',
            'min_value': 300,
            'max_value': 86400
        },
        'MAX_LOGIN_ATTEMPTS': {
            'type': 'integer',
            'default': 5,
            'description': '最大登录尝试次数',
            'category': 'security',
            'min_value': 3,
            'max_value': 20
        },
        'ACCOUNT_LOCKOUT_DURATION': {
            'type': 'integer',
            'default': 1800,
            'description': '账户锁定时长（秒）',
            'category': 'security',
            'min_value': 300,
            'max_value': 86400
        },
        'PASSWORD_MIN_LENGTH': {
            'type': 'integer',
            'default': 8,
            'description': '密码最小长度',
            'category': 'security',
            'min_value': 6,
            'max_value': 32
        },
        'PASSWORD_REQUIRE_UPPERCASE': {
            'type': 'boolean',
            'default': True,
            'description': '密码需要大写字母',
            'category': 'security'
        },
        'PASSWORD_REQUIRE_LOWERCASE': {
            'type': 'boolean',
            'default': True,
            'description': '密码需要小写字母',
            'category': 'security'
        },
        'PASSWORD_REQUIRE_NUMBERS': {
            'type': 'boolean',
            'default': True,
            'description': '密码需要数字',
            'category': 'security'
        },
        'PASSWORD_REQUIRE_SYMBOLS': {
            'type': 'boolean',
            'default': False,
            'description': '密码需要特殊字符',
            'category': 'security'
        },
        'PASSWORD_EXPIRY_DAYS': {
            'type': 'integer',
            'default': 90,
            'description': '密码过期天数（0表示不过期）',
            'category': 'security',
            'min_value': 0,
            'max_value': 365
        },
        
        # 文件上传配置
        'MAX_FILE_SIZE_MB': {
            'type': 'integer',
            'default': 10,
            'description': '最大文件大小（MB）',
            'category': 'upload',
            'min_value': 1,
            'max_value': 100
        },
        'ALLOWED_FILE_EXTENSIONS': {
            'type': 'list',
            'default': ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'],
            'description': '允许的文件扩展名',
            'category': 'upload'
        },
        
        # 邮件配置
        'EMAIL_HOST': {
            'type': 'string',
            'default': 'smtp.gmail.com',
            'description': '邮件服务器地址',
            'category': 'email'
        },
        'EMAIL_PORT': {
            'type': 'integer',
            'default': 587,
            'description': '邮件服务器端口',
            'category': 'email',
            'min_value': 1,
            'max_value': 65535
        },
        'EMAIL_USE_TLS': {
            'type': 'boolean',
            'default': True,
            'description': '使用TLS加密',
            'category': 'email'
        },
        'EMAIL_FROM': {
            'type': 'string',
            'default': '<EMAIL>',
            'description': '发件人邮箱',
            'category': 'email'
        },
        
        # 日志配置
        'LOG_LEVEL': {
            'type': 'choice',
            'default': 'INFO',
            'choices': ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
            'description': '日志级别',
            'category': 'logging'
        },
        'LOG_RETENTION_DAYS': {
            'type': 'integer',
            'default': 30,
            'description': '日志保留天数',
            'category': 'logging',
            'min_value': 1,
            'max_value': 365
        },
        'AUDIT_LOG_ENABLED': {
            'type': 'boolean',
            'default': True,
            'description': '启用审计日志',
            'category': 'logging'
        },
    }
    
    def __init__(self):
        self.cache_prefix = 'config_manager'
        self.cache_timeout = 300  # 5分钟缓存
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        # 先从缓存获取
        cache_key = f"{self.cache_prefix}:{key}"
        cached_value = cache.get(cache_key)
        if cached_value is not None:
            return cached_value
        
        # 从环境变量获取
        if key in self.CONFIGURABLE_ITEMS:
            config_def = self.CONFIGURABLE_ITEMS[key]
            env_value = self._get_env_value(key, config_def)
            
            # 缓存结果
            cache.set(cache_key, env_value, self.cache_timeout)
            return env_value
        
        # 使用默认值
        return default
    
    def set_config(self, key: str, value: Any, user_id: Optional[int] = None) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            user_id: 操作用户ID
            
        Returns:
            是否设置成功
        """
        try:
            # 检查是否为敏感配置
            if key in self.SENSITIVE_KEYS:
                logger.warning(f"尝试修改敏感配置: {key}")
                return False
            
            # 检查配置项是否存在
            if key not in self.CONFIGURABLE_ITEMS:
                logger.warning(f"未知的配置项: {key}")
                return False
            
            # 验证配置值
            config_def = self.CONFIGURABLE_ITEMS[key]
            if not self._validate_config_value(value, config_def):
                logger.warning(f"配置值验证失败: {key}={value}")
                return False
            
            # 保存到环境变量文件
            self._save_to_env_file(key, value)
            
            # 清除缓存
            cache_key = f"{self.cache_prefix}:{key}"
            cache.delete(cache_key)
            
            # 记录配置变更日志
            self._log_config_change(key, value, user_id)
            
            logger.info(f"配置更新成功: {key}")
            return True
            
        except Exception as e:
            logger.error(f"设置配置失败: {key}={value}, 错误: {e}")
            return False
    
    def get_all_configs(self, category: Optional[str] = None) -> Dict[str, Any]:
        """
        获取所有配置
        
        Args:
            category: 配置分类
            
        Returns:
            配置字典
        """
        configs = {}
        
        for key, config_def in self.CONFIGURABLE_ITEMS.items():
            if category and config_def.get('category') != category:
                continue
                
            configs[key] = {
                'value': self.get_config(key),
                'definition': config_def
            }
        
        return configs
    
    def get_categories(self) -> List[str]:
        """获取所有配置分类"""
        categories = set()
        for config_def in self.CONFIGURABLE_ITEMS.values():
            if 'category' in config_def:
                categories.add(config_def['category'])
        return sorted(list(categories))
    
    def reset_config(self, key: str, user_id: Optional[int] = None) -> bool:
        """
        重置配置为默认值
        
        Args:
            key: 配置键
            user_id: 操作用户ID
            
        Returns:
            是否重置成功
        """
        if key not in self.CONFIGURABLE_ITEMS:
            return False
        
        default_value = self.CONFIGURABLE_ITEMS[key]['default']
        return self.set_config(key, default_value, user_id)
    
    def validate_all_configs(self) -> Dict[str, List[str]]:
        """
        验证所有配置
        
        Returns:
            验证结果，包含错误信息
        """
        errors = {}
        
        for key, config_def in self.CONFIGURABLE_ITEMS.items():
            try:
                value = self.get_config(key)
                if not self._validate_config_value(value, config_def):
                    errors[key] = [f"配置值 {value} 不符合要求"]
            except Exception as e:
                errors[key] = [f"配置验证异常: {str(e)}"]
        
        return errors
    
    def _get_env_value(self, key: str, config_def: Dict[str, Any]) -> Any:
        """从环境变量获取配置值"""
        config_type = config_def['type']
        default_value = config_def['default']
        
        try:
            if config_type == 'boolean':
                return config(key, default=default_value, cast=bool)
            elif config_type == 'integer':
                return config(key, default=default_value, cast=int)
            elif config_type == 'list':
                return config(key, default=default_value, cast=Csv())
            else:  # string, choice
                return config(key, default=default_value)
        except Exception as e:
            logger.warning(f"获取环境变量失败: {key}, 使用默认值: {default_value}, 错误: {e}")
            return default_value
    
    def _validate_config_value(self, value: Any, config_def: Dict[str, Any]) -> bool:
        """验证配置值"""
        config_type = config_def['type']
        
        try:
            # 类型验证
            if config_type == 'boolean' and not isinstance(value, bool):
                return False
            elif config_type == 'integer' and not isinstance(value, int):
                return False
            elif config_type == 'list' and not isinstance(value, list):
                return False
            elif config_type in ['string', 'choice'] and not isinstance(value, str):
                return False
            
            # 范围验证
            if config_type == 'integer':
                min_value = config_def.get('min_value')
                max_value = config_def.get('max_value')
                if min_value is not None and value < min_value:
                    return False
                if max_value is not None and value > max_value:
                    return False
            
            # 选择项验证
            if config_type == 'choice':
                choices = config_def.get('choices', [])
                if choices and value not in choices:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _save_to_env_file(self, key: str, value: Any):
        """保存配置到环境变量文件"""
        env_file_path = os.path.join(settings.BASE_DIR, '.env')
        
        # 读取现有内容
        env_content = {}
        if os.path.exists(env_file_path):
            with open(env_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and '=' in line and not line.startswith('#'):
                        k, v = line.split('=', 1)
                        env_content[k.strip()] = v.strip()
        
        # 更新配置
        if isinstance(value, list):
            env_content[key] = ','.join(str(v) for v in value)
        elif isinstance(value, bool):
            env_content[key] = 'True' if value else 'False'
        else:
            env_content[key] = str(value)
        
        # 写回文件
        with open(env_file_path, 'w', encoding='utf-8') as f:
            f.write("# HEIM系统配置文件\n")
            f.write(f"# 最后更新: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for k, v in sorted(env_content.items()):
                f.write(f"{k}={v}\n")
    
    def _log_config_change(self, key: str, value: Any, user_id: Optional[int]):
        """记录配置变更日志"""
        try:
            from apps.audit.models import OperationLog
            
            OperationLog.objects.create(
                user_id=user_id,
                operation_type='UPDATE',
                operation_desc=f"修改系统配置: {key}",
                method='CONFIG',
                path=f'/config/{key}',
                ip_address='127.0.0.1',  # 系统内部操作
                user_agent='ConfigManager',
                status_code=200,
                response_time=0,
                request_data=json.dumps({'key': key, 'value': value}),
                response_data=json.dumps({'success': True})
            )
        except Exception as e:
            logger.warning(f"记录配置变更日志失败: {e}")

# 全局配置管理器实例
config_manager = ConfigManager()
