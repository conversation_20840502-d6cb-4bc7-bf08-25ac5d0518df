"""
用户管理模块 - URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import UserViewSet

app_name = 'users'

# 创建路由器
router = DefaultRouter()
router.register(r'users', UserViewSet, basename='user')

# URL模式
urlpatterns = [
    # 用户管理API
    path('', include(router.urls)),
]

# 可用的API端点：
# GET    /api/users/                    - 获取用户列表（支持搜索、过滤、分页）
# POST   /api/users/                    - 创建新用户
# GET    /api/users/{id}/               - 获取用户详情
# PUT    /api/users/{id}/               - 更新用户信息（完整更新）
# PATCH  /api/users/{id}/               - 更新用户信息（部分更新）
# DELETE /api/users/{id}/               - 软删除用户
# 
# GET    /api/users/profile/            - 获取当前用户信息
# PUT    /api/users/update_profile/     - 更新当前用户个人资料
# PATCH  /api/users/update_profile/     - 更新当前用户个人资料（部分更新）
# 
# POST   /api/users/{id}/reset_password/    - 重置用户密码（管理员功能）
# POST   /api/users/change_password/        - 修改当前用户密码
# POST   /api/users/{id}/restore/           - 恢复已删除的用户
# POST   /api/users/{id}/toggle_active/     - 切换用户激活状态
#
# 查询参数说明：
# - search: 搜索关键词（用户名、昵称、邮箱、手机号）
# - is_active: 过滤激活状态 (true/false)
# - is_staff: 过滤员工状态 (true/false)
# - is_superuser: 过滤超级用户状态 (true/false)
# - created_start: 创建时间开始日期 (YYYY-MM-DD)
# - created_end: 创建时间结束日期 (YYYY-MM-DD)
# - ordering: 排序字段 (默认: -created_at)
# - page: 页码
# - page_size: 每页数量
