"""
清理审计日志的Django管理命令
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.utils import timezone
from apps.audit.models import OperationLog
from apps.audit.tasks import (
    cleanup_old_operation_logs,
    cleanup_large_operation_logs,
    archive_old_operation_logs,
    comprehensive_audit_cleanup
)


class Command(BaseCommand):
    help = '清理审计日志数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=None,
            help='清理多少天前的日志（默认从配置读取）'
        )
        
        parser.add_argument(
            '--max-records',
            type=int,
            default=None,
            help='最大保留记录数（默认从配置读取）'
        )
        
        parser.add_argument(
            '--archive',
            action='store_true',
            help='归档旧日志而不是删除'
        )
        
        parser.add_argument(
            '--archive-days',
            type=int,
            default=None,
            help='归档多少天前的日志（默认180天）'
        )
        
        parser.add_argument(
            '--archive-format',
            choices=['json', 'csv'],
            default='json',
            help='归档格式（默认json）'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行，不实际删除数据'
        )
        
        parser.add_argument(
            '--async',
            action='store_true',
            help='使用异步任务执行（需要Celery）'
        )
        
        parser.add_argument(
            '--comprehensive',
            action='store_true',
            help='执行综合清理（包含所有清理策略）'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('开始清理审计日志...')
        )
        
        # 显示当前状态
        current_count = OperationLog.objects.count()
        self.stdout.write(f'当前日志记录数: {current_count}')
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('试运行模式，不会实际删除数据')
            )
        
        try:
            if options['comprehensive']:
                self._handle_comprehensive_cleanup(options)
            elif options['archive']:
                self._handle_archive(options)
            else:
                self._handle_cleanup(options)
                
        except Exception as e:
            raise CommandError(f'清理失败: {str(e)}')

    def _handle_comprehensive_cleanup(self, options):
        """处理综合清理"""
        if options['async']:
            # 异步执行
            from apps.audit.tasks import comprehensive_audit_cleanup
            result = comprehensive_audit_cleanup.delay()
            self.stdout.write(
                self.style.SUCCESS(f'已提交综合清理任务: {result.id}')
            )
        else:
            # 同步执行
            self._cleanup_by_time(options)
            self._cleanup_by_count(options)
            self.stdout.write(
                self.style.SUCCESS('综合清理完成')
            )

    def _handle_archive(self, options):
        """处理归档"""
        days = options['archive_days'] or getattr(settings, 'AUDIT_LOG_ARCHIVE_DAYS', 180)
        archive_format = options['archive_format']
        
        if options['dry_run']:
            # 计算要归档的记录数
            archive_date = timezone.now() - timezone.timedelta(days=days)
            count = OperationLog.objects.filter(created_at__lt=archive_date).count()
            self.stdout.write(f'将归档 {count} 条记录（{days}天前的日志）')
            return
        
        if options['async']:
            # 异步归档
            result = archive_old_operation_logs.delay(days, archive_format)
            self.stdout.write(
                self.style.SUCCESS(f'已提交归档任务: {result.id}')
            )
        else:
            # 同步归档
            from apps.audit.tasks import archive_old_operation_logs
            result = archive_old_operation_logs(days, archive_format)
            if result['status'] == 'success':
                self.stdout.write(
                    self.style.SUCCESS(
                        f'归档完成: {result["archived_count"]} 条记录已归档到 {result["archive_file"]}'
                    )
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'归档失败: {result.get("error", "未知错误")}')
                )

    def _handle_cleanup(self, options):
        """处理清理"""
        if options['async']:
            # 异步清理
            if options['days'] is not None:
                result = cleanup_old_operation_logs.delay(options['days'])
                self.stdout.write(
                    self.style.SUCCESS(f'已提交按时间清理任务: {result.id}')
                )
            
            if options['max_records'] is not None:
                result = cleanup_large_operation_logs.delay(options['max_records'])
                self.stdout.write(
                    self.style.SUCCESS(f'已提交按数量清理任务: {result.id}')
                )
        else:
            # 同步清理
            if options['days'] is not None:
                self._cleanup_by_time(options)
            
            if options['max_records'] is not None:
                self._cleanup_by_count(options)
            
            # 如果没有指定参数，使用默认的时间清理
            if options['days'] is None and options['max_records'] is None:
                self._cleanup_by_time(options)

    def _cleanup_by_time(self, options):
        """按时间清理"""
        days = options['days'] or getattr(settings, 'AUDIT_LOG_RETENTION_DAYS', 90)
        
        if options['dry_run']:
            cutoff_date = timezone.now() - timezone.timedelta(days=days)
            count = OperationLog.objects.filter(created_at__lt=cutoff_date).count()
            self.stdout.write(f'将删除 {count} 条记录（{days}天前的日志）')
            return
        
        deleted_count, _ = OperationLog.cleanup_old_logs(days)
        self.stdout.write(
            self.style.SUCCESS(f'按时间清理完成: 删除了 {deleted_count} 条记录')
        )

    def _cleanup_by_count(self, options):
        """按数量清理"""
        max_records = options['max_records'] or getattr(settings, 'AUDIT_LOG_MAX_RECORDS', 1000000)
        current_count = OperationLog.objects.count()
        
        if current_count <= max_records:
            self.stdout.write(f'当前记录数 {current_count} 未超过限制 {max_records}，无需清理')
            return
        
        delete_count = current_count - max_records
        
        if options['dry_run']:
            self.stdout.write(f'将删除 {delete_count} 条最旧记录')
            return
        
        # 删除最旧的记录
        old_logs = OperationLog.objects.order_by('created_at')[:delete_count]
        old_log_ids = list(old_logs.values_list('id', flat=True))
        deleted_count, _ = OperationLog.objects.filter(id__in=old_log_ids).delete()
        
        self.stdout.write(
            self.style.SUCCESS(f'按数量清理完成: 删除了 {deleted_count} 条记录')
        )
