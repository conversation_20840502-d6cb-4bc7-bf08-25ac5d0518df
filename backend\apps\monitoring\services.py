"""
系统监控服务
"""
import psutil
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from celery import current_app as celery_app

from .models import SystemMetrics, ApplicationMetrics, ServiceHealthCheck, AlertRule, AlertRecord
from apps.audit.models import OperationLog
from apps.authentication.models import UserSession

logger = logging.getLogger(__name__)


class SystemMonitorService:
    """系统监控服务"""
    
    def __init__(self):
        self.cache_timeout = 60  # 缓存超时时间（秒）
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统性能指标"""
        try:
            # CPU信息
            cpu_usage = psutil.cpu_percent(interval=1)
            cpu_cores = psutil.cpu_count()
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_total = memory.total
            memory_used = memory.used
            memory_usage = memory.percent
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_total = disk.total
            disk_used = disk.used
            disk_usage = (disk_used / disk_total) * 100
            
            # 网络信息
            network = psutil.net_io_counters()
            network_bytes_sent = network.bytes_sent
            network_bytes_recv = network.bytes_recv
            
            # 系统负载（仅Linux/Unix系统）
            load_average_1m = None
            load_average_5m = None
            load_average_15m = None
            try:
                load_avg = psutil.getloadavg()
                load_average_1m = load_avg[0]
                load_average_5m = load_avg[1]
                load_average_15m = load_avg[2]
            except (AttributeError, OSError):
                # Windows系统不支持getloadavg
                pass
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 创建系统指标记录
            metrics = SystemMetrics.objects.create(
                cpu_usage=cpu_usage,
                cpu_cores=cpu_cores,
                memory_total=memory_total,
                memory_used=memory_used,
                memory_usage=memory_usage,
                disk_total=disk_total,
                disk_used=disk_used,
                disk_usage=disk_usage,
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv,
                load_average_1m=load_average_1m,
                load_average_5m=load_average_5m,
                load_average_15m=load_average_15m,
                process_count=process_count,
            )
            
            logger.info(f"系统指标收集完成: CPU {cpu_usage}%, 内存 {memory_usage}%, 磁盘 {disk_usage:.1f}%")
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            raise
    
    def collect_application_metrics(self) -> ApplicationMetrics:
        """收集应用性能指标"""
        try:
            # 计算时间范围（最近1小时）
            end_time = timezone.now()
            start_time = end_time - timedelta(hours=1)
            
            # 请求统计
            logs = OperationLog.objects.filter(created_at__range=[start_time, end_time])
            total_requests = logs.count()
            successful_requests = logs.filter(status_code__lt=400).count()
            failed_requests = total_requests - successful_requests
            
            # 响应时间统计
            response_times = []
            for log in logs.filter(response_time__isnull=False):
                response_times.append(log.response_time)
            
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
                min_response_time = min(response_times)
                max_response_time = max(response_times)
                
                # 计算百分位数
                sorted_times = sorted(response_times)
                p95_index = int(len(sorted_times) * 0.95)
                p99_index = int(len(sorted_times) * 0.99)
                p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else max_response_time
                p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else max_response_time
            else:
                avg_response_time = min_response_time = max_response_time = 0
                p95_response_time = p99_response_time = 0
            
            # 错误率
            error_rate = (failed_requests / total_requests * 100) if total_requests > 0 else 0
            
            # 活跃用户数（最近1小时有活动的用户）
            active_users = UserSession.objects.filter(
                last_activity__gte=start_time,
                is_active=True
            ).values('user').distinct().count()
            
            # 数据库连接数
            db_connections = self._get_database_connections()
            
            # 缓存命中率
            cache_hit_rate = self._get_cache_hit_rate()
            
            # 队列任务数
            queue_stats = self._get_queue_stats()
            
            # 创建应用指标记录
            metrics = ApplicationMetrics.objects.create(
                total_requests=total_requests,
                successful_requests=successful_requests,
                failed_requests=failed_requests,
                avg_response_time=avg_response_time,
                min_response_time=min_response_time,
                max_response_time=max_response_time,
                p95_response_time=p95_response_time,
                p99_response_time=p99_response_time,
                error_rate=error_rate,
                active_users=active_users,
                db_connections=db_connections,
                cache_hit_rate=cache_hit_rate,
                queue_pending_tasks=queue_stats.get('pending', 0),
                queue_failed_tasks=queue_stats.get('failed', 0),
            )
            
            logger.info(f"应用指标收集完成: 请求 {total_requests}, 错误率 {error_rate:.2f}%, 活跃用户 {active_users}")
            return metrics
            
        except Exception as e:
            logger.error(f"收集应用指标失败: {e}")
            raise
    
    def _get_database_connections(self) -> int:
        """获取数据库连接数"""
        try:
            with connection.cursor() as cursor:
                # MySQL查询
                if 'mysql' in settings.DATABASES['default']['ENGINE']:
                    cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                    result = cursor.fetchone()
                    return int(result[1]) if result else 0
                # PostgreSQL查询
                elif 'postgresql' in settings.DATABASES['default']['ENGINE']:
                    cursor.execute("SELECT count(*) FROM pg_stat_activity")
                    result = cursor.fetchone()
                    return int(result[0]) if result else 0
                else:
                    return 0
        except Exception as e:
            logger.warning(f"获取数据库连接数失败: {e}")
            return 0
    
    def _get_cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        try:
            # 这里需要根据实际使用的缓存系统来实现
            # Redis示例
            if hasattr(cache, '_cache') and hasattr(cache._cache, '_client'):
                client = cache._cache._client
                info = client.info()
                hits = info.get('keyspace_hits', 0)
                misses = info.get('keyspace_misses', 0)
                total = hits + misses
                return (hits / total * 100) if total > 0 else 0
            return 0
        except Exception as e:
            logger.warning(f"获取缓存命中率失败: {e}")
            return 0
    
    def _get_queue_stats(self) -> Dict[str, int]:
        """获取队列统计信息"""
        try:
            # Celery队列统计
            inspect = celery_app.control.inspect()
            
            # 获取待处理任务
            active_tasks = inspect.active()
            scheduled_tasks = inspect.scheduled()
            reserved_tasks = inspect.reserved()
            
            pending_count = 0
            if active_tasks:
                for worker, tasks in active_tasks.items():
                    pending_count += len(tasks)
            if scheduled_tasks:
                for worker, tasks in scheduled_tasks.items():
                    pending_count += len(tasks)
            if reserved_tasks:
                for worker, tasks in reserved_tasks.items():
                    pending_count += len(tasks)
            
            # 获取失败任务数（这里简化处理，实际可能需要更复杂的逻辑）
            failed_count = 0
            
            return {
                'pending': pending_count,
                'failed': failed_count
            }
        except Exception as e:
            logger.warning(f"获取队列统计失败: {e}")
            return {'pending': 0, 'failed': 0}


class HealthCheckService:
    """健康检查服务"""
    
    def __init__(self):
        self.timeout = 5  # 检查超时时间（秒）
    
    def check_all_services(self) -> List[ServiceHealthCheck]:
        """检查所有服务健康状态"""
        results = []
        
        # 检查数据库
        results.append(self.check_database())
        
        # 检查缓存
        results.append(self.check_cache())
        
        # 检查消息队列
        results.append(self.check_queue())
        
        # 检查存储
        results.append(self.check_storage())
        
        return results
    
    def check_database(self) -> ServiceHealthCheck:
        """检查数据库连接"""
        start_time = time.time()
        
        try:
            # 执行简单查询测试连接
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealthCheck.objects.create(
                service_name='数据库',
                service_type='database',
                status='healthy',
                response_time=response_time,
                details={
                    'engine': settings.DATABASES['default']['ENGINE'],
                    'host': settings.DATABASES['default'].get('HOST', 'localhost'),
                    'port': settings.DATABASES['default'].get('PORT', ''),
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealthCheck.objects.create(
                service_name='数据库',
                service_type='database',
                status='critical',
                response_time=response_time,
                error_message=str(e),
                details={'error': str(e)}
            )
    
    def check_cache(self) -> ServiceHealthCheck:
        """检查缓存服务"""
        start_time = time.time()
        
        try:
            # 测试缓存读写
            test_key = 'health_check_test'
            test_value = 'test_value'
            
            cache.set(test_key, test_value, timeout=60)
            retrieved_value = cache.get(test_key)
            
            if retrieved_value == test_value:
                cache.delete(test_key)
                response_time = (time.time() - start_time) * 1000
                
                return ServiceHealthCheck.objects.create(
                    service_name='缓存服务',
                    service_type='cache',
                    status='healthy',
                    response_time=response_time,
                    details={'backend': settings.CACHES['default']['BACKEND']}
                )
            else:
                raise Exception("缓存读写测试失败")
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealthCheck.objects.create(
                service_name='缓存服务',
                service_type='cache',
                status='critical',
                response_time=response_time,
                error_message=str(e),
                details={'error': str(e)}
            )
    
    def check_queue(self) -> ServiceHealthCheck:
        """检查消息队列"""
        start_time = time.time()
        
        try:
            # 检查Celery连接
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            
            if stats:
                response_time = (time.time() - start_time) * 1000
                
                return ServiceHealthCheck.objects.create(
                    service_name='消息队列',
                    service_type='queue',
                    status='healthy',
                    response_time=response_time,
                    details={
                        'broker': celery_app.conf.broker_url,
                        'workers': len(stats)
                    }
                )
            else:
                raise Exception("无法获取Celery统计信息")
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealthCheck.objects.create(
                service_name='消息队列',
                service_type='queue',
                status='critical',
                response_time=response_time,
                error_message=str(e),
                details={'error': str(e)}
            )
    
    def check_storage(self) -> ServiceHealthCheck:
        """检查存储服务"""
        start_time = time.time()
        
        try:
            # 检查磁盘空间
            disk_usage = psutil.disk_usage('/')
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            
            # 根据磁盘使用率确定状态
            if usage_percent < 80:
                status = 'healthy'
            elif usage_percent < 90:
                status = 'warning'
            else:
                status = 'critical'
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealthCheck.objects.create(
                service_name='存储服务',
                service_type='storage',
                status=status,
                response_time=response_time,
                details={
                    'total_space': disk_usage.total,
                    'used_space': disk_usage.used,
                    'free_space': disk_usage.free,
                    'usage_percent': usage_percent
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealthCheck.objects.create(
                service_name='存储服务',
                service_type='storage',
                status='critical',
                response_time=response_time,
                error_message=str(e),
                details={'error': str(e)}
            )


class AlertService:
    """告警服务"""
    
    def __init__(self):
        self.check_interval = 60  # 检查间隔（秒）
    
    def check_alert_rules(self):
        """检查所有告警规则"""
        active_rules = AlertRule.objects.filter(is_active=True)
        
        for rule in active_rules:
            try:
                self._check_single_rule(rule)
            except Exception as e:
                logger.error(f"检查告警规则失败 {rule.name}: {e}")
    
    def _check_single_rule(self, rule: AlertRule):
        """检查单个告警规则"""
        # 获取最新的指标值
        metric_value = self._get_metric_value(rule.metric_type)
        
        if metric_value is None:
            return
        
        # 检查是否满足告警条件
        is_triggered = self._evaluate_condition(metric_value, rule.operator, rule.threshold)
        
        if is_triggered:
            # 检查是否已经有未解决的告警
            existing_alert = AlertRecord.objects.filter(
                rule=rule,
                status__in=['triggered', 'acknowledged']
            ).first()
            
            if not existing_alert:
                # 创建新的告警记录
                alert = AlertRecord.objects.create(
                    rule=rule,
                    metric_value=metric_value,
                    message=f"{rule.get_metric_type_display()} {rule.get_operator_display()} {rule.threshold}，当前值: {metric_value}",
                    details={
                        'metric_type': rule.metric_type,
                        'threshold': rule.threshold,
                        'actual_value': metric_value,
                        'operator': rule.operator
                    }
                )
                
                # 发送告警通知
                self._send_alert_notification(alert)
                
                logger.warning(f"触发告警: {rule.name}, 当前值: {metric_value}")
    
    def _get_metric_value(self, metric_type: str) -> Optional[float]:
        """获取指标值"""
        try:
            if metric_type in ['cpu_usage', 'memory_usage', 'disk_usage']:
                # 系统指标
                latest_metrics = SystemMetrics.objects.first()
                if latest_metrics:
                    return getattr(latest_metrics, metric_type)
            
            elif metric_type in ['error_rate', 'response_time', 'active_users', 'queue_tasks']:
                # 应用指标
                latest_metrics = ApplicationMetrics.objects.first()
                if latest_metrics:
                    if metric_type == 'response_time':
                        return latest_metrics.avg_response_time
                    elif metric_type == 'queue_tasks':
                        return latest_metrics.queue_pending_tasks
                    else:
                        return getattr(latest_metrics, metric_type)
            
            return None
            
        except Exception as e:
            logger.error(f"获取指标值失败 {metric_type}: {e}")
            return None
    
    def _evaluate_condition(self, value: float, operator: str, threshold: float) -> bool:
        """评估告警条件"""
        if operator == 'gt':
            return value > threshold
        elif operator == 'gte':
            return value >= threshold
        elif operator == 'lt':
            return value < threshold
        elif operator == 'lte':
            return value <= threshold
        elif operator == 'eq':
            return value == threshold
        elif operator == 'ne':
            return value != threshold
        else:
            return False
    
    def _send_alert_notification(self, alert: AlertRecord):
        """发送告警通知"""
        try:
            # 这里可以集成邮件、短信、钉钉等通知方式
            # 暂时只记录日志
            logger.critical(f"告警通知: {alert.rule.name} - {alert.message}")
            
            # TODO: 实现具体的通知逻辑
            # - 邮件通知
            # - Webhook通知
            # - 短信通知
            
        except Exception as e:
            logger.error(f"发送告警通知失败: {e}")


# 全局服务实例
system_monitor = SystemMonitorService()
health_checker = HealthCheckService()
alert_service = AlertService()
