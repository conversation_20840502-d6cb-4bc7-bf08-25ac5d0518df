"""
Celery配置文件
"""
import os
from celery import Celery
from django.conf import settings

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

# 创建Celery应用
app = Celery('heim_auth')

# 使用Django设置配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

# 定时任务配置
app.conf.beat_schedule = {
    # 每天凌晨2点清理过期操作日志
    'cleanup-old-operation-logs': {
        'task': 'apps.audit.tasks.cleanup_old_operation_logs',
        'schedule': 60.0 * 60.0 * 24.0,  # 24小时
        'options': {'queue': 'cleanup'},
    },
    
    # 每周日凌晨3点执行综合清理
    'comprehensive-audit-cleanup': {
        'task': 'apps.audit.tasks.comprehensive_audit_cleanup',
        'schedule': 60.0 * 60.0 * 24.0 * 7.0,  # 7天
        'options': {'queue': 'cleanup'},
    },
    
    # 每小时生成统计信息
    'generate-audit-statistics': {
        'task': 'apps.audit.tasks.generate_audit_statistics',
        'schedule': 60.0 * 60.0,  # 1小时
        'options': {'queue': 'stats'},
    },
    
    # 每天凌晨1点清理过期验证码
    'cleanup-expired-captcha': {
        'task': 'apps.authentication.tasks.cleanup_expired_captcha',
        'schedule': 60.0 * 60.0 * 24.0,  # 24小时
        'options': {'queue': 'cleanup'},
    },
    
    # 每30分钟清理过期会话
    'cleanup-expired-sessions': {
        'task': 'apps.authentication.tasks.cleanup_expired_sessions',
        'schedule': 60.0 * 30.0,  # 30分钟
        'options': {'queue': 'cleanup'},
    },
    
    # 每小时检查可疑登录尝试
    'check-suspicious-login-attempts': {
        'task': 'apps.authentication.tasks.check_suspicious_login_attempts',
        'schedule': 60.0 * 60.0,  # 1小时
        'options': {'queue': 'security'},
    },
    
    # 每月第一天凌晨4点归档旧日志
    'archive-old-operation-logs': {
        'task': 'apps.audit.tasks.archive_old_operation_logs',
        'schedule': 60.0 * 60.0 * 24.0 * 30.0,  # 30天
        'options': {'queue': 'archive'},
    },

    # 系统监控相关定时任务
    # 收集系统指标（每分钟执行）
    'collect-system-metrics': {
        'task': 'apps.monitoring.tasks.collect_system_metrics_task',
        'schedule': 60.0,  # 每60秒执行一次
        'options': {'queue': 'monitoring'},
    },

    # 健康检查（每5分钟执行）
    'health-check': {
        'task': 'apps.monitoring.tasks.health_check_task',
        'schedule': 300.0,  # 每300秒执行一次
        'options': {'queue': 'monitoring'},
    },

    # 检查告警规则（每分钟执行）
    'check-alert-rules': {
        'task': 'apps.monitoring.tasks.check_alert_rules_task',
        'schedule': 60.0,  # 每60秒执行一次
        'options': {'queue': 'monitoring'},
    },

    # 清理过期监控数据（每天凌晨3点执行）
    'cleanup-old-metrics': {
        'task': 'apps.monitoring.tasks.cleanup_old_metrics_task',
        'schedule': 60.0 * 60.0 * 24.0,  # 24小时
        'options': {'queue': 'cleanup'},
    },

    # 生成监控报告（每天早上8点执行）
    'generate-monitoring-report': {
        'task': 'apps.monitoring.tasks.generate_monitoring_report_task',
        'schedule': 60.0 * 60.0 * 24.0,  # 24小时
        'options': {'queue': 'stats'},
    },
}

# 队列配置
app.conf.task_routes = {
    'apps.audit.tasks.*': {'queue': 'audit'},
    'apps.authentication.tasks.*': {'queue': 'auth'},
    'apps.monitoring.tasks.*': {'queue': 'monitoring'},
    '*.cleanup_*': {'queue': 'cleanup'},
    '*.archive_*': {'queue': 'archive'},
    '*.generate_*': {'queue': 'stats'},
    '*.check_*': {'queue': 'security'},
}

# 任务配置
app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    
    # 时区
    timezone=settings.TIME_ZONE,
    enable_utc=True,
    
    # 结果后端
    result_backend=settings.CELERY_RESULT_BACKEND,
    result_expires=3600,  # 结果保存1小时
    
    # 任务路由
    task_default_queue='default',
    task_default_exchange='default',
    task_default_exchange_type='direct',
    task_default_routing_key='default',
    
    # 工作进程配置
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # 任务重试配置
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
)


@app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
    return 'Debug task completed'


# 启动时的调试信息
@app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """设置定时任务"""
    print("Celery定时任务已配置:")
    for name, task in app.conf.beat_schedule.items():
        print(f"  - {name}: {task['task']}")
