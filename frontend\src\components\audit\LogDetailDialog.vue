<template>
  <n-modal
    :show="visible"
    preset="dialog"
    title="操作日志详情"
    class="log-detail-dialog"
    style="width: 900px"
    @update:show="$emit('update:visible', $event)"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else-if="logDetail" class="log-detail-content">
      <!-- 基础信息 -->
      <n-card title="基础信息" class="detail-card">
        <n-grid :cols="3" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <div class="detail-item">
              <label>操作时间</label>
              <span>{{ logDetail.created_at_formatted }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>操作用户</label>
              <div class="user-info">
                <span v-if="logDetail.user_info">
                  {{ logDetail.user_info.nickname || logDetail.user_info.username }}
                </span>
                <span v-else class="system-user">系统</span>
              </div>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>操作类型</label>
              <n-tag :type="getOperationTypeColor(logDetail.operation_type)" size="small">
                {{ logDetail.operation_type_display }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>IP地址</label>
              <span class="ip-address">{{ logDetail.ip_address }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>请求方法</label>
              <n-tag :type="getMethodColor(logDetail.method)" size="small">
                {{ logDetail.method }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>状态码</label>
              <n-tag :type="getStatusCodeColor(logDetail.status_code)" size="small">
                {{ logDetail.status_code }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>响应时间</label>
              <span :class="getResponseTimeClass(logDetail.response_time)">
                {{ formatResponseTime(logDetail.response_time) }}
              </span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>请求路径</label>
              <span class="request-path">{{ logDetail.path }}</span>
            </div>
          </n-grid-item>
        </n-grid>
        
        <div class="operation-desc">
          <label>操作描述</label>
          <p>{{ logDetail.operation_desc }}</p>
        </div>
      </n-card>

      <!-- 用户代理信息 -->
      <n-card v-if="logDetail.user_agent" title="客户端信息" class="detail-card">
        <n-grid :cols="2" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <div class="detail-item">
              <label>浏览器</label>
              <span>{{ logDetail.user_agent_parsed?.browser || '未知' }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>操作系统</label>
              <span>{{ logDetail.user_agent_parsed?.os || '未知' }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>设备类型</label>
              <span>{{ logDetail.user_agent_parsed?.device || '未知' }}</span>
            </div>
          </n-grid-item>
        </n-grid>
        
        <div class="user-agent-raw">
          <label>完整User-Agent</label>
          <n-code :code="logDetail.user_agent" language="text" />
        </div>
      </n-card>

      <!-- 请求详情 -->
      <n-card title="请求详情" class="detail-card">
        <n-tabs type="line">
          <n-tab-pane name="request" tab="请求信息">
            <div class="request-info">
              <div class="info-item">
                <label>请求URL</label>
                <n-code :code="getFullUrl()" language="text" />
              </div>
              
              <div v-if="requestHeaders" class="info-item">
                <label>请求头</label>
                <n-code :code="JSON.stringify(requestHeaders, null, 2)" language="json" />
              </div>
              
              <div v-if="requestParams" class="info-item">
                <label>请求参数</label>
                <n-code :code="JSON.stringify(requestParams, null, 2)" language="json" />
              </div>
            </div>
          </n-tab-pane>
          
          <n-tab-pane name="response" tab="响应信息">
            <div class="response-info">
              <div class="info-item">
                <label>响应状态</label>
                <div class="status-info">
                  <n-tag :type="getStatusCodeColor(logDetail.status_code)" size="small">
                    {{ logDetail.status_code }}
                  </n-tag>
                  <span class="status-text">{{ getStatusText(logDetail.status_code) }}</span>
                </div>
              </div>
              
              <div class="info-item">
                <label>响应时间</label>
                <span :class="getResponseTimeClass(logDetail.response_time)">
                  {{ formatResponseTime(logDetail.response_time) }}
                </span>
              </div>
              
              <div v-if="responseHeaders" class="info-item">
                <label>响应头</label>
                <n-code :code="JSON.stringify(responseHeaders, null, 2)" language="json" />
              </div>
              
              <div v-if="responseData" class="info-item">
                <label>响应数据</label>
                <n-code :code="JSON.stringify(responseData, null, 2)" language="json" />
              </div>
            </div>
          </n-tab-pane>
          
          <n-tab-pane v-if="errorInfo" name="error" tab="错误信息">
            <div class="error-info">
              <div class="info-item">
                <label>错误类型</label>
                <span class="error-type">{{ errorInfo.type || '未知错误' }}</span>
              </div>
              
              <div class="info-item">
                <label>错误消息</label>
                <n-alert type="error" :title="errorInfo.message" />
              </div>
              
              <div v-if="errorInfo.stack" class="info-item">
                <label>错误堆栈</label>
                <n-code :code="errorInfo.stack" language="text" />
              </div>
            </div>
          </n-tab-pane>
        </n-tabs>
      </n-card>

      <!-- 关联操作 -->
      <n-card v-if="relatedLogs.length > 0" title="关联操作" class="detail-card">
        <div class="related-logs">
          <div
            v-for="log in relatedLogs"
            :key="log.id"
            class="related-log-item"
            @click="handleViewRelatedLog(log.id)"
          >
            <div class="log-time">{{ log.created_at_formatted }}</div>
            <div class="log-type">
              <n-tag :type="getOperationTypeColor(log.operation_type)" size="small">
                {{ log.operation_type_display }}
              </n-tag>
            </div>
            <div class="log-desc">{{ log.operation_desc }}</div>
            <div class="log-status">
              <n-tag :type="getStatusCodeColor(log.status_code)" size="small">
                {{ log.status_code }}
              </n-tag>
            </div>
          </div>
        </div>
      </n-card>
    </div>
    
    <div v-else class="empty-state">
      <n-empty description="未找到日志信息" />
    </div>
    
    <template #action>
      <n-space>
        <n-button @click="handleClose">关闭</n-button>
        <n-button
          v-if="logDetail"
          type="primary"
          @click="handleExportSingle"
        >
          导出此日志
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { useAuditStore } from '@/stores/audit'
import { auditApi } from '@/api/audit'
import type { OperationLogDetail, OperationLogListItem } from '@/types/audit'

// Props
interface Props {
  visible: boolean
  logId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  logId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'view-related': [logId: number]
}>()

// 状态管理
const auditStore = useAuditStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const relatedLogs = ref<OperationLogListItem[]>([])

// 模拟数据（实际应该从API获取）
const requestHeaders = ref<Record<string, string> | null>(null)
const requestParams = ref<Record<string, any> | null>(null)
const responseHeaders = ref<Record<string, string> | null>(null)
const responseData = ref<any>(null)
const errorInfo = ref<{
  type?: string
  message?: string
  stack?: string
} | null>(null)

// 计算属性
const logDetail = computed(() => auditStore.currentOperationLog)

// 方法
const getOperationTypeColor = (type: string) => {
  switch (type) {
    case 'LOGIN':
      return 'success'
    case 'LOGOUT':
      return 'info'
    case 'CREATE':
      return 'success'
    case 'UPDATE':
      return 'warning'
    case 'DELETE':
      return 'error'
    case 'QUERY':
      return 'info'
    case 'ERROR':
      return 'error'
    default:
      return 'default'
  }
}

const getMethodColor = (method: string) => {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'info'
    case 'POST':
      return 'success'
    case 'PUT':
      return 'warning'
    case 'PATCH':
      return 'warning'
    case 'DELETE':
      return 'error'
    default:
      return 'default'
  }
}

const getStatusCodeColor = (code: number) => {
  if (code >= 200 && code < 300) {
    return 'success'
  } else if (code >= 400 && code < 500) {
    return 'warning'
  } else if (code >= 500) {
    return 'error'
  } else {
    return 'default'
  }
}

const getResponseTimeClass = (time: number) => {
  if (time < 100) {
    return 'response-time-fast'
  } else if (time < 500) {
    return 'response-time-normal'
  } else if (time < 1000) {
    return 'response-time-slow'
  } else {
    return 'response-time-very-slow'
  }
}

const formatResponseTime = (time: number) => {
  return auditApi.formatResponseTime(time)
}

const getStatusText = (code: number) => {
  const statusTexts: Record<number, string> = {
    200: 'OK',
    201: 'Created',
    204: 'No Content',
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable'
  }
  return statusTexts[code] || 'Unknown'
}

const getFullUrl = () => {
  if (!logDetail.value) return ''
  
  // 这里应该根据实际情况构建完整URL
  const baseUrl = window.location.origin
  return `${baseUrl}${logDetail.value.path}`
}

const fetchLogDetail = async () => {
  if (!props.logId) return
  
  try {
    loading.value = true
    await auditStore.fetchOperationLogDetail(props.logId)
    
    // 获取关联日志（模拟）
    // 实际应该调用API获取相同用户或相同IP的相关操作
    relatedLogs.value = []
  } catch (error) {
    message.error('获取日志详情失败')
  } finally {
    loading.value = false
  }
}

const handleViewRelatedLog = (logId: number) => {
  emit('view-related', logId)
}

const handleExportSingle = () => {
  // 导出单个日志
  message.info('导出功能开发中')
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.logId) {
    fetchLogDetail()
  }
})

watch(() => props.logId, (newLogId) => {
  if (props.visible && newLogId) {
    fetchLogDetail()
  }
})
</script>

<style scoped>
.log-detail-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.log-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #1f2937;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.system-user {
  color: #6b7280;
  font-style: italic;
}

.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.request-path {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  word-break: break-all;
}

.operation-desc {
  margin-top: 16px;
}

.operation-desc label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  display: block;
  margin-bottom: 8px;
}

.operation-desc p {
  font-size: 14px;
  color: #1f2937;
  margin: 0;
  line-height: 1.5;
}

.user-agent-raw {
  margin-top: 16px;
}

.user-agent-raw label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  display: block;
  margin-bottom: 8px;
}

.request-info,
.response-info,
.error-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 14px;
  color: #6b7280;
}

.error-type {
  color: #dc2626;
  font-weight: 500;
}

.response-time-fast {
  color: #16a34a;
  font-weight: 500;
}

.response-time-normal {
  color: #2563eb;
}

.response-time-slow {
  color: #d97706;
  font-weight: 500;
}

.response-time-very-slow {
  color: #dc2626;
  font-weight: 600;
}

.related-logs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.related-log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.related-log-item:hover {
  background: #f3f4f6;
}

.log-time {
  font-size: 12px;
  color: #6b7280;
  min-width: 120px;
}

.log-type {
  min-width: 80px;
}

.log-desc {
  flex: 1;
  font-size: 14px;
  color: #1f2937;
}

.log-status {
  min-width: 60px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
