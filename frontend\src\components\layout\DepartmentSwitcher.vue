<template>
  <div class="department-switcher">
    <!-- 单部门用户显示 -->
    <div v-if="!authStore.isMultiDepartmentUser && authStore.currentDeptInfo" 
         class="single-department">
      <span class="department-icon">🏢</span>
      <span class="department-name">{{ authStore.currentDeptInfo.name }}</span>
    </div>

    <!-- 多部门用户显示下拉选择 -->
    <n-dropdown 
      v-else-if="authStore.isMultiDepartmentUser"
      :options="departmentOptions"
      @select="handleDepartmentSelect"
      trigger="click"
    >
      <n-button text class="department-button">
        <template #icon>
          <span class="department-icon">🏢</span>
        </template>
        <span class="department-name">
          {{ authStore.currentDeptInfo?.name || '选择部门' }}
        </span>
        <template #suffix>
          <n-icon size="14">
            <svg viewBox="0 0 24 24">
              <path d="M7 10l5 5 5-5z" fill="currentColor" />
            </svg>
          </n-icon>
        </template>
      </n-button>
    </n-dropdown>

    <!-- 无部门时的显示 -->
    <div v-else class="no-department">
      <span class="department-icon">❓</span>
      <span class="department-name">未分配部门</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import type { DropdownOption } from 'naive-ui'

const authStore = useAuthStore()
const message = useMessage()

// 部门选项
const departmentOptions = computed((): DropdownOption[] => {
  return authStore.departments.map(dept => ({
    label: dept.name,
    key: dept.id,
    disabled: dept.id === authStore.currentDepartment,
    props: {
      onClick: () => handleDepartmentSelect(dept.id)
    }
  }))
})

// 处理部门选择
const handleDepartmentSelect = async (departmentId: number) => {
  try {
    await authStore.switchDepartment(departmentId)
    message.success('部门切换成功')
    
    // 可以在这里触发页面刷新或重新加载权限相关数据
    // window.location.reload()
  } catch (error) {
    message.error('部门切换失败')
    console.error('部门切换失败:', error)
  }
}
</script>

<style scoped>
.department-switcher {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.single-department,
.no-department {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: #f3f4f6;
  color: #374151;
  font-size: 14px;
}

.department-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px !important;
  border-radius: 6px;
  background-color: #f3f4f6;
  color: #374151;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.department-button:hover {
  background-color: #e5e7eb;
}

.department-icon {
  font-size: 16px;
  line-height: 1;
}

.department-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.no-department {
  opacity: 0.6;
}
</style>
