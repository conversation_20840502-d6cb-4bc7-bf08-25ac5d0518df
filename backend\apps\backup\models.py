"""
数据备份和恢复数据模型
"""
from django.db import models
from django.utils import timezone
from apps.common.models import BaseModel
import hashlib
import os


class BackupJob(BaseModel):
    """备份任务"""
    
    BACKUP_TYPES = [
        ('full', '全量备份'),
        ('incremental', '增量备份'),
        ('differential', '差异备份'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    COMPRESSION_TYPES = [
        ('none', '无压缩'),
        ('gzip', 'GZIP压缩'),
        ('bzip2', 'BZIP2压缩'),
        ('lzma', 'LZMA压缩'),
    ]
    
    name = models.CharField(max_length=200, verbose_name="备份名称")
    description = models.TextField(blank=True, verbose_name="备份描述")
    
    # 备份配置
    backup_type = models.CharField(max_length=20, choices=BACKUP_TYPES, verbose_name="备份类型")
    compression = models.CharField(max_length=20, choices=COMPRESSION_TYPES, default='gzip', verbose_name="压缩方式")
    
    # 备份范围
    include_tables = models.JSONField(default=list, verbose_name="包含的表")
    exclude_tables = models.JSONField(default=list, verbose_name="排除的表")
    include_media = models.BooleanField(default=True, verbose_name="包含媒体文件")
    include_logs = models.BooleanField(default=False, verbose_name="包含日志文件")
    
    # 任务状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="任务状态")
    progress = models.IntegerField(default=0, verbose_name="进度百分比")
    
    # 时间信息
    scheduled_at = models.DateTimeField(null=True, blank=True, verbose_name="计划执行时间")
    started_at = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")
    
    # 执行信息
    executed_by = models.ForeignKey(
        'users.UserProfile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="执行人"
    )
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    execution_log = models.TextField(blank=True, verbose_name="执行日志")
    
    # 备份文件信息
    backup_file_path = models.CharField(max_length=500, blank=True, verbose_name="备份文件路径")
    backup_file_size = models.BigIntegerField(default=0, verbose_name="备份文件大小(bytes)")
    backup_file_hash = models.CharField(max_length=64, blank=True, verbose_name="备份文件哈希")
    
    # 自动清理配置
    retention_days = models.IntegerField(default=30, verbose_name="保留天数")
    auto_cleanup = models.BooleanField(default=True, verbose_name="自动清理")
    
    class Meta:
        db_table = 'backup_job'
        verbose_name = "备份任务"
        verbose_name_plural = "备份任务"
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['backup_type', 'created_at']),
            models.Index(fields=['scheduled_at']),
            models.Index(fields=['completed_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.get_backup_type_display()}"
    
    def calculate_file_hash(self):
        """计算备份文件哈希值"""
        if not self.backup_file_path or not os.path.exists(self.backup_file_path):
            return None
        
        hash_sha256 = hashlib.sha256()
        with open(self.backup_file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        
        return hash_sha256.hexdigest()
    
    def verify_integrity(self):
        """验证备份文件完整性"""
        if not self.backup_file_hash:
            return False
        
        current_hash = self.calculate_file_hash()
        return current_hash == self.backup_file_hash
    
    @property
    def duration(self):
        """获取备份耗时"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


class RestoreJob(BaseModel):
    """恢复任务"""
    
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    RESTORE_MODES = [
        ('full', '完全恢复'),
        ('partial', '部分恢复'),
        ('schema_only', '仅结构'),
        ('data_only', '仅数据'),
    ]
    
    name = models.CharField(max_length=200, verbose_name="恢复名称")
    description = models.TextField(blank=True, verbose_name="恢复描述")
    
    # 关联的备份任务
    backup_job = models.ForeignKey(BackupJob, on_delete=models.CASCADE, verbose_name="备份任务")
    
    # 恢复配置
    restore_mode = models.CharField(max_length=20, choices=RESTORE_MODES, verbose_name="恢复模式")
    target_database = models.CharField(max_length=100, blank=True, verbose_name="目标数据库")
    
    # 恢复范围
    include_tables = models.JSONField(default=list, verbose_name="包含的表")
    exclude_tables = models.JSONField(default=list, verbose_name="排除的表")
    restore_media = models.BooleanField(default=True, verbose_name="恢复媒体文件")
    
    # 任务状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="任务状态")
    progress = models.IntegerField(default=0, verbose_name="进度百分比")
    
    # 时间信息
    scheduled_at = models.DateTimeField(null=True, blank=True, verbose_name="计划执行时间")
    started_at = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")
    
    # 执行信息
    executed_by = models.ForeignKey(
        'users.UserProfile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="执行人"
    )
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    execution_log = models.TextField(blank=True, verbose_name="执行日志")
    
    # 验证信息
    pre_restore_verification = models.BooleanField(default=True, verbose_name="恢复前验证")
    post_restore_verification = models.BooleanField(default=True, verbose_name="恢复后验证")
    verification_passed = models.BooleanField(default=False, verbose_name="验证通过")
    
    class Meta:
        db_table = 'backup_restore_job'
        verbose_name = "恢复任务"
        verbose_name_plural = "恢复任务"
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['backup_job', 'created_at']),
            models.Index(fields=['scheduled_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.backup_job.name}"


class BackupSchedule(BaseModel):
    """备份计划"""
    
    FREQUENCY_CHOICES = [
        ('hourly', '每小时'),
        ('daily', '每天'),
        ('weekly', '每周'),
        ('monthly', '每月'),
        ('custom', '自定义'),
    ]
    
    name = models.CharField(max_length=200, verbose_name="计划名称")
    description = models.TextField(blank=True, verbose_name="计划描述")
    
    # 计划配置
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, verbose_name="执行频率")
    cron_expression = models.CharField(max_length=100, blank=True, verbose_name="Cron表达式")
    
    # 备份配置模板
    backup_type = models.CharField(max_length=20, choices=BackupJob.BACKUP_TYPES, verbose_name="备份类型")
    compression = models.CharField(max_length=20, choices=BackupJob.COMPRESSION_TYPES, default='gzip', verbose_name="压缩方式")
    include_tables = models.JSONField(default=list, verbose_name="包含的表")
    exclude_tables = models.JSONField(default=list, verbose_name="排除的表")
    include_media = models.BooleanField(default=True, verbose_name="包含媒体文件")
    include_logs = models.BooleanField(default=False, verbose_name="包含日志文件")
    
    # 保留策略
    retention_days = models.IntegerField(default=30, verbose_name="保留天数")
    max_backups = models.IntegerField(default=10, verbose_name="最大备份数量")
    
    # 计划状态
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    next_run_time = models.DateTimeField(null=True, blank=True, verbose_name="下次执行时间")
    last_run_time = models.DateTimeField(null=True, blank=True, verbose_name="上次执行时间")
    
    # 通知配置
    notify_on_success = models.BooleanField(default=False, verbose_name="成功时通知")
    notify_on_failure = models.BooleanField(default=True, verbose_name="失败时通知")
    notification_emails = models.JSONField(default=list, verbose_name="通知邮箱")
    
    class Meta:
        db_table = 'backup_schedule'
        verbose_name = "备份计划"
        verbose_name_plural = "备份计划"
        indexes = [
            models.Index(fields=['is_active', 'next_run_time']),
            models.Index(fields=['frequency']),
        ]
        ordering = ['next_run_time']
    
    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"


class BackupStorage(BaseModel):
    """备份存储配置"""
    
    STORAGE_TYPES = [
        ('local', '本地存储'),
        ('ftp', 'FTP服务器'),
        ('sftp', 'SFTP服务器'),
        ('s3', 'Amazon S3'),
        ('oss', '阿里云OSS'),
        ('cos', '腾讯云COS'),
    ]
    
    name = models.CharField(max_length=100, verbose_name="存储名称")
    storage_type = models.CharField(max_length=20, choices=STORAGE_TYPES, verbose_name="存储类型")
    
    # 存储配置
    config = models.JSONField(default=dict, verbose_name="存储配置")
    
    # 存储状态
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    is_default = models.BooleanField(default=False, verbose_name="是否默认")
    
    # 容量信息
    total_capacity = models.BigIntegerField(null=True, blank=True, verbose_name="总容量(bytes)")
    used_capacity = models.BigIntegerField(default=0, verbose_name="已用容量(bytes)")
    
    # 连接测试
    last_test_time = models.DateTimeField(null=True, blank=True, verbose_name="上次测试时间")
    test_result = models.BooleanField(default=False, verbose_name="测试结果")
    test_message = models.TextField(blank=True, verbose_name="测试信息")
    
    class Meta:
        db_table = 'backup_storage'
        verbose_name = "备份存储"
        verbose_name_plural = "备份存储"
        indexes = [
            models.Index(fields=['storage_type', 'is_active']),
            models.Index(fields=['is_default']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.get_storage_type_display()}"
    
    @property
    def usage_percentage(self):
        """获取存储使用率"""
        if self.total_capacity and self.total_capacity > 0:
            return (self.used_capacity / self.total_capacity) * 100
        return 0
