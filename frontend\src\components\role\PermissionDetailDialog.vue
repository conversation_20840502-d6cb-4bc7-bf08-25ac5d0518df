<template>
  <n-modal
    :show="visible"
    preset="dialog"
    title="权限详情"
    class="permission-detail-dialog"
    style="width: 700px"
    @update:show="$emit('update:visible', $event)"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else-if="permissionDetail" class="permission-detail-content">
      <!-- 基础信息 -->
      <n-card title="基础信息" class="detail-card">
        <n-grid :cols="3" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <div class="detail-item">
              <label>权限名称</label>
              <span>{{ permissionDetail.name }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>权限编码</label>
              <span>{{ permissionDetail.code }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>权限类型</label>
              <n-tag :type="getPermissionTypeColor(permissionDetail.permission_type)" size="small">
                {{ getPermissionTypeLabel(permissionDetail.permission_type) }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>权限状态</label>
              <n-tag :type="permissionDetail.is_active ? 'success' : 'error'" size="small">
                {{ permissionDetail.is_active ? '正常' : '禁用' }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>排序权重</label>
              <span>{{ permissionDetail.sort_order }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>子权限数</label>
              <span>{{ permissionDetail.children_count }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>父权限</label>
              <span>{{ permissionDetail.parent_name || '-' }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>创建时间</label>
              <span>{{ formatDateTime(permissionDetail.created_at) }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>更新时间</label>
              <span>{{ formatDateTime(permissionDetail.updated_at) }}</span>
            </div>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 路径信息 -->
      <n-card v-if="hasPathInfo" title="路径信息" class="detail-card">
        <n-grid :cols="2" :x-gap="24" :y-gap="16">
          <n-grid-item v-if="permissionDetail.path">
            <div class="detail-item">
              <label>路由路径</label>
              <span class="path-text">{{ permissionDetail.path }}</span>
            </div>
          </n-grid-item>
          <n-grid-item v-if="permissionDetail.component">
            <div class="detail-item">
              <label>组件路径</label>
              <span class="path-text">{{ permissionDetail.component }}</span>
            </div>
          </n-grid-item>
          <n-grid-item v-if="permissionDetail.icon">
            <div class="detail-item">
              <label>图标</label>
              <div class="icon-display">
                <n-icon :component="getPermissionIcon(permissionDetail.icon)" />
                <span>{{ permissionDetail.icon }}</span>
              </div>
            </div>
          </n-grid-item>
          <n-grid-item v-if="permissionDetail.http_method">
            <div class="detail-item">
              <label>HTTP方法</label>
              <n-tag :type="getHttpMethodColor(permissionDetail.http_method)" size="small">
                {{ permissionDetail.http_method }}
              </n-tag>
            </div>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 权限路径 -->
      <n-card v-if="permissionDetail.permission_path" title="权限路径" class="detail-card">
        <div class="permission-path">
          <n-breadcrumb>
            <n-breadcrumb-item
              v-for="(pathItem, index) in permissionPathItems"
              :key="index"
            >
              {{ pathItem }}
            </n-breadcrumb-item>
          </n-breadcrumb>
        </div>
      </n-card>

      <!-- 子权限 -->
      <n-card v-if="childPermissions.length > 0" title="子权限" class="detail-card">
        <template #header-extra>
          <n-button
            v-permission="'permission:create'"
            size="small"
            type="primary"
            @click="handleCreateChild"
          >
            添加子权限
          </n-button>
        </template>
        
        <div class="child-permissions">
          <div
            v-for="child in childPermissions"
            :key="child.id"
            class="child-permission-item"
          >
            <div class="child-info">
              <n-icon
                v-if="child.icon"
                :component="getPermissionIcon(child.icon)"
                class="child-icon"
              />
              <div class="child-details">
                <div class="child-name">{{ child.name }}</div>
                <div class="child-code">{{ child.code }}</div>
              </div>
              <n-tag
                :type="getPermissionTypeColor(child.permission_type)"
                size="small"
                class="child-type"
              >
                {{ getPermissionTypeLabel(child.permission_type) }}
              </n-tag>
              <n-tag
                :type="child.is_active ? 'success' : 'error'"
                size="small"
                class="child-status"
              >
                {{ child.is_active ? '正常' : '禁用' }}
              </n-tag>
            </div>
            
            <div class="child-actions">
              <n-button
                v-permission="'permission:view'"
                size="small"
                type="primary"
                text
                @click="handleViewChild(child.id)"
              >
                查看
              </n-button>
              <n-button
                v-permission="'permission:update'"
                size="small"
                type="primary"
                text
                @click="handleEditChild(child)"
              >
                编辑
              </n-button>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 使用情况 -->
      <n-card title="使用情况" class="detail-card">
        <template #header-extra>
          <n-button
            size="small"
            @click="handleRefreshUsage"
            :loading="usageLoading"
          >
            刷新
          </n-button>
        </template>
        
        <div v-if="permissionUsage" class="usage-info">
          <div class="usage-stats">
            <div class="usage-stat">
              <span class="stat-label">关联角色:</span>
              <span class="stat-value">{{ permissionUsage.role_count }}</span>
            </div>
            <div class="usage-stat">
              <span class="stat-label">关联用户:</span>
              <span class="stat-value">{{ permissionUsage.user_count }}</span>
            </div>
          </div>
          
          <div v-if="permissionUsage.roles.length > 0" class="usage-roles">
            <h4>关联角色</h4>
            <div class="role-list">
              <n-tag
                v-for="role in permissionUsage.roles"
                :key="role.id"
                size="small"
                class="role-tag"
              >
                {{ role.name }}
              </n-tag>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-usage">
          <n-empty description="暂无使用情况数据" />
        </div>
      </n-card>
    </div>
    
    <div v-else class="empty-state">
      <n-empty description="未找到权限信息" />
    </div>
    
    <template #action>
      <n-button @click="handleClose">关闭</n-button>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { SearchIcon } from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { roleApi } from '@/api/role'
import type { Permission, PermissionType, PermissionUsage } from '@/types/role'

// Props
interface Props {
  visible: boolean
  permissionId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  permissionId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'create-child': [parentId: number]
  'edit-child': [permission: Permission]
  'view-child': [permissionId: number]
}>()

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const usageLoading = ref(false)
const childPermissions = ref<Permission[]>([])
const permissionUsage = ref<PermissionUsage | null>(null)

// 计算属性
const permissionDetail = computed(() => roleStore.currentPermission)

const hasPathInfo = computed(() => {
  if (!permissionDetail.value) return false
  return !!(permissionDetail.value.path || 
           permissionDetail.value.component || 
           permissionDetail.value.icon || 
           permissionDetail.value.http_method)
})

const permissionPathItems = computed(() => {
  if (!permissionDetail.value?.permission_path) return []
  return permissionDetail.value.permission_path.split(' > ')
})

// 方法
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getPermissionTypeColor = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return 'info'
    case 'BUTTON':
      return 'warning'
    case 'API':
      return 'success'
    default:
      return 'default'
  }
}

const getPermissionTypeLabel = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return '菜单'
    case 'BUTTON':
      return '按钮'
    case 'API':
      return 'API'
    default:
      return type
  }
}

const getPermissionIcon = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  return SearchIcon // 临时使用搜索图标
}

const getHttpMethodColor = (method: string) => {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'info'
    case 'POST':
      return 'success'
    case 'PUT':
      return 'warning'
    case 'PATCH':
      return 'warning'
    case 'DELETE':
      return 'error'
    default:
      return 'default'
  }
}

const fetchPermissionDetail = async () => {
  if (!props.permissionId) return
  
  try {
    loading.value = true
    await roleStore.fetchPermissionDetail(props.permissionId)
  } catch (error) {
    message.error('获取权限详情失败')
  } finally {
    loading.value = false
  }
}

const fetchChildPermissions = async () => {
  if (!props.permissionId) return
  
  try {
    const response = await roleApi.getPermissionList({ parent_id: props.permissionId })
    if (response.code === 200) {
      childPermissions.value = response.data.results
    }
  } catch (error) {
    console.error('获取子权限失败:', error)
  }
}

const fetchPermissionUsage = async () => {
  if (!props.permissionId) return
  
  try {
    usageLoading.value = true
    const response = await roleApi.getPermissionUsage(props.permissionId)
    if (response.code === 200) {
      permissionUsage.value = response.data
    }
  } catch (error) {
    console.error('获取权限使用情况失败:', error)
  } finally {
    usageLoading.value = false
  }
}

const handleCreateChild = () => {
  if (props.permissionId) {
    emit('create-child', props.permissionId)
  }
}

const handleEditChild = (permission: Permission) => {
  emit('edit-child', permission)
}

const handleViewChild = (permissionId: number) => {
  emit('view-child', permissionId)
}

const handleRefreshUsage = () => {
  fetchPermissionUsage()
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.permissionId) {
    fetchPermissionDetail()
    fetchChildPermissions()
    fetchPermissionUsage()
  }
})

watch(() => props.permissionId, (newPermissionId) => {
  if (props.visible && newPermissionId) {
    fetchPermissionDetail()
    fetchChildPermissions()
    fetchPermissionUsage()
  }
})
</script>

<style scoped>
.permission-detail-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.permission-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #1f2937;
}

.path-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.icon-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-path {
  padding: 12px 0;
}

.child-permissions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.child-permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.child-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.child-icon {
  flex-shrink: 0;
  color: #6b7280;
}

.child-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.child-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.child-code {
  font-size: 12px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.child-type,
.child-status {
  flex-shrink: 0;
}

.child-actions {
  display: flex;
  gap: 8px;
}

.usage-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-stats {
  display: flex;
  gap: 24px;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
}

.usage-stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.usage-roles h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.role-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.role-tag {
  margin: 0;
}

.empty-usage,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
