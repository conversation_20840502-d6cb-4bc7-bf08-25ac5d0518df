<template>
  <div class="secure-file-upload">
    <n-card title="安全文件上传" class="upload-card">
      <!-- 上传配置信息 -->
      <div v-if="uploadConfig" class="upload-config">
        <n-alert type="info" class="config-alert">
          <template #header>上传限制</template>
          <div class="config-details">
            <div class="config-item">
              <span class="label">文件类型:</span>
              <span class="value">{{ allowedExtensions.join(', ') }}</span>
            </div>
            <div class="config-item">
              <span class="label">最大大小:</span>
              <span class="value">{{ maxSizeMB }}MB</span>
            </div>
            <div class="config-item">
              <span class="label">安全检查:</span>
              <span class="value">文件类型验证、内容扫描、病毒检测</span>
            </div>
          </div>
        </n-alert>
      </div>

      <!-- 文件上传区域 -->
      <n-upload
        ref="uploadRef"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :file-list="fileList"
        :max="maxFiles"
        :accept="acceptTypes"
        :before-upload="beforeUpload"
        :on-finish="onUploadFinish"
        :on-error="onUploadError"
        :on-remove="onFileRemove"
        :show-file-list="true"
        :multiple="allowMultiple"
        :directory-dnd="false"
        class="upload-area"
      >
        <n-upload-dragger>
          <div class="upload-content">
            <n-icon size="48" class="upload-icon">
              <CloudUploadIcon />
            </n-icon>
            <n-text class="upload-text">
              点击或拖拽文件到此区域上传
            </n-text>
            <n-text depth="3" class="upload-hint">
              支持 {{ allowedExtensions.join(', ') }} 格式，单个文件不超过 {{ maxSizeMB }}MB
            </n-text>
          </div>
        </n-upload-dragger>
      </n-upload>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <n-progress
          type="line"
          :percentage="uploadProgress"
          :status="uploadStatus"
          :show-indicator="true"
        />
        <n-text class="progress-text">
          {{ uploadProgressText }}
        </n-text>
      </div>

      <!-- 安全验证结果 -->
      <div v-if="securityResults.length > 0" class="security-results">
        <n-divider>安全验证结果</n-divider>
        <div
          v-for="result in securityResults"
          :key="result.filename"
          class="security-result-item"
        >
          <div class="result-header">
            <n-icon :color="result.isValid ? '#52c41a' : '#ff4d4f'">
              <CheckCircleIcon v-if="result.isValid" />
              <CloseCircleIcon v-else />
            </n-icon>
            <span class="filename">{{ result.filename }}</span>
            <n-tag :type="result.isValid ? 'success' : 'error'" size="small">
              {{ result.isValid ? '安全' : '风险' }}
            </n-tag>
          </div>
          
          <div v-if="!result.isValid" class="error-details">
            <n-alert type="error" :title="`安全检查失败`">
              <ul class="error-list">
                <li v-for="error in result.errors" :key="error">{{ error }}</li>
              </ul>
            </n-alert>
          </div>
          
          <div v-if="result.securityInfo" class="security-info">
            <n-descriptions :column="2" size="small">
              <n-descriptions-item label="文件哈希">
                {{ result.fileInfo?.hash?.substring(0, 16) }}...
              </n-descriptions-item>
              <n-descriptions-item label="MIME类型">
                {{ result.fileInfo?.mimeType }}
              </n-descriptions-item>
              <n-descriptions-item label="扫描时间">
                {{ formatTime(result.securityInfo.scanTimestamp) }}
              </n-descriptions-item>
              <n-descriptions-item label="威胁检测">
                {{ result.securityInfo.hasMaliciousContent ? '发现威胁' : '未发现威胁' }}
              </n-descriptions-item>
            </n-descriptions>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="upload-actions">
        <n-space>
          <n-button
            type="primary"
            :loading="uploading"
            :disabled="fileList.length === 0"
            @click="startUpload"
          >
            <template #icon>
              <n-icon><UploadIcon /></n-icon>
            </template>
            开始上传
          </n-button>
          
          <n-button
            :disabled="uploading"
            @click="clearFiles"
          >
            <template #icon>
              <n-icon><TrashIcon /></n-icon>
            </template>
            清空文件
          </n-button>
          
          <n-button
            type="info"
            @click="showSecuritySettings = true"
          >
            <template #icon>
              <n-icon><SettingsIcon /></n-icon>
            </template>
            安全设置
          </n-button>
        </n-space>
      </div>
    </n-card>

    <!-- 安全设置对话框 -->
    <n-modal
      v-model:show="showSecuritySettings"
      preset="dialog"
      title="文件上传安全设置"
      style="width: 600px"
    >
      <div class="security-settings">
        <n-form :model="securityConfig" label-placement="left" label-width="120px">
          <n-form-item label="文件类型检查">
            <n-switch v-model:value="securityConfig.enableTypeCheck">
              <template #checked>启用</template>
              <template #unchecked>禁用</template>
            </n-switch>
          </n-form-item>
          
          <n-form-item label="内容安全扫描">
            <n-switch v-model:value="securityConfig.enableContentScan">
              <template #checked>启用</template>
              <template #unchecked>禁用</template>
            </n-switch>
          </n-form-item>
          
          <n-form-item label="文件大小限制">
            <n-input-number
              v-model:value="securityConfig.maxSizeMB"
              :min="1"
              :max="100"
              :step="1"
            >
              <template #suffix>MB</template>
            </n-input-number>
          </n-form-item>
          
          <n-form-item label="允许的扩展名">
            <n-dynamic-tags v-model:value="securityConfig.allowedExtensions" />
          </n-form-item>
          
          <n-form-item label="自动删除风险文件">
            <n-switch v-model:value="securityConfig.autoDeleteRisky">
              <template #checked>启用</template>
              <template #unchecked>禁用</template>
            </n-switch>
          </n-form-item>
        </n-form>
      </div>
      
      <template #action>
        <n-space>
          <n-button @click="showSecuritySettings = false">取消</n-button>
          <n-button type="primary" @click="saveSecurityConfig">保存设置</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useMessage } from 'naive-ui'
import {
  CloudUploadIcon,
  UploadIcon,
  TrashIcon,
  SettingsIcon,
  CheckCircleIcon,
  CloseCircleIcon
} from '@vicons/tabler'
import { useAuthStore } from '@/stores/auth'
import { request } from '@/api'

// Props
interface Props {
  fileType?: 'image' | 'document' | 'archive'
  maxFiles?: number
  allowMultiple?: boolean
  autoUpload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  fileType: 'image',
  maxFiles: 10,
  allowMultiple: true,
  autoUpload: false
})

// Emits
const emit = defineEmits<{
  'upload-success': [files: any[]]
  'upload-error': [error: string]
  'security-check': [results: any[]]
}>()

// 状态管理
const authStore = useAuthStore()
const message = useMessage()

// 响应式数据
const uploadRef = ref()
const fileList = ref<any[]>([])
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref<'success' | 'error' | 'warning' | 'info'>('info')
const uploadProgressText = ref('')
const securityResults = ref<any[]>([])
const showSecuritySettings = ref(false)

// 上传配置
const uploadConfig = ref<any>(null)
const securityConfig = ref({
  enableTypeCheck: true,
  enableContentScan: true,
  maxSizeMB: 10,
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif'],
  autoDeleteRisky: true
})

// 计算属性
const uploadUrl = computed(() => '/api/common/upload/')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))
const uploadData = computed(() => ({
  file_type: props.fileType
}))

const allowedExtensions = computed(() => {
  if (!uploadConfig.value) return securityConfig.value.allowedExtensions
  return uploadConfig.value.allowed_types[props.fileType]?.extensions || []
})

const maxSizeMB = computed(() => {
  if (!uploadConfig.value) return securityConfig.value.maxSizeMB
  return uploadConfig.value.allowed_types[props.fileType]?.max_size_mb || 10
})

const acceptTypes = computed(() => {
  return allowedExtensions.value.join(',')
})

// 方法
const loadUploadConfig = async () => {
  try {
    const response = await request.get('/api/common/upload/config/')
    if (response.data.code === 200) {
      uploadConfig.value = response.data.data
    }
  } catch (error) {
    console.error('加载上传配置失败:', error)
  }
}

const beforeUpload = async (data: { file: File }) => {
  const file = data.file
  
  // 文件大小检查
  const maxSize = maxSizeMB.value * 1024 * 1024
  if (file.size > maxSize) {
    message.error(`文件大小不能超过 ${maxSizeMB.value}MB`)
    return false
  }
  
  // 文件类型检查
  const fileName = file.name.toLowerCase()
  const isValidType = allowedExtensions.value.some(ext => fileName.endsWith(ext))
  if (!isValidType) {
    message.error(`不支持的文件类型，请选择 ${allowedExtensions.value.join(', ')} 格式的文件`)
    return false
  }
  
  // 安全验证
  if (securityConfig.value.enableContentScan) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('file_type', props.fileType)
      
      const response = await request.post('/api/common/upload/validate/', formData)
      const result = response.data.data
      
      securityResults.value.push({
        filename: file.name,
        isValid: result.is_valid,
        errors: result.errors || [],
        fileInfo: result.file_info,
        securityInfo: result.security_info
      })
      
      if (!result.is_valid) {
        message.error(`文件 ${file.name} 安全检查失败`)
        if (securityConfig.value.autoDeleteRisky) {
          return false
        }
      }
      
      emit('security-check', securityResults.value)
      
    } catch (error) {
      console.error('文件安全验证失败:', error)
      message.error('文件安全验证失败')
      return false
    }
  }
  
  return !props.autoUpload // 如果不是自动上传，阻止默认上传行为
}

const startUpload = async () => {
  if (fileList.value.length === 0) {
    message.warning('请先选择文件')
    return
  }
  
  uploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = 'info'
  uploadProgressText.value = '准备上传...'
  
  try {
    const formData = new FormData()
    fileList.value.forEach(fileItem => {
      formData.append('files', fileItem.file)
    })
    formData.append('file_type', props.fileType)
    formData.append('max_files', props.maxFiles.toString())
    
    const response = await request.post('/api/common/upload/batch/', formData, {
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          uploadProgressText.value = `上传中... ${uploadProgress.value}%`
        }
      }
    })
    
    if (response.data.code === 200) {
      uploadStatus.value = 'success'
      uploadProgressText.value = '上传完成'
      message.success(`成功上传 ${response.data.data.success_count} 个文件`)
      
      emit('upload-success', response.data.data.results)
      
      // 清空文件列表
      setTimeout(() => {
        clearFiles()
      }, 2000)
    } else {
      throw new Error(response.data.message)
    }
    
  } catch (error: any) {
    uploadStatus.value = 'error'
    uploadProgressText.value = '上传失败'
    const errorMessage = error.response?.data?.message || error.message || '上传失败'
    message.error(errorMessage)
    emit('upload-error', errorMessage)
  } finally {
    uploading.value = false
  }
}

const onUploadFinish = ({ file, event }: any) => {
  try {
    const response = JSON.parse(event.target.response)
    if (response.code === 200) {
      message.success(`文件 ${file.name} 上传成功`)
      emit('upload-success', [response.data])
    } else {
      throw new Error(response.message)
    }
  } catch (error: any) {
    message.error(`文件 ${file.name} 上传失败: ${error.message}`)
    emit('upload-error', error.message)
  }
}

const onUploadError = ({ file, event }: any) => {
  const errorMessage = `文件 ${file.name} 上传失败`
  message.error(errorMessage)
  emit('upload-error', errorMessage)
}

const onFileRemove = ({ file }: any) => {
  // 从安全验证结果中移除
  securityResults.value = securityResults.value.filter(
    result => result.filename !== file.name
  )
  return true
}

const clearFiles = () => {
  fileList.value = []
  securityResults.value = []
  uploadProgress.value = 0
  uploadProgressText.value = ''
}

const saveSecurityConfig = () => {
  // 保存安全配置到本地存储
  localStorage.setItem('file_upload_security_config', JSON.stringify(securityConfig.value))
  message.success('安全设置已保存')
  showSecuritySettings.value = false
}

const formatTime = (timestamp: string) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadUploadConfig()
  
  // 加载本地安全配置
  const savedConfig = localStorage.getItem('file_upload_security_config')
  if (savedConfig) {
    try {
      securityConfig.value = { ...securityConfig.value, ...JSON.parse(savedConfig) }
    } catch (error) {
      console.error('加载安全配置失败:', error)
    }
  }
})

// 监听文件类型变化，更新配置
watch(() => props.fileType, () => {
  loadUploadConfig()
})
</script>

<style scoped>
.secure-file-upload {
  width: 100%;
}

.upload-card {
  margin-bottom: 16px;
}

.upload-config {
  margin-bottom: 16px;
}

.config-alert {
  margin-bottom: 16px;
}

.config-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item .label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.config-item .value {
  color: #333;
}

.upload-area {
  margin-bottom: 16px;
}

.upload-content {
  text-align: center;
  padding: 40px 20px;
}

.upload-icon {
  color: #1890ff;
  margin-bottom: 16px;
}

.upload-text {
  display: block;
  font-size: 16px;
  margin-bottom: 8px;
}

.upload-hint {
  display: block;
  font-size: 14px;
}

.upload-progress {
  margin: 16px 0;
}

.progress-text {
  display: block;
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
}

.security-results {
  margin-top: 16px;
}

.security-result-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.filename {
  flex: 1;
  font-weight: 500;
}

.error-details {
  margin: 8px 0;
}

.error-list {
  margin: 0;
  padding-left: 20px;
}

.security-info {
  margin-top: 8px;
}

.upload-actions {
  margin-top: 16px;
  text-align: center;
}

.security-settings {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .config-item .label {
    min-width: auto;
  }
  
  .result-header {
    flex-wrap: wrap;
  }
  
  .upload-actions {
    text-align: left;
  }
}
</style>
