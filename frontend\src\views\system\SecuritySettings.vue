<template>
  <div class="security-settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">系统安全设置</h1>
          <p class="page-description">配置系统安全策略和防护机制</p>
        </div>
        
        <!-- 安全状态指示器 -->
        <div class="security-status">
          <div class="status-item">
            <div class="status-dot" :class="{ active: securityStatus.overall >= 80 }"></div>
            <span class="status-text">安全评分: {{ securityStatus.overall }}/100</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <n-grid :cols="1" :x-gap="24" :y-gap="24">
        <!-- 密码策略配置 -->
        <n-grid-item>
          <n-card title="密码策略" class="config-card">
            <template #header-extra>
              <n-button
                type="primary"
                size="small"
                :loading="saving.password"
                @click="savePasswordPolicy"
              >
                保存设置
              </n-button>
            </template>
            
            <n-form
              :model="passwordPolicy"
              label-placement="left"
              label-width="150px"
            >
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="最小长度">
                    <n-input-number
                      v-model:value="passwordPolicy.minLength"
                      :min="6"
                      :max="32"
                      :step="1"
                    />
                  </n-form-item>
                  
                  <n-form-item label="密码有效期">
                    <n-input-number
                      v-model:value="passwordPolicy.expiryDays"
                      :min="0"
                      :max="365"
                      :step="1"
                    >
                      <template #suffix>天 (0表示不过期)</template>
                    </n-input-number>
                  </n-form-item>
                </n-grid-item>
                
                <n-grid-item>
                  <n-form-item label="复杂度要求">
                    <n-space vertical>
                      <n-checkbox v-model:checked="passwordPolicy.requireUppercase">
                        包含大写字母
                      </n-checkbox>
                      <n-checkbox v-model:checked="passwordPolicy.requireLowercase">
                        包含小写字母
                      </n-checkbox>
                      <n-checkbox v-model:checked="passwordPolicy.requireNumbers">
                        包含数字
                      </n-checkbox>
                      <n-checkbox v-model:checked="passwordPolicy.requireSymbols">
                        包含特殊字符
                      </n-checkbox>
                    </n-space>
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
          </n-card>
        </n-grid-item>

        <!-- 登录安全配置 -->
        <n-grid-item>
          <n-card title="登录安全" class="config-card">
            <template #header-extra>
              <n-button
                type="primary"
                size="small"
                :loading="saving.login"
                @click="saveLoginSecurity"
              >
                保存设置
              </n-button>
            </template>
            
            <n-form
              :model="loginSecurity"
              label-placement="left"
              label-width="150px"
            >
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="最大失败次数">
                    <n-input-number
                      v-model:value="loginSecurity.maxFailedAttempts"
                      :min="3"
                      :max="20"
                      :step="1"
                    />
                  </n-form-item>
                  
                  <n-form-item label="锁定时长">
                    <n-input-number
                      v-model:value="loginSecurity.lockoutDuration"
                      :min="300"
                      :max="86400"
                      :step="300"
                    >
                      <template #suffix>秒</template>
                    </n-input-number>
                  </n-form-item>
                  
                  <n-form-item label="会话超时">
                    <n-input-number
                      v-model:value="loginSecurity.sessionTimeout"
                      :min="300"
                      :max="86400"
                      :step="300"
                    >
                      <template #suffix>秒</template>
                    </n-input-number>
                  </n-form-item>
                </n-grid-item>
                
                <n-grid-item>
                  <n-form-item label="双因素认证">
                    <n-switch v-model:value="loginSecurity.enableTwoFactor">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                  
                  <n-form-item label="验证码验证">
                    <n-switch v-model:value="loginSecurity.enableCaptcha">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                  
                  <n-form-item label="异常登录检测">
                    <n-switch v-model:value="loginSecurity.enableAnomalyDetection">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                  
                  <n-form-item label="IP白名单">
                    <n-dynamic-tags
                      v-model:value="loginSecurity.ipWhitelist"
                      :render-tag="renderIpTag"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
          </n-card>
        </n-grid-item>

        <!-- 文件上传安全 -->
        <n-grid-item>
          <n-card title="文件上传安全" class="config-card">
            <template #header-extra>
              <n-button
                type="primary"
                size="small"
                :loading="saving.upload"
                @click="saveUploadSecurity"
              >
                保存设置
              </n-button>
            </template>
            
            <n-form
              :model="uploadSecurity"
              label-placement="left"
              label-width="150px"
            >
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="最大文件大小">
                    <n-input-number
                      v-model:value="uploadSecurity.maxFileSize"
                      :min="1"
                      :max="100"
                      :step="1"
                    >
                      <template #suffix>MB</template>
                    </n-input-number>
                  </n-form-item>
                  
                  <n-form-item label="允许的文件类型">
                    <n-dynamic-tags
                      v-model:value="uploadSecurity.allowedExtensions"
                      :render-tag="renderExtensionTag"
                    />
                  </n-form-item>
                </n-grid-item>
                
                <n-grid-item>
                  <n-form-item label="文件内容扫描">
                    <n-switch v-model:value="uploadSecurity.enableContentScan">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                  
                  <n-form-item label="病毒检测">
                    <n-switch v-model:value="uploadSecurity.enableVirusScan">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                  
                  <n-form-item label="自动隔离风险文件">
                    <n-switch v-model:value="uploadSecurity.autoQuarantine">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
          </n-card>
        </n-grid-item>

        <!-- 系统监控配置 -->
        <n-grid-item>
          <n-card title="系统监控" class="config-card">
            <template #header-extra>
              <n-button
                type="primary"
                size="small"
                :loading="saving.monitoring"
                @click="saveMonitoringConfig"
              >
                保存设置
              </n-button>
            </template>
            
            <n-form
              :model="monitoringConfig"
              label-placement="left"
              label-width="150px"
            >
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="日志级别">
                    <n-select
                      v-model:value="monitoringConfig.logLevel"
                      :options="logLevelOptions"
                    />
                  </n-form-item>
                  
                  <n-form-item label="日志保留天数">
                    <n-input-number
                      v-model:value="monitoringConfig.logRetentionDays"
                      :min="1"
                      :max="365"
                      :step="1"
                    >
                      <template #suffix>天</template>
                    </n-input-number>
                  </n-form-item>
                </n-grid-item>
                
                <n-grid-item>
                  <n-form-item label="审计日志">
                    <n-switch v-model:value="monitoringConfig.enableAuditLog">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                  
                  <n-form-item label="性能监控">
                    <n-switch v-model:value="monitoringConfig.enablePerformanceMonitoring">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                  
                  <n-form-item label="安全告警">
                    <n-switch v-model:value="monitoringConfig.enableSecurityAlerts">
                      <template #checked>启用</template>
                      <template #unchecked>禁用</template>
                    </n-switch>
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
          </n-card>
        </n-grid-item>

        <!-- 安全测试工具 -->
        <n-grid-item>
          <n-card title="安全测试" class="config-card">
            <div class="security-tools">
              <n-space>
                <n-button
                  type="info"
                  :loading="testing.vulnerability"
                  @click="runVulnerabilityTest"
                >
                  <template #icon>
                    <n-icon><ShieldCheckIcon /></n-icon>
                  </template>
                  漏洞扫描
                </n-button>
                
                <n-button
                  type="warning"
                  :loading="testing.penetration"
                  @click="runPenetrationTest"
                >
                  <template #icon>
                    <n-icon><BugIcon /></n-icon>
                  </template>
                  渗透测试
                </n-button>
                
                <n-button
                  type="success"
                  :loading="testing.compliance"
                  @click="runComplianceCheck"
                >
                  <template #icon>
                    <n-icon><ChecklistIcon /></n-icon>
                  </template>
                  合规检查
                </n-button>
                
                <n-button
                  @click="showSecurityReport = true"
                >
                  <template #icon>
                    <n-icon><ReportIcon /></n-icon>
                  </template>
                  安全报告
                </n-button>
              </n-space>
            </div>
            
            <!-- 测试结果 -->
            <div v-if="testResults.length > 0" class="test-results">
              <n-divider>测试结果</n-divider>
              <div
                v-for="result in testResults"
                :key="result.id"
                class="test-result-item"
              >
                <div class="result-header">
                  <n-tag :type="getResultType(result.status)" size="small">
                    {{ result.type }}
                  </n-tag>
                  <span class="result-time">{{ formatTime(result.timestamp) }}</span>
                </div>
                <div class="result-content">
                  <p class="result-summary">{{ result.summary }}</p>
                  <div v-if="result.details" class="result-details">
                    <n-collapse>
                      <n-collapse-item title="详细信息" name="details">
                        <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
                      </n-collapse-item>
                    </n-collapse>
                  </div>
                </div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 安全报告对话框 -->
    <!-- <SecurityReportDialog
      v-model:visible="showSecurityReport"
      :security-status="securityStatus"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import {
  ShieldCheckIcon,
  BugIcon,
  ChecklistIcon,
  ReportIcon
} from '@vicons/tabler'
import { request } from '@/api'
// import SecurityReportDialog from '@/components/security/SecurityReportDialog.vue'

// 状态管理
const message = useMessage()

// 响应式数据
const saving = ref({
  password: false,
  login: false,
  upload: false,
  monitoring: false
})

const testing = ref({
  vulnerability: false,
  penetration: false,
  compliance: false
})

const showSecurityReport = ref(false)
const testResults = ref<any[]>([])

// 安全状态
const securityStatus = ref({
  overall: 85,
  password: 90,
  login: 80,
  upload: 85,
  monitoring: 90
})

// 密码策略配置
const passwordPolicy = ref({
  minLength: 8,
  expiryDays: 90,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSymbols: false
})

// 登录安全配置
const loginSecurity = ref({
  maxFailedAttempts: 5,
  lockoutDuration: 1800,
  sessionTimeout: 3600,
  enableTwoFactor: false,
  enableCaptcha: true,
  enableAnomalyDetection: true,
  ipWhitelist: []
})

// 文件上传安全配置
const uploadSecurity = ref({
  maxFileSize: 10,
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'],
  enableContentScan: true,
  enableVirusScan: true,
  autoQuarantine: true
})

// 系统监控配置
const monitoringConfig = ref({
  logLevel: 'INFO',
  logRetentionDays: 30,
  enableAuditLog: true,
  enablePerformanceMonitoring: true,
  enableSecurityAlerts: true
})

// 选项数据
const logLevelOptions = [
  { label: 'DEBUG', value: 'DEBUG' },
  { label: 'INFO', value: 'INFO' },
  { label: 'WARNING', value: 'WARNING' },
  { label: 'ERROR', value: 'ERROR' },
  { label: 'CRITICAL', value: 'CRITICAL' }
]

// 方法
const renderIpTag = ({ option, handleClose }: any) => {
  return h(
    'n-tag',
    {
      closable: true,
      onClose: handleClose
    },
    option
  )
}

const renderExtensionTag = ({ option, handleClose }: any) => {
  return h(
    'n-tag',
    {
      type: 'info',
      closable: true,
      onClose: handleClose
    },
    option
  )
}

const savePasswordPolicy = async () => {
  try {
    saving.value.password = true
    
    const configs = {
      PASSWORD_MIN_LENGTH: passwordPolicy.value.minLength,
      PASSWORD_EXPIRY_DAYS: passwordPolicy.value.expiryDays,
      PASSWORD_REQUIRE_UPPERCASE: passwordPolicy.value.requireUppercase,
      PASSWORD_REQUIRE_LOWERCASE: passwordPolicy.value.requireLowercase,
      PASSWORD_REQUIRE_NUMBERS: passwordPolicy.value.requireNumbers,
      PASSWORD_REQUIRE_SYMBOLS: passwordPolicy.value.requireSymbols
    }
    
    await request.post('/api/common/config/batch/update/', { configs })
    message.success('密码策略保存成功')
    
  } catch (error: any) {
    message.error('密码策略保存失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value.password = false
  }
}

const saveLoginSecurity = async () => {
  try {
    saving.value.login = true
    
    const configs = {
      MAX_LOGIN_ATTEMPTS: loginSecurity.value.maxFailedAttempts,
      ACCOUNT_LOCKOUT_DURATION: loginSecurity.value.lockoutDuration,
      SESSION_TIMEOUT: loginSecurity.value.sessionTimeout
    }
    
    await request.post('/api/common/config/batch/update/', { configs })
    message.success('登录安全设置保存成功')
    
  } catch (error: any) {
    message.error('登录安全设置保存失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value.login = false
  }
}

const saveUploadSecurity = async () => {
  try {
    saving.value.upload = true
    
    const configs = {
      MAX_FILE_SIZE_MB: uploadSecurity.value.maxFileSize,
      ALLOWED_FILE_EXTENSIONS: uploadSecurity.value.allowedExtensions
    }
    
    await request.post('/api/common/config/batch/update/', { configs })
    message.success('文件上传安全设置保存成功')
    
  } catch (error: any) {
    message.error('文件上传安全设置保存失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value.upload = false
  }
}

const saveMonitoringConfig = async () => {
  try {
    saving.value.monitoring = true
    
    const configs = {
      LOG_LEVEL: monitoringConfig.value.logLevel,
      LOG_RETENTION_DAYS: monitoringConfig.value.logRetentionDays,
      AUDIT_LOG_ENABLED: monitoringConfig.value.enableAuditLog
    }
    
    await request.post('/api/common/config/batch/update/', { configs })
    message.success('系统监控配置保存成功')
    
  } catch (error: any) {
    message.error('系统监控配置保存失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value.monitoring = false
  }
}

const runVulnerabilityTest = async () => {
  try {
    testing.value.vulnerability = true
    
    // 模拟漏洞扫描
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    const result = {
      id: Date.now(),
      type: '漏洞扫描',
      status: 'success',
      timestamp: new Date().toISOString(),
      summary: '扫描完成，发现 2 个低风险漏洞',
      details: {
        total_checks: 150,
        vulnerabilities_found: 2,
        risk_level: 'low',
        recommendations: ['更新依赖包', '加强输入验证']
      }
    }
    
    testResults.value.unshift(result)
    message.success('漏洞扫描完成')
    
  } catch (error: any) {
    message.error('漏洞扫描失败: ' + error.message)
  } finally {
    testing.value.vulnerability = false
  }
}

const runPenetrationTest = async () => {
  try {
    testing.value.penetration = true
    
    // 模拟渗透测试
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    const result = {
      id: Date.now(),
      type: '渗透测试',
      status: 'warning',
      timestamp: new Date().toISOString(),
      summary: '发现 1 个中等风险问题',
      details: {
        tests_performed: 50,
        issues_found: 1,
        risk_level: 'medium',
        affected_components: ['登录模块']
      }
    }
    
    testResults.value.unshift(result)
    message.warning('渗透测试发现安全问题')
    
  } catch (error: any) {
    message.error('渗透测试失败: ' + error.message)
  } finally {
    testing.value.penetration = false
  }
}

const runComplianceCheck = async () => {
  try {
    testing.value.compliance = true
    
    // 模拟合规检查
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const result = {
      id: Date.now(),
      type: '合规检查',
      status: 'success',
      timestamp: new Date().toISOString(),
      summary: '合规检查通过，符合安全标准',
      details: {
        standards_checked: ['ISO 27001', 'GDPR', 'SOX'],
        compliance_rate: '98%',
        non_compliant_items: 1
      }
    }
    
    testResults.value.unshift(result)
    message.success('合规检查完成')
    
  } catch (error: any) {
    message.error('合规检查失败: ' + error.message)
  } finally {
    testing.value.compliance = false
  }
}

const getResultType = (status: string) => {
  switch (status) {
    case 'success':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'error'
    default:
      return 'info'
  }
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const loadConfigs = async () => {
  try {
    // 加载各种配置
    const response = await request.get('/api/common/config/', {
      params: { category: 'security' }
    })
    
    if (response.data.code === 200) {
      const configs = response.data.data.configs
      
      // 更新配置数据
      if (configs.PASSWORD_MIN_LENGTH) {
        passwordPolicy.value.minLength = configs.PASSWORD_MIN_LENGTH.value
      }
      // ... 其他配置项的更新
    }
    
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.security-settings-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.security-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #dc2626;
  transition: background-color 0.3s;
}

.status-dot.active {
  background: #16a34a;
}

.status-text {
  font-size: 14px;
  color: #6b7280;
}

.main-content {
  flex: 1;
  padding: 0 24px 24px;
}

.config-card {
  margin-bottom: 24px;
}

.security-tools {
  margin-bottom: 16px;
}

.test-results {
  margin-top: 16px;
}

.test-result-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-time {
  font-size: 12px;
  color: #6b7280;
}

.result-content {
  margin-top: 8px;
}

.result-summary {
  margin: 0 0 8px 0;
  color: #374151;
}

.result-details pre {
  background: #f9fafb;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .main-content {
    padding: 0 16px 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .security-tools {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
