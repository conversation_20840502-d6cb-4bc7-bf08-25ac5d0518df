<template>
  <div class="audit-log-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">审计日志</h1>
          <p class="page-description">查看和分析系统操作日志</p>
        </div>
        
        <!-- 统计卡片 -->
        <div v-if="operationLogStats" class="stats-cards">
          <div class="stat-card">
            <div class="stat-value">{{ operationLogStats.total_count.toLocaleString() }}</div>
            <div class="stat-label">总操作数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ operationLogStats.today_count.toLocaleString() }}</div>
            <div class="stat-label">今日操作</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ operationLogStats.login_count.toLocaleString() }}</div>
            <div class="stat-label">登录次数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ unacknowledgedAlerts }}</div>
            <div class="stat-label">待处理告警</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 搜索和筛选栏 -->
      <div class="search-toolbar">
        <div class="search-section">
          <!-- 搜索框 -->
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索操作描述、用户、IP地址..."
            class="search-input"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
            <template #suffix>
              <n-button
                text
                type="primary"
                @click="showAdvancedSearch = !showAdvancedSearch"
              >
                高级搜索
              </n-button>
            </template>
          </n-input>
          
          <!-- 快速筛选 -->
          <div class="quick-filters">
            <n-select
              v-model:value="quickTimeRange"
              placeholder="时间范围"
              class="filter-select"
              @update:value="handleQuickTimeFilter"
            >
              <n-option value="today" label="今天" />
              <n-option value="yesterday" label="昨天" />
              <n-option value="week" label="本周" />
              <n-option value="month" label="本月" />
              <n-option value="custom" label="自定义" />
            </n-select>
            
            <n-select
              v-model:value="filterOperationType"
              placeholder="操作类型"
              class="filter-select"
              clearable
              @update:value="handleFilter"
            >
              <n-option
                v-for="option in operationTypeOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </n-select>
            
            <n-select
              v-model:value="filterStatus"
              placeholder="状态"
              class="filter-select"
              clearable
              @update:value="handleFilter"
            >
              <n-option value="success" label="成功" />
              <n-option value="error" label="失败" />
              <n-option value="warning" label="警告" />
            </n-select>
          </div>
        </div>
        
        <div class="action-section">
          <!-- 实时监控切换 -->
          <n-switch
            v-model:value="realTimeEnabled"
            @update:value="handleToggleRealTime"
          >
            <template #checked>实时监控</template>
            <template #unchecked>实时监控</template>
          </n-switch>
          
          <!-- 刷新按钮 -->
          <n-button
            :loading="loading"
            @click="handleRefresh"
          >
            <template #icon>
              <n-icon><RefreshIcon /></n-icon>
            </template>
            刷新
          </n-button>
          
          <!-- 导出按钮 -->
          <n-button
            v-permission="'audit:export'"
            type="primary"
            @click="showExportDialog = true"
          >
            <template #icon>
              <n-icon><DownloadIcon /></n-icon>
            </template>
            导出
          </n-button>
          
          <!-- 更多操作 -->
          <n-dropdown
            :options="moreOptions"
            @select="handleMoreAction"
          >
            <n-button>
              更多
              <template #icon>
                <n-icon><MoreIcon /></n-icon>
              </template>
            </n-button>
          </n-dropdown>
        </div>
      </div>

      <!-- 高级搜索面板 -->
      <n-collapse-transition :show="showAdvancedSearch">
        <div class="advanced-search-panel">
          <n-form
            :model="advancedSearchForm"
            label-placement="left"
            label-width="100px"
          >
            <n-grid :cols="4" :x-gap="16">
              <n-form-item-gi label="开始时间">
                <n-date-picker
                  v-model:value="advancedSearchForm.start_date"
                  type="datetime"
                  placeholder="选择开始时间"
                  class="w-full"
                />
              </n-form-item-gi>
              
              <n-form-item-gi label="结束时间">
                <n-date-picker
                  v-model:value="advancedSearchForm.end_date"
                  type="datetime"
                  placeholder="选择结束时间"
                  class="w-full"
                />
              </n-form-item-gi>
              
              <n-form-item-gi label="用户">
                <n-select
                  v-model:value="advancedSearchForm.user_id"
                  placeholder="选择用户"
                  filterable
                  clearable
                  remote
                  :loading="userLoading"
                  :options="userOptions"
                  @search="handleUserSearch"
                  class="w-full"
                />
              </n-form-item-gi>
              
              <n-form-item-gi label="IP地址">
                <n-input
                  v-model:value="advancedSearchForm.ip_address"
                  placeholder="输入IP地址"
                />
              </n-form-item-gi>
              
              <n-form-item-gi label="请求方法">
                <n-select
                  v-model:value="advancedSearchForm.method"
                  placeholder="选择请求方法"
                  clearable
                  :options="methodOptions"
                />
              </n-form-item-gi>
              
              <n-form-item-gi label="状态码">
                <n-input-number
                  v-model:value="advancedSearchForm.status_code"
                  placeholder="输入状态码"
                  :min="100"
                  :max="599"
                  class="w-full"
                />
              </n-form-item-gi>
              
              <n-form-item-gi label="响应时间">
                <n-input-group>
                  <n-input-number
                    v-model:value="advancedSearchForm.min_response_time"
                    placeholder="最小值(ms)"
                    :min="0"
                    style="width: 50%"
                  />
                  <n-input-number
                    v-model:value="advancedSearchForm.max_response_time"
                    placeholder="最大值(ms)"
                    :min="0"
                    style="width: 50%"
                  />
                </n-input-group>
              </n-form-item-gi>
              
              <n-form-item-gi>
                <n-space>
                  <n-button type="primary" @click="handleAdvancedSearch">
                    搜索
                  </n-button>
                  <n-button @click="handleResetAdvancedSearch">
                    重置
                  </n-button>
                  <n-button
                    v-if="canSaveSearch"
                    @click="showSaveSearchDialog = true"
                  >
                    保存搜索
                  </n-button>
                </n-space>
              </n-form-item-gi>
            </n-grid>
          </n-form>
        </div>
      </n-collapse-transition>

      <!-- 保存的搜索和历史 -->
      <div v-if="savedSearches.length > 0 || searchHistory.length > 0" class="saved-searches">
        <n-tabs type="line" size="small">
          <n-tab-pane name="saved" tab="保存的搜索">
            <div class="search-tags">
              <n-tag
                v-for="search in savedSearches"
                :key="search.id"
                closable
                @click="handleLoadSavedSearch(search)"
                @close="handleDeleteSavedSearch(search.id)"
              >
                {{ search.name }}
              </n-tag>
            </div>
          </n-tab-pane>
          <n-tab-pane name="history" tab="搜索历史">
            <div class="search-tags">
              <n-tag
                v-for="history in searchHistory.slice(0, 10)"
                :key="history.id"
                @click="handleLoadSearchHistory(history)"
              >
                {{ history.query || '高级搜索' }}
              </n-tag>
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <n-data-table
          :columns="tableColumns"
          :data="operationLogList"
          :loading="loading"
          :pagination="paginationConfig"
          :row-key="getRowKey"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
          @update:sorter="handleSort"
          striped
          flex-height
          class="audit-table"
        />
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <LogDetailDialog
      v-model:visible="detailDialogVisible"
      :log-id="viewingLogId"
    />

    <!-- 导出对话框 -->
    <ExportDialog
      v-model:visible="showExportDialog"
      :search-params="searchParams"
      @success="handleExportSuccess"
    />

    <!-- 保存搜索对话框 -->
    <SaveSearchDialog
      v-model:visible="showSaveSearchDialog"
      :search-params="advancedSearchForm"
      @success="handleSaveSearchSuccess"
    />

    <!-- 统计图表对话框 -->
    <StatsDialog
      v-model:visible="showStatsDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, h } from 'vue'
import { useMessage, type DataTableColumns, type DropdownOption } from 'naive-ui'
import { 
  SearchIcon, 
  RefreshIcon, 
  DownloadIcon, 
  MoreIcon,
  EyeIcon,
  ChartBarIcon,
  SettingsIcon,
  CleaningServicesIcon
} from '@vicons/tabler'
import { useAuditStore } from '@/stores/audit'
import { auditApi } from '@/api/audit'
import { hasPermission } from '@/utils/auth'
import type { OperationLogListItem, OperationLogSearchParams, OperationType } from '@/types/audit'
import LogDetailDialog from '@/components/audit/LogDetailDialog.vue'
import ExportDialog from '@/components/audit/ExportDialog.vue'
import SaveSearchDialog from '@/components/audit/SaveSearchDialog.vue'
import StatsDialog from '@/components/audit/StatsDialog.vue'

// 状态管理
const auditStore = useAuditStore()
const message = useMessage()

// 响应式数据
const searchKeyword = ref('')
const quickTimeRange = ref<string>('')
const filterOperationType = ref<OperationType | null>(null)
const filterStatus = ref<string | null>(null)
const showAdvancedSearch = ref(false)
const userLoading = ref(false)
const userOptions = ref<Array<{ label: string; value: number }>>([])

// 对话框状态
const detailDialogVisible = ref(false)
const showExportDialog = ref(false)
const showSaveSearchDialog = ref(false)
const showStatsDialog = ref(false)
const viewingLogId = ref<number | null>(null)

// 高级搜索表单
const advancedSearchForm = ref<OperationLogSearchParams>({
  start_date: undefined,
  end_date: undefined,
  user_id: undefined,
  operation_type: undefined,
  method: undefined,
  ip_address: undefined,
  status_code: undefined
})

// 计算属性
const operationLogList = computed(() => auditStore.operationLogList)
const operationLogStats = computed(() => auditStore.operationLogStats)
const loading = computed(() => auditStore.operationLogLoading)
const realTimeEnabled = computed(() => auditStore.realTimeEnabled)
const searchParams = computed(() => auditStore.searchParams)
const savedSearches = computed(() => auditStore.savedSearches)
const searchHistory = computed(() => auditStore.searchHistory)
const unacknowledgedAlerts = computed(() => auditStore.unacknowledgedAlerts)

// 操作类型选项
const operationTypeOptions = [
  { value: 'LOGIN', label: '登录' },
  { value: 'LOGOUT', label: '登出' },
  { value: 'CREATE', label: '创建' },
  { value: 'UPDATE', label: '更新' },
  { value: 'DELETE', label: '删除' },
  { value: 'QUERY', label: '查询' },
  { value: 'ERROR', label: '系统异常' }
]

// HTTP方法选项
const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'DELETE', value: 'DELETE' }
]

// 分页配置
const paginationConfig = computed(() => ({
  page: auditStore.pagination.page,
  pageSize: auditStore.pagination.page_size,
  itemCount: auditStore.pagination.total,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}))

// 表格行键
const getRowKey = (row: OperationLogListItem) => row.id

// 更多操作选项
const moreOptions: DropdownOption[] = [
  {
    label: '统计图表',
    key: 'stats',
    icon: () => h(ChartBarIcon),
    disabled: !hasPermission('audit:view_stats')
  },
  {
    label: '系统设置',
    key: 'settings',
    icon: () => h(SettingsIcon),
    disabled: !hasPermission('audit:manage')
  },
  {
    label: '日志清理',
    key: 'cleanup',
    icon: () => h(CleaningServicesIcon),
    disabled: !hasPermission('audit:cleanup')
  }
]

// 是否可以保存搜索
const canSaveSearch = computed(() => {
  return Object.values(advancedSearchForm.value).some(value =>
    value !== undefined && value !== null && value !== ''
  )
})

// 表格列配置
const tableColumns: DataTableColumns<OperationLogListItem> = [
  {
    title: '操作时间',
    key: 'created_at_formatted',
    width: 160,
    sorter: true
  },
  {
    title: '用户',
    key: 'user_nickname',
    width: 120,
    render: (row: OperationLogListItem) => row.user_nickname || '系统'
  },
  {
    title: '操作类型',
    key: 'operation_type',
    width: 100,
    render: (row: OperationLogListItem) => h(
      'n-tag',
      {
        size: 'small',
        type: getOperationTypeColor(row.operation_type)
      },
      row.operation_type_display
    )
  },
  {
    title: '操作描述',
    key: 'operation_desc',
    ellipsis: {
      tooltip: true
    },
    render: (row: OperationLogListItem) => h('span', {
      class: 'operation-desc',
      innerHTML: highlightKeywords(row.operation_desc, searchKeyword.value)
    })
  },
  {
    title: '请求方法',
    key: 'method',
    width: 80,
    render: (row: OperationLogListItem) => h(
      'n-tag',
      {
        size: 'small',
        type: getMethodColor(row.method)
      },
      row.method
    )
  },
  {
    title: 'IP地址',
    key: 'ip_address',
    width: 130,
    render: (row: OperationLogListItem) => h('span', {
      class: 'ip-address',
      innerHTML: highlightKeywords(row.ip_address, searchKeyword.value)
    })
  },
  {
    title: '状态',
    key: 'status_code',
    width: 80,
    render: (row: OperationLogListItem) => {
      const status = auditApi.formatStatusCode(row.status_code)
      return h(
        'n-tag',
        {
          size: 'small',
          type: status.type
        },
        row.status_code.toString()
      )
    }
  },
  {
    title: '响应时间',
    key: 'response_time',
    width: 100,
    sorter: true,
    render: (row: OperationLogListItem) => h('span', {
      class: getResponseTimeClass(row.response_time)
    }, auditApi.formatResponseTime(row.response_time))
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    fixed: 'right',
    render: (row: OperationLogListItem) => h('n-button', {
      size: 'small',
      type: 'primary',
      text: true,
      onClick: () => handleViewDetail(row.id)
    }, { default: () => '查看', icon: () => h(EyeIcon) })
  }
]

// 工具函数
const getOperationTypeColor = (type: string) => {
  switch (type) {
    case 'LOGIN':
      return 'success'
    case 'LOGOUT':
      return 'info'
    case 'CREATE':
      return 'success'
    case 'UPDATE':
      return 'warning'
    case 'DELETE':
      return 'error'
    case 'QUERY':
      return 'info'
    case 'ERROR':
      return 'error'
    default:
      return 'default'
  }
}

const getMethodColor = (method: string) => {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'info'
    case 'POST':
      return 'success'
    case 'PUT':
      return 'warning'
    case 'PATCH':
      return 'warning'
    case 'DELETE':
      return 'error'
    default:
      return 'default'
  }
}

const getResponseTimeClass = (time: number) => {
  if (time < 100) {
    return 'response-time-fast'
  } else if (time < 500) {
    return 'response-time-normal'
  } else if (time < 1000) {
    return 'response-time-slow'
  } else {
    return 'response-time-very-slow'
  }
}

const highlightKeywords = (text: string, keyword: string) => {
  if (!keyword || !text) return text

  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 事件处理方法
const handleSearch = () => {
  const params: OperationLogSearchParams = {
    ...auditStore.searchParams,
    search: searchKeyword.value,
    page: 1
  }

  auditStore.fetchOperationLogList(params)

  if (searchKeyword.value) {
    auditStore.addSearchHistory(searchKeyword.value, params)
  }
}

const handleFilter = () => {
  const params: OperationLogSearchParams = {
    ...auditStore.searchParams,
    operation_type: filterOperationType.value || undefined,
    page: 1
  }

  // 处理状态筛选
  if (filterStatus.value) {
    switch (filterStatus.value) {
      case 'success':
        params.status_code = 200
        break
      case 'error':
        // 可以设置状态码范围，这里简化处理
        break
      case 'warning':
        // 可以设置状态码范围，这里简化处理
        break
    }
  }

  auditStore.fetchOperationLogList(params)
}

const handleQuickTimeFilter = (value: string) => {
  const now = new Date()
  let startDate: Date | undefined
  let endDate: Date | undefined

  switch (value) {
    case 'today':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      break
    case 'yesterday':
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      startDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
      endDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
      break
    case 'week':
      const weekStart = new Date(now.getTime() - (now.getDay() - 1) * 24 * 60 * 60 * 1000)
      startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate())
      endDate = now
      break
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1)
      endDate = now
      break
    case 'custom':
      // 显示高级搜索面板
      showAdvancedSearch.value = true
      return
  }

  const params: OperationLogSearchParams = {
    ...auditStore.searchParams,
    start_date: startDate?.toISOString(),
    end_date: endDate?.toISOString(),
    page: 1
  }

  auditStore.fetchOperationLogList(params)
}

const handleAdvancedSearch = () => {
  const params: OperationLogSearchParams = {
    ...auditStore.searchParams,
    ...advancedSearchForm.value,
    start_date: advancedSearchForm.value.start_date ? new Date(advancedSearchForm.value.start_date).toISOString() : undefined,
    end_date: advancedSearchForm.value.end_date ? new Date(advancedSearchForm.value.end_date).toISOString() : undefined,
    page: 1
  }

  auditStore.fetchOperationLogList(params)
  auditStore.addSearchHistory('', params)
  showAdvancedSearch.value = false
}

const handleResetAdvancedSearch = () => {
  advancedSearchForm.value = {
    start_date: undefined,
    end_date: undefined,
    user_id: undefined,
    operation_type: undefined,
    method: undefined,
    ip_address: undefined,
    status_code: undefined
  }
}

const handleToggleRealTime = (enabled: boolean) => {
  auditStore.toggleRealTime()

  if (enabled) {
    message.success('实时监控已开启')
    // 这里可以启动WebSocket连接
  } else {
    message.info('实时监控已关闭')
    // 这里可以关闭WebSocket连接
  }
}

const handleRefresh = () => {
  auditStore.fetchOperationLogList()
}

const handleViewDetail = (logId: number) => {
  viewingLogId.value = logId
  detailDialogVisible.value = true
}

const handlePageChange = (page: number) => {
  auditStore.fetchOperationLogList({ ...auditStore.searchParams, page })
}

const handlePageSizeChange = (pageSize: number) => {
  auditStore.fetchOperationLogList({ ...auditStore.searchParams, page_size: pageSize, page: 1 })
}

const handleSort = (sorter: any) => {
  if (sorter) {
    const ordering = sorter.order === 'descend' ? `-${sorter.columnKey}` : sorter.columnKey
    auditStore.fetchOperationLogList({ ...auditStore.searchParams, ordering, page: 1 })
  }
}

const handleMoreAction = (key: string) => {
  switch (key) {
    case 'stats':
      showStatsDialog.value = true
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'cleanup':
      // 跳转到清理页面
      break
  }
}

const handleUserSearch = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }

  try {
    userLoading.value = true
    // 这里应该调用用户搜索API
    // const response = await userApi.searchUsers(query)
    // userOptions.value = response.data.map(user => ({
    //   label: `${user.nickname} (${user.username})`,
    //   value: user.id
    // }))
    userOptions.value = []
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

const handleLoadSavedSearch = (search: any) => {
  advancedSearchForm.value = { ...search.filters }
  handleAdvancedSearch()
}

const handleDeleteSavedSearch = (id: string) => {
  auditStore.deleteSavedSearch(id)
  message.success('已删除保存的搜索')
}

const handleLoadSearchHistory = (history: any) => {
  if (history.query) {
    searchKeyword.value = history.query
    handleSearch()
  } else {
    advancedSearchForm.value = { ...history.filters }
    handleAdvancedSearch()
  }
}

const handleExportSuccess = () => {
  showExportDialog.value = false
  message.success('导出任务已创建，请在导出管理中查看进度')
}

const handleSaveSearchSuccess = () => {
  showSaveSearchDialog.value = false
  message.success('搜索条件已保存')
}

// 生命周期
onMounted(async () => {
  // 加载本地数据
  auditStore.loadLocalData()

  // 获取操作日志列表
  await auditStore.fetchOperationLogList()

  // 获取操作日志统计
  await auditStore.fetchOperationLogStats()

  // 获取筛选选项
  await auditStore.fetchFilterOptions()
})

onUnmounted(() => {
  // 清理实时连接
  if (realTimeEnabled.value) {
    auditStore.toggleRealTime()
  }
})
</script>

<style scoped>
.audit-log-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px 20px;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  gap: 16px;
}

.search-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.search-input {
  width: 100%;
  max-width: 500px;
}

.quick-filters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-select {
  width: 150px;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.advanced-search-panel {
  padding: 16px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.w-full {
  width: 100%;
}

.saved-searches {
  padding: 12px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.search-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.search-tags .n-tag {
  cursor: pointer;
}

.table-container {
  flex: 1;
  padding: 0 24px 24px;
}

.audit-table {
  height: 100%;
}

/* 表格内容样式 */
.operation-desc {
  font-size: 14px;
  line-height: 1.4;
}

.operation-desc mark {
  background: #fff3cd;
  padding: 0 2px;
  border-radius: 2px;
}

.ip-address mark {
  background: #d1ecf1;
  padding: 0 2px;
  border-radius: 2px;
}

.response-time-fast {
  color: #52c41a;
  font-weight: 500;
}

.response-time-normal {
  color: #1890ff;
}

.response-time-slow {
  color: #faad14;
  font-weight: 500;
}

.response-time-very-slow {
  color: #ff4d4f;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .search-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-section {
    width: 100%;
  }

  .action-section {
    justify-content: flex-end;
  }

  .search-input {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }

  .stats-cards {
    flex-direction: column;
    gap: 12px;
  }

  .quick-filters {
    flex-direction: column;
  }

  .filter-select {
    width: 100%;
  }

  .action-section {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
