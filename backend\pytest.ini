[tool:pytest]
# pytest配置文件
DJANGO_SETTINGS_MODULE = config.settings.test
django_find_project = false
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
testpaths = .
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=apps
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --reuse-db
    --nomigrations
    --maxfail=5
    -p no:warnings

# 标记定义
markers =
    unit: 单元测试标记
    integration: 集成测试标记
    api: API测试标记
    auth: 认证相关测试标记
    permissions: 权限相关测试标记
    slow: 慢速测试标记
    external: 需要外部依赖的测试标记

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:django.*
    ignore::RuntimeWarning:django.*
