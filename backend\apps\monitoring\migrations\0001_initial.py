# Generated by Django 4.2.23 on 2025-08-08 08:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('cpu_usage', models.FloatField(verbose_name='CPU使用率(%)')),
                ('cpu_cores', models.IntegerField(verbose_name='CPU核心数')),
                ('memory_total', models.BigIntegerField(verbose_name='总内存(bytes)')),
                ('memory_used', models.BigIntegerField(verbose_name='已用内存(bytes)')),
                ('memory_usage', models.FloatField(verbose_name='内存使用率(%)')),
                ('disk_total', models.BigIntegerField(verbose_name='总磁盘空间(bytes)')),
                ('disk_used', models.BigIntegerField(verbose_name='已用磁盘空间(bytes)')),
                ('disk_usage', models.FloatField(verbose_name='磁盘使用率(%)')),
                ('network_bytes_sent', models.BigIntegerField(default=0, verbose_name='网络发送字节数')),
                ('network_bytes_recv', models.BigIntegerField(default=0, verbose_name='网络接收字节数')),
                ('load_average_1m', models.FloatField(blank=True, null=True, verbose_name='1分钟平均负载')),
                ('load_average_5m', models.FloatField(blank=True, null=True, verbose_name='5分钟平均负载')),
                ('load_average_15m', models.FloatField(blank=True, null=True, verbose_name='15分钟平均负载')),
                ('process_count', models.IntegerField(default=0, verbose_name='进程数量')),
                ('collected_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='采集时间')),
            ],
            options={
                'verbose_name': '系统性能指标',
                'verbose_name_plural': '系统性能指标',
                'db_table': 'monitoring_system_metrics',
                'ordering': ['-collected_at'],
                'indexes': [models.Index(fields=['collected_at'], name='monitoring__collect_e1ea73_idx'), models.Index(fields=['cpu_usage'], name='monitoring__cpu_usa_698911_idx'), models.Index(fields=['memory_usage'], name='monitoring__memory__c51791_idx'), models.Index(fields=['disk_usage'], name='monitoring__disk_us_8ee02d_idx')],
            },
        ),
        migrations.CreateModel(
            name='ServiceHealthCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('service_name', models.CharField(max_length=100, verbose_name='服务名称')),
                ('service_type', models.CharField(choices=[('database', '数据库'), ('cache', '缓存'), ('queue', '消息队列'), ('storage', '存储'), ('external_api', '外部API'), ('email', '邮件服务')], max_length=20, verbose_name='服务类型')),
                ('status', models.CharField(choices=[('healthy', '健康'), ('warning', '警告'), ('critical', '严重'), ('down', '宕机')], max_length=20, verbose_name='健康状态')),
                ('response_time', models.FloatField(blank=True, null=True, verbose_name='响应时间(ms)')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('details', models.JSONField(default=dict, verbose_name='检查详情')),
                ('checked_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='检查时间')),
            ],
            options={
                'verbose_name': '服务健康检查',
                'verbose_name_plural': '服务健康检查',
                'db_table': 'monitoring_service_health',
                'ordering': ['-checked_at'],
                'indexes': [models.Index(fields=['service_name', 'checked_at'], name='monitoring__service_59ea50_idx'), models.Index(fields=['service_type', 'status'], name='monitoring__service_79605e_idx'), models.Index(fields=['status', 'checked_at'], name='monitoring__status_b0a6df_idx')],
            },
        ),
        migrations.CreateModel(
            name='ApplicationMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('total_requests', models.BigIntegerField(default=0, verbose_name='总请求数')),
                ('successful_requests', models.BigIntegerField(default=0, verbose_name='成功请求数')),
                ('failed_requests', models.BigIntegerField(default=0, verbose_name='失败请求数')),
                ('avg_response_time', models.FloatField(default=0, verbose_name='平均响应时间(ms)')),
                ('min_response_time', models.FloatField(default=0, verbose_name='最小响应时间(ms)')),
                ('max_response_time', models.FloatField(default=0, verbose_name='最大响应时间(ms)')),
                ('p95_response_time', models.FloatField(default=0, verbose_name='95%响应时间(ms)')),
                ('p99_response_time', models.FloatField(default=0, verbose_name='99%响应时间(ms)')),
                ('error_rate', models.FloatField(default=0, verbose_name='错误率(%)')),
                ('active_users', models.IntegerField(default=0, verbose_name='活跃用户数')),
                ('db_connections', models.IntegerField(default=0, verbose_name='数据库连接数')),
                ('cache_hit_rate', models.FloatField(default=0, verbose_name='缓存命中率(%)')),
                ('queue_pending_tasks', models.IntegerField(default=0, verbose_name='队列待处理任务数')),
                ('queue_failed_tasks', models.IntegerField(default=0, verbose_name='队列失败任务数')),
                ('collected_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='采集时间')),
            ],
            options={
                'verbose_name': '应用性能指标',
                'verbose_name_plural': '应用性能指标',
                'db_table': 'monitoring_application_metrics',
                'ordering': ['-collected_at'],
                'indexes': [models.Index(fields=['collected_at'], name='monitoring__collect_9d4ca8_idx'), models.Index(fields=['error_rate'], name='monitoring__error_r_bc7a21_idx'), models.Index(fields=['avg_response_time'], name='monitoring__avg_res_4d1cc5_idx')],
            },
        ),
        migrations.CreateModel(
            name='AlertRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=100, verbose_name='规则名称')),
                ('description', models.TextField(blank=True, verbose_name='规则描述')),
                ('metric_type', models.CharField(choices=[('cpu_usage', 'CPU使用率'), ('memory_usage', '内存使用率'), ('disk_usage', '磁盘使用率'), ('error_rate', '错误率'), ('response_time', '响应时间'), ('active_users', '活跃用户数'), ('queue_tasks', '队列任务数')], max_length=20, verbose_name='监控指标')),
                ('operator', models.CharField(choices=[('gt', '大于'), ('gte', '大于等于'), ('lt', '小于'), ('lte', '小于等于'), ('eq', '等于'), ('ne', '不等于')], max_length=10, verbose_name='比较操作符')),
                ('threshold', models.FloatField(verbose_name='阈值')),
                ('severity', models.CharField(choices=[('info', '信息'), ('warning', '警告'), ('error', '错误'), ('critical', '严重')], max_length=20, verbose_name='严重程度')),
                ('duration', models.IntegerField(default=300, verbose_name='持续时间(秒)')),
                ('enable_email', models.BooleanField(default=True, verbose_name='启用邮件通知')),
                ('enable_webhook', models.BooleanField(default=False, verbose_name='启用Webhook通知')),
                ('webhook_url', models.URLField(blank=True, verbose_name='Webhook地址')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '告警规则',
                'verbose_name_plural': '告警规则',
                'db_table': 'monitoring_alert_rule',
                'indexes': [models.Index(fields=['metric_type', 'is_active'], name='monitoring__metric__2912eb_idx'), models.Index(fields=['severity'], name='monitoring__severit_de33eb_idx')],
            },
        ),
        migrations.CreateModel(
            name='AlertRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('status', models.CharField(choices=[('triggered', '已触发'), ('acknowledged', '已确认'), ('resolved', '已解决'), ('suppressed', '已抑制')], default='triggered', max_length=20, verbose_name='告警状态')),
                ('metric_value', models.FloatField(verbose_name='指标值')),
                ('message', models.TextField(verbose_name='告警消息')),
                ('details', models.JSONField(default=dict, verbose_name='告警详情')),
                ('triggered_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='触发时间')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True, verbose_name='确认时间')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('resolution_notes', models.TextField(blank=True, verbose_name='解决备注')),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='monitoring_acknowledged_alerts', to=settings.AUTH_USER_MODEL, verbose_name='确认人')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='monitoring_resolved_alerts', to=settings.AUTH_USER_MODEL, verbose_name='解决人')),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='monitoring.alertrule', verbose_name='告警规则')),
            ],
            options={
                'verbose_name': '告警记录',
                'verbose_name_plural': '告警记录',
                'db_table': 'monitoring_alert_record',
                'ordering': ['-triggered_at'],
                'indexes': [models.Index(fields=['rule', 'triggered_at'], name='monitoring__rule_id_d8a274_idx'), models.Index(fields=['status', 'triggered_at'], name='monitoring__status_915c0a_idx'), models.Index(fields=['triggered_at'], name='monitoring__trigger_421017_idx')],
            },
        ),
    ]
