/**
 * 用户管理相关类型定义
 */

import type { DepartmentInfo, Role } from './auth'

// 用户基础信息
export interface User {
  id: number
  username: string
  nickname: string
  email: string
  phone?: string
  wechat_work_id?: string
  avatar: string
  is_active: boolean
  is_staff: boolean
  is_superuser: boolean
  last_login_time?: string
  last_login_ip?: string
  login_fail_count: number
  locked_until?: string
  created_at: string
  updated_at: string
  deleted_at?: string
}

// 用户列表项（用于表格显示）
export interface UserListItem extends User {
  departments?: DepartmentInfo[]
  roles?: Role[]
  primary_department?: DepartmentInfo
}

// 用户创建表单
export interface UserCreateForm {
  username: string
  nickname: string
  email?: string
  phone?: string
  wechat_work_id?: string
  password: string
  confirm_password: string
  is_active: boolean
  is_staff: boolean
  department_ids: number[]
  role_ids: number[]
  primary_department_id?: number
}

// 用户编辑表单
export interface UserEditForm {
  nickname: string
  email?: string
  phone?: string
  wechat_work_id?: string
  is_active: boolean
  is_staff: boolean
  department_ids: number[]
  role_ids: number[]
  primary_department_id?: number
}

// 用户搜索参数
export interface UserSearchParams {
  search?: string
  department_id?: number
  role_id?: number
  is_active?: boolean
  is_staff?: boolean
  ordering?: string
  page?: number
  page_size?: number
}

// 密码重置表单
export interface PasswordResetForm {
  new_password: string
  confirm_password: string
}

// 密码修改表单
export interface ChangePasswordForm {
  old_password: string
  new_password: string
  confirm_password: string
}

// 个人资料表单
export interface ProfileForm {
  nickname: string
  email?: string
  phone?: string
  avatar?: string
}

// 批量操作类型
export type BatchOperationType = 'delete' | 'activate' | 'deactivate' | 'assign_role'

// 批量操作参数
export interface BatchOperationParams {
  user_ids: number[]
  operation: BatchOperationType
  role_id?: number // 用于批量分配角色
}

// 用户详情信息
export interface UserDetail extends User {
  departments: DepartmentInfo[]
  roles: Role[]
  primary_department?: DepartmentInfo
  permissions: string[]
  sessions: UserSession[]
  login_logs: LoginLog[]
}

// 用户会话信息
export interface UserSession {
  id: number
  session_key: string
  ip_address: string
  user_agent: string
  device_type: string
  browser: string
  os: string
  is_active: boolean
  last_activity: string
  expires_at: string
  created_at: string
}

// 登录日志
export interface LoginLog {
  id: number
  ip_address: string
  user_agent: string
  login_time: string
  login_result: 'SUCCESS' | 'FAILED'
  fail_reason?: string
  location?: string
}

// 用户状态统计
export interface UserStats {
  total_users: number
  active_users: number
  inactive_users: number
  locked_users: number
  online_users: number
}

// 表格列配置
export interface TableColumn {
  key: string
  title: string
  width?: number
  sortable?: boolean
  filterable?: boolean
  render?: (row: any) => any
}

// 分页信息
export interface PaginationInfo {
  page: number
  page_size: number
  total: number
  total_pages: number
}

// API响应类型
export interface UserListResponse {
  results: UserListItem[]
  count: number
  next?: string
  previous?: string
}

export interface UserDetailResponse {
  user: UserDetail
}

// 表单验证规则
export interface FormValidationRule {
  required?: boolean
  message?: string
  pattern?: RegExp
  min?: number
  max?: number
  validator?: (value: any) => boolean | string
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  LOCKED = 'locked'
}

// 用户操作权限
export interface UserPermissions {
  canView: boolean
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canResetPassword: boolean
  canToggleStatus: boolean
  canViewSessions: boolean
  canManageRoles: boolean
}
