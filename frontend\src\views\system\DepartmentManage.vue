<template>
  <div class="department-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">部门管理</h1>
          <p class="page-description">管理组织架构和部门信息</p>
        </div>

        <!-- 统计卡片 -->
        <div v-if="departmentStats" class="stats-cards">
          <div class="stat-card">
            <div class="stat-value">{{ departmentStats.total_departments }}</div>
            <div class="stat-label">总部门数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ departmentStats.active_departments }}</div>
            <div class="stat-label">活跃部门</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ departmentStats.total_members }}</div>
            <div class="stat-label">总成员数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ departmentStats.max_level }}</div>
            <div class="stat-label">最大层级</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <n-grid :cols="3" :x-gap="24">
        <!-- 左侧：部门树 -->
        <n-grid-item>
          <n-card title="组织架构" class="tree-card">
            <template #header-extra>
              <div class="tree-actions">
                <n-button
                  v-permission="'department:create'"
                  size="small"
                  type="primary"
                  @click="handleCreateRoot"
                >
                  <template #icon>
                    <n-icon><PlusIcon /></n-icon>
                  </template>
                  新增根部门
                </n-button>
                <n-button
                  size="small"
                  @click="handleRefreshTree"
                  :loading="treeLoading"
                >
                  <template #icon>
                    <n-icon><RefreshIcon /></n-icon>
                  </template>
                  刷新
                </n-button>
              </div>
            </template>

            <div class="tree-container">
              <n-input
                v-model:value="treeSearchKeyword"
                placeholder="搜索部门..."
                class="tree-search"
                clearable
              >
                <template #prefix>
                  <n-icon><SearchIcon /></n-icon>
                </template>
              </n-input>

              <n-tree
                :data="filteredTreeData"
                :expanded-keys="expandedKeys"
                :selected-keys="selectedKeys"
                :loading="treeLoading"
                key-field="id"
                label-field="name"
                children-field="children"
                block-line
                draggable
                @update:expanded-keys="handleExpandedKeysChange"
                @update:selected-keys="handleSelectedKeysChange"
                @drop="handleTreeDrop"
                class="department-tree"
              >
                <template #default="{ option }">
                  <div class="tree-node">
                    <div class="node-content">
                      <span class="node-name">{{ option.name }}</span>
                      <span class="node-code">({{ option.code }})</span>
                      <n-tag
                        v-if="!option.is_active"
                        type="error"
                        size="small"
                        class="node-status"
                      >
                        禁用
                      </n-tag>
                    </div>
                    <div class="node-info">
                      <span class="member-count">{{ option.member_count }}人</span>
                    </div>
                  </div>
                </template>

                <template #suffix="{ option }">
                  <div class="tree-node-actions">
                    <n-dropdown
                      :options="getNodeActionOptions(option)"
                      @select="(key) => handleNodeAction(key, option)"
                    >
                      <n-button size="tiny" quaternary>
                        <template #icon>
                          <n-icon><MoreIcon /></n-icon>
                        </template>
                      </n-button>
                    </n-dropdown>
                  </div>
                </template>
              </n-tree>
            </div>
          </n-card>
        </n-grid-item>

        <!-- 右侧：部门详情和成员管理 -->
        <n-grid-item :span="2">
          <div v-if="!selectedDepartmentId" class="empty-state">
            <n-empty description="请选择一个部门查看详情">
              <template #icon>
                <n-icon size="48"><FolderIcon /></n-icon>
              </template>
            </n-empty>
          </div>

          <div v-else class="detail-panels">
            <!-- 部门详情 -->
            <DepartmentDetailPanel
              :department-id="selectedDepartmentId"
              @edit="handleEdit"
              @delete="handleDelete"
              @toggle-status="handleToggleStatus"
            />

            <!-- 部门成员管理 -->
            <DepartmentMemberPanel
              :department-id="selectedDepartmentId"
              @refresh="handleRefreshDetail"
            />
          </div>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 部门表单对话框 -->
    <DepartmentFormDialog
      v-model:visible="formDialogVisible"
      :department="editingDepartment"
      :mode="formMode"
      :parent-id="parentDepartmentId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { useMessage, useDialog, type DropdownOption } from 'naive-ui'
import {
  PlusIcon,
  RefreshIcon,
  SearchIcon,
  MoreIcon,
  FolderIcon,
  EditIcon,
  DeleteIcon,
  EyeIcon,
  UsersIcon,
  SettingsIcon
} from '@vicons/tabler'
import { useDepartmentStore } from '@/stores/department'
import { hasPermission } from '@/utils/auth'
import type { DepartmentTree, DepartmentTreeNode } from '@/types/department'
import DepartmentFormDialog from '@/components/department/DepartmentFormDialog.vue'
import DepartmentDetailPanel from '@/components/department/DepartmentDetailPanel.vue'
import DepartmentMemberPanel from '@/components/department/DepartmentMemberPanel.vue'

// 状态管理
const departmentStore = useDepartmentStore()
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const treeSearchKeyword = ref('')
const formDialogVisible = ref(false)
const editingDepartment = ref<DepartmentTree | null>(null)
const formMode = ref<'create' | 'edit'>('create')
const parentDepartmentId = ref<number | null>(null)

// 计算属性
const departmentTree = computed(() => departmentStore.departmentTree)
const departmentStats = computed(() => departmentStore.departmentStats)
const treeLoading = computed(() => departmentStore.treeLoading)
const selectedDepartmentId = computed(() => departmentStore.selectedDepartmentId)

// 树形组件状态
const expandedKeys = ref<number[]>([])
const selectedKeys = ref<number[]>([])

// 过滤后的树形数据
const filteredTreeData = computed(() => {
  if (!treeSearchKeyword.value) {
    return departmentTree.value
  }

  const filterTree = (nodes: DepartmentTree[]): DepartmentTree[] => {
    return nodes.filter(node => {
      const matchesKeyword = node.name.toLowerCase().includes(treeSearchKeyword.value.toLowerCase()) ||
                           node.code.toLowerCase().includes(treeSearchKeyword.value.toLowerCase())

      if (matchesKeyword) {
        return true
      }

      if (node.children && node.children.length > 0) {
        const filteredChildren = filterTree(node.children)
        if (filteredChildren.length > 0) {
          return true
        }
      }

      return false
    }).map(node => ({
      ...node,
      children: node.children ? filterTree(node.children) : []
    }))
  }

  return filterTree(departmentTree.value)
})

// 获取节点操作选项
const getNodeActionOptions = (node: DepartmentTree): DropdownOption[] => {
  const options: DropdownOption[] = []

  // 查看详情
  if (hasPermission('department:view')) {
    options.push({
      label: '查看详情',
      key: 'view',
      icon: () => h(EyeIcon)
    })
  }

  // 新增子部门
  if (hasPermission('department:create')) {
    options.push({
      label: '新增子部门',
      key: 'create_child',
      icon: () => h(PlusIcon)
    })
  }

  // 编辑
  if (hasPermission('department:update')) {
    options.push({
      label: '编辑部门',
      key: 'edit',
      icon: () => h(EditIcon)
    })
  }

  // 成员管理
  if (hasPermission('department:manage_members')) {
    options.push({
      label: '成员管理',
      key: 'manage_members',
      icon: () => h(UsersIcon)
    })
  }

  // 分隔线
  if (options.length > 0) {
    options.push({ type: 'divider' })
  }

  // 启用/禁用
  if (hasPermission('department:update')) {
    options.push({
      label: node.is_active ? '禁用部门' : '启用部门',
      key: 'toggle_status',
      icon: () => h(SettingsIcon)
    })
  }

  // 删除
  if (hasPermission('department:delete')) {
    options.push({
      label: '删除部门',
      key: 'delete',
      icon: () => h(DeleteIcon)
    })
  }

  return options
}

// 事件处理方法
const handleExpandedKeysChange = (keys: number[]) => {
  expandedKeys.value = keys
}

const handleSelectedKeysChange = (keys: number[]) => {
  selectedKeys.value = keys
  if (keys.length > 0) {
    departmentStore.selectDepartment(keys[0])
  } else {
    departmentStore.clearSelection()
  }
}

const handleCreateRoot = () => {
  editingDepartment.value = null
  formMode.value = 'create'
  parentDepartmentId.value = null
  formDialogVisible.value = true
}

const handleRefreshTree = async () => {
  try {
    await departmentStore.fetchDepartmentTree()
    message.success('刷新成功')
  } catch (error) {
    message.error('刷新失败')
  }
}

const handleNodeAction = async (key: string, node: DepartmentTree) => {
  switch (key) {
    case 'view':
      departmentStore.selectDepartment(node.id)
      selectedKeys.value = [node.id]
      break

    case 'create_child':
      editingDepartment.value = null
      formMode.value = 'create'
      parentDepartmentId.value = node.id
      formDialogVisible.value = true
      break

    case 'edit':
      editingDepartment.value = node
      formMode.value = 'edit'
      parentDepartmentId.value = null
      formDialogVisible.value = true
      break

    case 'manage_members':
      departmentStore.selectDepartment(node.id)
      selectedKeys.value = [node.id]
      // 可以添加滚动到成员管理面板的逻辑
      break

    case 'toggle_status':
      await handleToggleStatus(node.id)
      break

    case 'delete':
      await handleDelete(node.id)
      break
  }
}

const handleEdit = (department: any) => {
  editingDepartment.value = department
  formMode.value = 'edit'
  parentDepartmentId.value = null
  formDialogVisible.value = true
}

const handleDelete = async (departmentId: number) => {
  dialog.error({
    title: '确认删除部门',
    content: '确定要删除该部门吗？删除后将无法恢复，且会影响该部门下的所有子部门和成员。',
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await departmentStore.deleteDepartment(departmentId)
        message.success('部门删除成功')

        // 如果删除的是当前选中的部门，清空选择
        if (selectedDepartmentId.value === departmentId) {
          selectedKeys.value = []
          departmentStore.clearSelection()
        }
      } catch (error) {
        message.error('部门删除失败')
      }
    }
  })
}

const handleToggleStatus = async (departmentId: number) => {
  try {
    await departmentStore.toggleDepartmentActive(departmentId)
    message.success('部门状态切换成功')
  } catch (error) {
    message.error('部门状态切换失败')
  }
}

const handleTreeDrop = async ({ node, dragNode, dropPosition }: any) => {
  try {
    // 这里实现拖拽逻辑
    const moveData = {
      department_id: dragNode.id,
      target_parent_id: dropPosition === 'inside' ? node.id : node.parent?.id,
      position: dropPosition,
      target_id: dropPosition !== 'inside' ? node.id : undefined
    }

    await departmentStore.moveDepartment(moveData)
    message.success('部门移动成功')

    // 刷新树形结构
    await departmentStore.fetchDepartmentTree()
  } catch (error) {
    message.error('部门移动失败')
  }
}

const handleFormSuccess = () => {
  formDialogVisible.value = false
  message.success(formMode.value === 'create' ? '部门创建成功' : '部门更新成功')
}

const handleRefreshDetail = () => {
  // 刷新部门详情和成员信息
  if (selectedDepartmentId.value) {
    departmentStore.fetchDepartmentDetail(selectedDepartmentId.value)
  }
}

// 生命周期
onMounted(async () => {
  // 获取部门树形结构
  await departmentStore.fetchDepartmentTree()

  // 获取部门统计
  await departmentStore.fetchDepartmentStats()

  // 默认展开第一级
  if (departmentTree.value.length > 0) {
    expandedKeys.value = departmentTree.value.map(dept => dept.id)
  }
})

// 监听选中的部门变化
watch(selectedDepartmentId, (newId) => {
  if (newId && !selectedKeys.value.includes(newId)) {
    selectedKeys.value = [newId]
  }
})
</script>

<style scoped>
.department-manage-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px 20px;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.main-content {
  flex: 1;
  padding: 0 24px 24px;
  overflow: hidden;
}

.tree-card {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

.tree-actions {
  display: flex;
  gap: 8px;
}

.tree-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tree-search {
  margin-bottom: 16px;
}

.department-tree {
  flex: 1;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-name {
  font-weight: 500;
  color: #1f2937;
}

.node-code {
  font-size: 12px;
  color: #6b7280;
}

.node-status {
  margin-left: 4px;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-count {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.tree-node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .tree-node-actions {
  opacity: 1;
}

.empty-state {
  height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-panels {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .main-content {
    padding: 0 16px 16px;
  }

  .tree-card {
    height: 400px;
  }

  .detail-panels {
    height: auto;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }

  .stats-cards {
    flex-direction: column;
    gap: 12px;
  }

  .tree-actions {
    flex-direction: column;
    gap: 8px;
  }

  .tree-card {
    height: 300px;
  }
}
</style>
