<template>
  <div class="breadcrumb-container">
    <n-breadcrumb>
      <n-breadcrumb-item 
        v-for="(item, index) in breadcrumbItems" 
        :key="item.path"
        :clickable="index < breadcrumbItems.length - 1"
        @click="handleBreadcrumbClick(item, index)"
      >
        <template #icon v-if="item.icon">
          <span>{{ getIconText(item.icon) }}</span>
        </template>
        {{ item.title }}
      </n-breadcrumb-item>
    </n-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMenuStore } from '@/stores/menu'
import type { MenuItem } from '@/stores/menu'

const router = useRouter()
const menuStore = useMenuStore()

// 面包屑项目
const breadcrumbItems = computed(() => {
  const breadcrumbs = menuStore.breadcrumbs
  
  // 如果没有面包屑或只有一级，添加首页
  if (breadcrumbs.length === 0) {
    return [{ path: '/', title: '首页', icon: 'home' }]
  }
  
  // 如果第一个不是首页，添加首页
  if (breadcrumbs[0].path !== '/') {
    return [
      { path: '/', title: '首页', icon: 'home' },
      ...breadcrumbs
    ]
  }
  
  return breadcrumbs
})

// 获取图标文本
function getIconText(iconName: string): string {
  const iconMap: Record<string, string> = {
    'home': '🏠',
    'setting': '⚙️',
    'user': '👤',
    'team': '👥',
    'key': '🔑',
    'apartment': '🏢',
    'info': 'ℹ️'
  }
  
  return iconMap[iconName] || '📄'
}

// 处理面包屑点击
function handleBreadcrumbClick(item: MenuItem, index: number) {
  // 只有非最后一项才可以点击
  if (index < breadcrumbItems.value.length - 1) {
    router.push(item.path)
  }
}
</script>

<style scoped>
.breadcrumb-container {
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
}
</style>
