# Generated by Django 4.2.23 on 2025-08-12 07:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('departments', '0003_alter_userdepartment_effective_date'),
        ('users', '0002_alter_userprofile_managers'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserDepartment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主部门')),
                ('is_manager', models.BooleanField(default=False, verbose_name='是否部门负责人')),
                ('position', models.CharField(blank=True, max_length=100, verbose_name='职位')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='开始日期')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='结束日期')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='department_users', to='departments.department', verbose_name='部门')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_departments', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户部门关联',
                'verbose_name_plural': '用户部门关联',
                'db_table': 'user_department',
                'indexes': [models.Index(fields=['user', 'is_active'], name='user_depart_user_id_eaa2dc_idx'), models.Index(fields=['department', 'is_active'], name='user_depart_departm_69e8a1_idx'), models.Index(fields=['is_primary', 'is_active'], name='user_depart_is_prim_29492e_idx')],
                'unique_together': {('user', 'department')},
            },
        ),
    ]
