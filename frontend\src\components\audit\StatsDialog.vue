<template>
  <n-modal
    :show="visible"
    preset="dialog"
    title="操作日志统计"
    class="stats-dialog"
    style="width: 1200px"
    @update:show="$emit('update:visible', $event)"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else class="stats-content">
      <!-- 统计概览 -->
      <div class="stats-overview">
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <div class="stat-card">
              <div class="stat-icon">
                <n-icon size="24" color="#1890ff"><ChartBarIcon /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ operationLogStats?.total_count.toLocaleString() || 0 }}</div>
                <div class="stat-label">总操作数</div>
              </div>
            </div>
          </n-grid-item>
          
          <n-grid-item>
            <div class="stat-card">
              <div class="stat-icon">
                <n-icon size="24" color="#52c41a"><TrendingUpIcon /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ operationLogStats?.today_count.toLocaleString() || 0 }}</div>
                <div class="stat-label">今日操作</div>
              </div>
            </div>
          </n-grid-item>
          
          <n-grid-item>
            <div class="stat-card">
              <div class="stat-icon">
                <n-icon size="24" color="#faad14"><UserIcon /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ operationLogStats?.login_count.toLocaleString() || 0 }}</div>
                <div class="stat-label">登录次数</div>
              </div>
            </div>
          </n-grid-item>
          
          <n-grid-item>
            <div class="stat-card">
              <div class="stat-icon">
                <n-icon size="24" color="#f5222d"><AlertTriangleIcon /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ errorCount }}</div>
                <div class="stat-label">错误操作</div>
              </div>
            </div>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <n-grid :cols="2" :x-gap="16" :y-gap="16">
          <!-- 操作类型分布 -->
          <n-grid-item>
            <n-card title="操作类型分布" class="chart-card">
              <div ref="operationTypePieRef" class="chart-container"></div>
            </n-card>
          </n-grid-item>
          
          <!-- 24小时操作趋势 -->
          <n-grid-item>
            <n-card title="24小时操作趋势" class="chart-card">
              <div ref="hourlyTrendRef" class="chart-container"></div>
            </n-card>
          </n-grid-item>
          
          <!-- 用户活跃度排行 -->
          <n-grid-item>
            <n-card title="用户活跃度排行" class="chart-card">
              <div ref="userRankingRef" class="chart-container"></div>
            </n-card>
          </n-grid-item>
          
          <!-- 响应时间分布 -->
          <n-grid-item>
            <n-card title="响应时间分布" class="chart-card">
              <div ref="responseTimeRef" class="chart-container"></div>
            </n-card>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 详细统计表格 -->
      <div class="detailed-stats">
        <n-tabs type="line">
          <n-tab-pane name="operation-types" tab="操作类型统计">
            <n-data-table
              :columns="operationTypeColumns"
              :data="operationTypeData"
              :pagination="false"
              size="small"
            />
          </n-tab-pane>
          
          <n-tab-pane name="user-stats" tab="用户统计">
            <n-data-table
              :columns="userStatsColumns"
              :data="userStatsData"
              :pagination="{ pageSize: 10 }"
              size="small"
            />
          </n-tab-pane>
          
          <n-tab-pane name="ip-stats" tab="IP统计">
            <n-data-table
              :columns="ipStatsColumns"
              :data="ipStatsData"
              :pagination="{ pageSize: 10 }"
              size="small"
            />
          </n-tab-pane>
        </n-tabs>
      </div>
    </div>
    
    <template #action>
      <n-space>
        <n-button @click="handleRefresh" :loading="loading">
          <template #icon>
            <n-icon><RefreshIcon /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button @click="handleExportStats">
          <template #icon>
            <n-icon><DownloadIcon /></n-icon>
          </template>
          导出统计
        </n-button>
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useMessage, type DataTableColumns } from 'naive-ui'
import { 
  ChartBarIcon, 
  TrendingUpIcon, 
  UserIcon, 
  AlertTriangleIcon,
  RefreshIcon,
  DownloadIcon
} from '@vicons/tabler'
import * as echarts from 'echarts'
import { useAuditStore } from '@/stores/audit'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 状态管理
const auditStore = useAuditStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)

// 图表引用
const operationTypePieRef = ref<HTMLElement>()
const hourlyTrendRef = ref<HTMLElement>()
const userRankingRef = ref<HTMLElement>()
const responseTimeRef = ref<HTMLElement>()

// 图表实例
let operationTypePieChart: echarts.ECharts | null = null
let hourlyTrendChart: echarts.ECharts | null = null
let userRankingChart: echarts.ECharts | null = null
let responseTimeChart: echarts.ECharts | null = null

// 计算属性
const operationLogStats = computed(() => auditStore.operationLogStats)

const errorCount = computed(() => {
  if (!operationLogStats.value?.operation_type_stats) return 0
  return operationLogStats.value.operation_type_stats['ERROR'] || 0
})

// 操作类型统计表格数据
const operationTypeData = computed(() => {
  if (!operationLogStats.value?.operation_type_stats) return []
  
  return Object.entries(operationLogStats.value.operation_type_stats).map(([type, count]) => ({
    type,
    type_display: getOperationTypeLabel(type),
    count,
    percentage: ((count / operationLogStats.value!.total_count) * 100).toFixed(1)
  }))
})

const operationTypeColumns: DataTableColumns = [
  { title: '操作类型', key: 'type_display' },
  { title: '数量', key: 'count', sorter: true },
  { title: '占比', key: 'percentage', render: (row: any) => `${row.percentage}%` }
]

// 用户统计表格数据
const userStatsData = computed(() => {
  if (!operationLogStats.value?.user_stats) return []
  
  return operationLogStats.value.user_stats.map(user => ({
    ...user,
    percentage: ((user.count / operationLogStats.value!.total_count) * 100).toFixed(1)
  }))
})

const userStatsColumns: DataTableColumns = [
  { title: '用户', key: 'user_nickname' },
  { title: '操作数', key: 'count', sorter: true },
  { title: '占比', key: 'percentage', render: (row: any) => `${row.percentage}%` }
]

// IP统计表格数据（模拟）
const ipStatsData = ref([
  { ip: '*************', count: 150, location: '内网' },
  { ip: '*********', count: 89, location: '内网' },
  { ip: '***********', count: 45, location: '内网' }
])

const ipStatsColumns: DataTableColumns = [
  { title: 'IP地址', key: 'ip' },
  { title: '操作数', key: 'count', sorter: true },
  { title: '位置', key: 'location' }
]

// 方法
const getOperationTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    'LOGIN': '登录',
    'LOGOUT': '登出',
    'CREATE': '创建',
    'UPDATE': '更新',
    'DELETE': '删除',
    'QUERY': '查询',
    'ERROR': '系统异常'
  }
  return labels[type] || type
}

const initCharts = () => {
  nextTick(() => {
    initOperationTypePieChart()
    initHourlyTrendChart()
    initUserRankingChart()
    initResponseTimeChart()
  })
}

const initOperationTypePieChart = () => {
  if (!operationTypePieRef.value || !operationLogStats.value) return
  
  operationTypePieChart = echarts.init(operationTypePieRef.value)
  
  const data = Object.entries(operationLogStats.value.operation_type_stats || {}).map(([type, count]) => ({
    name: getOperationTypeLabel(type),
    value: count
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '操作类型',
        type: 'pie',
        radius: '50%',
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  operationTypePieChart.setOption(option)
}

const initHourlyTrendChart = () => {
  if (!hourlyTrendRef.value || !operationLogStats.value) return
  
  hourlyTrendChart = echarts.init(hourlyTrendRef.value)
  
  const hours = Array.from({ length: 24 }, (_, i) => i)
  const data = operationLogStats.value.hourly_stats || []
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: hours.map(h => `${h}:00`)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '操作数',
        type: 'line',
        data: hours.map(hour => {
          const stat = data.find(s => s.hour === hour)
          return stat ? stat.count : 0
        }),
        smooth: true,
        areaStyle: {}
      }
    ]
  }
  
  hourlyTrendChart.setOption(option)
}

const initUserRankingChart = () => {
  if (!userRankingRef.value || !operationLogStats.value) return
  
  userRankingChart = echarts.init(userRankingRef.value)
  
  const data = operationLogStats.value.user_stats?.slice(0, 10) || []
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: data.map(user => user.user_nickname)
    },
    series: [
      {
        name: '操作数',
        type: 'bar',
        data: data.map(user => user.count)
      }
    ]
  }
  
  userRankingChart.setOption(option)
}

const initResponseTimeChart = () => {
  if (!responseTimeRef.value) return
  
  responseTimeChart = echarts.init(responseTimeRef.value)
  
  // 模拟响应时间分布数据
  const data = [
    { name: '< 100ms', value: 45 },
    { name: '100-500ms', value: 30 },
    { name: '500ms-1s', value: 15 },
    { name: '1-3s', value: 8 },
    { name: '> 3s', value: 2 }
  ]
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '响应时间',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  }
  
  responseTimeChart.setOption(option)
}

const resizeCharts = () => {
  operationTypePieChart?.resize()
  hourlyTrendChart?.resize()
  userRankingChart?.resize()
  responseTimeChart?.resize()
}

const destroyCharts = () => {
  operationTypePieChart?.dispose()
  hourlyTrendChart?.dispose()
  userRankingChart?.dispose()
  responseTimeChart?.dispose()
  
  operationTypePieChart = null
  hourlyTrendChart = null
  userRankingChart = null
  responseTimeChart = null
}

const handleRefresh = async () => {
  try {
    loading.value = true
    await auditStore.fetchOperationLogStats()
    
    // 重新初始化图表
    destroyCharts()
    initCharts()
    
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const handleExportStats = () => {
  message.info('统计导出功能开发中')
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (!operationLogStats.value) {
        auditStore.fetchOperationLogStats().then(() => {
          initCharts()
        })
      } else {
        initCharts()
      }
    })
  } else {
    destroyCharts()
  }
})

// 生命周期
onMounted(() => {
  window.addEventListener('resize', resizeCharts)
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeCharts)
  destroyCharts()
})
</script>

<style scoped>
.stats-dialog {
  max-height: 90vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.stat-card:hover {
  background: #f3f4f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.charts-container {
  margin-bottom: 24px;
}

.chart-card {
  height: 300px;
}

.chart-container {
  width: 100%;
  height: 240px;
}

.detailed-stats {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}
</style>
