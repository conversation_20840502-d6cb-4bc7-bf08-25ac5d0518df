<template>
  <div class="permission-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">权限管理</h1>
          <p class="page-description">管理系统权限和权限层级关系</p>
        </div>

        <!-- 统计卡片 -->
        <div v-if="permissionStats" class="stats-cards">
          <div class="stat-card">
            <div class="stat-value">{{ permissionStats.total_permissions }}</div>
            <div class="stat-label">总权限数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ permissionStats.menu_permissions }}</div>
            <div class="stat-label">菜单权限</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ permissionStats.button_permissions }}</div>
            <div class="stat-label">按钮权限</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ permissionStats.api_permissions }}</div>
            <div class="stat-label">API权限</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 操作栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <!-- 搜索框 -->
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索权限名称、编码..."
            class="search-input"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
          </n-input>

          <!-- 筛选器 -->
          <n-select
            v-model:value="filterType"
            placeholder="权限类型"
            class="filter-select"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="MENU" label="菜单权限" />
            <n-option value="BUTTON" label="按钮权限" />
            <n-option value="API" label="API权限" />
          </n-select>

          <n-select
            v-model:value="filterStatus"
            placeholder="权限状态"
            class="filter-select"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="true" label="已激活" />
            <n-option value="false" label="已禁用" />
          </n-select>

          <!-- 视图切换 -->
          <n-button-group>
            <n-button
              :type="viewMode === 'tree' ? 'primary' : 'default'"
              @click="viewMode = 'tree'"
            >
              <template #icon>
                <n-icon><TreeIcon /></n-icon>
              </template>
              树形视图
            </n-button>
            <n-button
              :type="viewMode === 'table' ? 'primary' : 'default'"
              @click="viewMode = 'table'"
            >
              <template #icon>
                <n-icon><TableIcon /></n-icon>
              </template>
              表格视图
            </n-button>
          </n-button-group>
        </div>

        <div class="toolbar-right">
          <!-- 批量操作 -->
          <n-dropdown
            v-if="hasSelectedPermissions"
            :options="batchOptions"
            @select="handleBatchOperation"
          >
            <n-button>
              批量操作 ({{ selectedPermissionsCount }})
              <template #icon>
                <n-icon><ChevronDownIcon /></n-icon>
              </template>
            </n-button>
          </n-dropdown>

          <!-- 角色管理按钮 -->
          <n-button
            v-permission="'role:manage'"
            @click="handleManageRoles"
          >
            <template #icon>
              <n-icon><UsersIcon /></n-icon>
            </template>
            角色管理
          </n-button>

          <!-- 新增权限按钮 -->
          <n-button
            v-permission="'permission:create'"
            type="primary"
            @click="handleCreate"
          >
            <template #icon>
              <n-icon><PlusIcon /></n-icon>
            </template>
            新增权限
          </n-button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 树形视图 -->
        <div v-if="viewMode === 'tree'" class="tree-view">
          <div class="tree-toolbar">
            <n-space>
              <n-button size="small" @click="handleExpandAll">
                全部展开
              </n-button>
              <n-button size="small" @click="handleCollapseAll">
                全部折叠
              </n-button>
            </n-space>
          </div>

          <div class="tree-container">
            <n-tree
              :data="filteredPermissionTree"
              :expanded-keys="expandedKeys"
              :loading="loading"
              key-field="id"
              label-field="name"
              children-field="children"
              block-line
              @update:expanded-keys="handleExpandedKeysChange"
              class="permission-tree"
            >
              <template #default="{ option }">
                <div class="permission-node">
                  <div class="node-content">
                    <n-icon
                      v-if="option.icon"
                      :component="getPermissionIcon(option.icon)"
                      class="permission-icon"
                    />
                    <span class="permission-name">{{ option.name }}</span>
                    <n-tag
                      :type="getPermissionTypeColor(option.permission_type)"
                      size="small"
                      class="permission-type"
                    >
                      {{ getPermissionTypeLabel(option.permission_type) }}
                    </n-tag>
                    <span v-if="option.code" class="permission-code">{{ option.code }}</span>
                    <n-tag
                      :type="option.is_active ? 'success' : 'error'"
                      size="small"
                      class="permission-status"
                    >
                      {{ option.is_active ? '正常' : '禁用' }}
                    </n-tag>
                  </div>

                  <div class="node-actions">
                    <n-button
                      v-permission="'permission:view'"
                      size="small"
                      type="primary"
                      text
                      @click="handleView(option)"
                    >
                      查看
                    </n-button>
                    <n-button
                      v-permission="'permission:update'"
                      size="small"
                      type="primary"
                      text
                      @click="handleEdit(option)"
                    >
                      编辑
                    </n-button>
                    <n-button
                      v-permission="'permission:create'"
                      size="small"
                      type="info"
                      text
                      @click="handleCreateChild(option)"
                    >
                      添加子权限
                    </n-button>
                    <n-button
                      v-permission="'permission:delete'"
                      size="small"
                      type="error"
                      text
                      @click="handleDelete(option)"
                    >
                      删除
                    </n-button>
                  </div>
                </div>
              </template>
            </n-tree>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-else class="table-view">
          <n-data-table
            :columns="tableColumns"
            :data="permissionList"
            :loading="loading"
            :pagination="paginationConfig"
            :row-key="getRowKey"
            :checked-row-keys="selectedPermissionIds"
            @update:checked-row-keys="handleSelectionChange"
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
            @update:sorter="handleSort"
            striped
            flex-height
            class="permission-table"
          />
        </div>
      </div>
    </div>

    <!-- 权限表单对话框 -->
    <PermissionFormDialog
      v-model:visible="formDialogVisible"
      :permission="editingPermission"
      :mode="formMode"
      :parent-id="parentPermissionId"
      @success="handleFormSuccess"
    />

    <!-- 权限详情对话框 -->
    <PermissionDetailDialog
      v-model:visible="detailDialogVisible"
      :permission-id="viewingPermissionId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage, useDialog, type DataTableColumns, type DropdownOption } from 'naive-ui'
import { useRouter } from 'vue-router'
import {
  SearchIcon,
  PlusIcon,
  ChevronDownIcon,
  UsersIcon,
  TreeIcon,
  TableIcon,
  EditIcon,
  DeleteIcon,
  EyeIcon,
  CheckIcon,
  CloseIcon
} from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { hasPermission } from '@/utils/auth'
import type { Permission, PermissionType } from '@/types/role'
import PermissionFormDialog from '@/components/role/PermissionFormDialog.vue'
import PermissionDetailDialog from '@/components/role/PermissionDetailDialog.vue'

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()
const dialog = useDialog()
const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const filterType = ref<PermissionType | null>(null)
const filterStatus = ref<string | null>(null)
const viewMode = ref<'tree' | 'table'>('tree')
const expandedKeys = ref<number[]>([])

// 对话框状态
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)

const editingPermission = ref<Permission | null>(null)
const formMode = ref<'create' | 'edit'>('create')
const parentPermissionId = ref<number | null>(null)
const viewingPermissionId = ref<number | null>(null)

// 计算属性
const permissionList = computed(() => roleStore.permissionList)
const permissionTree = computed(() => roleStore.permissionTree)
const loading = computed(() => roleStore.permissionLoading)
const selectedPermissionIds = computed(() => roleStore.selectedPermissionIds)
const hasSelectedPermissions = computed(() => roleStore.hasSelectedPermissions)
const selectedPermissionsCount = computed(() => roleStore.selectedPermissionsCount)
const permissionStats = computed(() => roleStore.permissionStats)

// 过滤后的权限树
const filteredPermissionTree = computed(() => {
  let tree = permissionTree.value

  // 根据搜索关键词过滤
  if (searchKeyword.value) {
    const filterTree = (nodes: any[]): any[] => {
      return nodes.filter(node => {
        const matchesKeyword = node.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                             node.code.toLowerCase().includes(searchKeyword.value.toLowerCase())

        if (matchesKeyword) {
          return true
        }

        if (node.children && node.children.length > 0) {
          const filteredChildren = filterTree(node.children)
          if (filteredChildren.length > 0) {
            node.children = filteredChildren
            return true
          }
        }

        return false
      })
    }

    tree = filterTree(JSON.parse(JSON.stringify(tree)))
  }

  // 根据类型过滤
  if (filterType.value) {
    const filterByType = (nodes: any[]): any[] => {
      return nodes.filter(node => {
        if (node.permission_type === filterType.value) {
          return true
        }

        if (node.children && node.children.length > 0) {
          const filteredChildren = filterByType(node.children)
          if (filteredChildren.length > 0) {
            node.children = filteredChildren
            return true
          }
        }

        return false
      })
    }

    tree = filterByType(tree)
  }

  // 根据状态过滤
  if (filterStatus.value !== null) {
    const isActive = filterStatus.value === 'true'
    const filterByStatus = (nodes: any[]): any[] => {
      return nodes.filter(node => {
        if (node.is_active === isActive) {
          return true
        }

        if (node.children && node.children.length > 0) {
          const filteredChildren = filterByStatus(node.children)
          if (filteredChildren.length > 0) {
            node.children = filteredChildren
            return true
          }
        }

        return false
      })
    }

    tree = filterByStatus(tree)
  }

  return tree
})

// 分页配置
const paginationConfig = computed(() => ({
  page: roleStore.permissionPagination.page,
  pageSize: roleStore.permissionPagination.page_size,
  itemCount: roleStore.permissionPagination.total,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}))

// 表格行键
const getRowKey = (row: Permission) => row.id

// 批量操作选项
const batchOptions: DropdownOption[] = [
  {
    label: '批量激活',
    key: 'activate',
    disabled: !hasPermission('permission:update')
  },
  {
    label: '批量禁用',
    key: 'deactivate',
    disabled: !hasPermission('permission:update')
  },
  {
    label: '批量删除',
    key: 'delete',
    disabled: !hasPermission('permission:delete')
  }
]

// 权限类型标签和颜色
const getPermissionTypeColor = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return 'info'
    case 'BUTTON':
      return 'warning'
    case 'API':
      return 'success'
    default:
      return 'default'
  }
}

const getPermissionTypeLabel = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return '菜单'
    case 'BUTTON':
      return '按钮'
    case 'API':
      return 'API'
    default:
      return type
  }
}

const getPermissionIcon = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  return SearchIcon // 临时使用搜索图标
}

// 表格列配置
const tableColumns: DataTableColumns<Permission> = [
  {
    type: 'selection'
  },
  {
    title: '权限名称',
    key: 'name',
    width: 200,
    sorter: true,
    render: (row: Permission) => h('span', { class: 'font-medium' }, row.name)
  },
  {
    title: '权限编码',
    key: 'code',
    width: 150,
    sorter: true,
    render: (row: Permission) => h('code', { class: 'text-sm bg-gray-100 px-2 py-1 rounded' }, row.code)
  },
  {
    title: '权限类型',
    key: 'permission_type',
    width: 100,
    render: (row: Permission) => h(
      'n-tag',
      { size: 'small', type: getPermissionTypeColor(row.permission_type) },
      getPermissionTypeLabel(row.permission_type)
    )
  },
  {
    title: '父权限',
    key: 'parent_name',
    width: 150,
    render: (row: Permission) => row.parent_name || '-'
  },
  {
    title: '路径',
    key: 'path',
    ellipsis: {
      tooltip: true
    },
    render: (row: Permission) => row.path || '-'
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row: Permission) => h(
      'n-tag',
      {
        type: row.is_active ? 'success' : 'error',
        size: 'small'
      },
      row.is_active ? '正常' : '禁用'
    )
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: Permission) => {
      const actions = []

      // 查看详情
      if (hasPermission('permission:view')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleView(row)
          }, { default: () => '查看', icon: () => h(EyeIcon) })
        )
      }

      // 编辑
      if (hasPermission('permission:update')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleEdit(row)
          }, { default: () => '编辑', icon: () => h(EditIcon) })
        )
      }

      // 删除
      if (hasPermission('permission:delete')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'error',
            text: true,
            onClick: () => handleDelete(row)
          }, { default: () => '删除', icon: () => h(DeleteIcon) })
        )
      }

      return h('div', { class: 'flex gap-1 flex-wrap' }, actions)
    }
  }
]

// 事件处理方法
const handleSearch = () => {
  if (viewMode.value === 'table') {
    roleStore.fetchPermissionList({
      ...roleStore.permissionSearchParams,
      search: searchKeyword.value,
      page: 1
    })
  }
}

const handleFilter = () => {
  const filters: any = {}

  if (filterType.value) {
    filters.permission_type = filterType.value
  }

  if (filterStatus.value !== null) {
    filters.is_active = filterStatus.value === 'true'
  }

  if (viewMode.value === 'table') {
    roleStore.fetchPermissionList({
      ...roleStore.permissionSearchParams,
      ...filters,
      page: 1
    })
  }
}

const handleCreate = () => {
  editingPermission.value = null
  parentPermissionId.value = null
  formMode.value = 'create'
  formDialogVisible.value = true
}

const handleCreateChild = (parent: Permission) => {
  editingPermission.value = null
  parentPermissionId.value = parent.id
  formMode.value = 'create'
  formDialogVisible.value = true
}

const handleEdit = (permission: Permission) => {
  editingPermission.value = permission
  parentPermissionId.value = null
  formMode.value = 'edit'
  formDialogVisible.value = true
}

const handleView = (permission: Permission) => {
  viewingPermissionId.value = permission.id
  detailDialogVisible.value = true
}

const handleDelete = (permission: Permission) => {
  dialog.error({
    title: '确认删除权限',
    content: `确定要删除权限 "${permission.name}" 吗？此操作不可恢复！`,
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await roleStore.deletePermission(permission.id)
        message.success('权限删除成功')
      } catch (error) {
        message.error('权限删除失败')
      }
    }
  })
}

const handleManageRoles = () => {
  router.push('/system/roles')
}

const handleExpandAll = () => {
  const getAllKeys = (nodes: any[]): number[] => {
    let keys: number[] = []
    nodes.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }

  expandedKeys.value = getAllKeys(permissionTree.value)
}

const handleCollapseAll = () => {
  expandedKeys.value = []
}

const handleExpandedKeysChange = (keys: number[]) => {
  expandedKeys.value = keys
}

const handleSelectionChange = (keys: Array<string | number>) => {
  roleStore.selectedPermissionIds = keys as number[]
}

const handlePageChange = (page: number) => {
  roleStore.fetchPermissionList({ ...roleStore.permissionSearchParams, page })
}

const handlePageSizeChange = (pageSize: number) => {
  roleStore.fetchPermissionList({ ...roleStore.permissionSearchParams, page_size: pageSize, page: 1 })
}

const handleSort = (sorter: any) => {
  if (sorter) {
    const ordering = sorter.order === 'descend' ? `-${sorter.columnKey}` : sorter.columnKey
    roleStore.fetchPermissionList({ ...roleStore.permissionSearchParams, ordering, page: 1 })
  }
}

const handleBatchOperation = async (key: string) => {
  const selectedIds = roleStore.selectedPermissionIds

  if (selectedIds.length === 0) {
    message.warning('请先选择要操作的权限')
    return
  }

  let title = ''
  let content = ''
  let operation = key

  switch (key) {
    case 'activate':
      title = '批量激活权限'
      content = `确定要激活选中的 ${selectedIds.length} 个权限吗？`
      break
    case 'deactivate':
      title = '批量禁用权限'
      content = `确定要禁用选中的 ${selectedIds.length} 个权限吗？`
      break
    case 'delete':
      title = '批量删除权限'
      content = `确定要删除选中的 ${selectedIds.length} 个权限吗？此操作不可恢复！`
      break
  }

  dialog.warning({
    title,
    content,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await roleStore.batchOperatePermissions({
          permission_ids: selectedIds,
          operation: operation as any
        })
        message.success('批量操作成功')
      } catch (error) {
        message.error('批量操作失败')
      }
    }
  })
}

const handleFormSuccess = () => {
  formDialogVisible.value = false
  message.success(formMode.value === 'create' ? '权限创建成功' : '权限更新成功')
}

// 生命周期
onMounted(async () => {
  // 获取权限树
  await roleStore.fetchPermissionTree()

  // 获取权限列表（表格视图用）
  await roleStore.fetchPermissionList()

  // 获取权限统计
  await roleStore.fetchPermissionStats()
})
</script>

<style scoped>
.permission-manage-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px 20px;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 150px;
}

.content-area {
  flex: 1;
  padding: 24px;
  overflow: hidden;
}

.tree-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-toolbar {
  margin-bottom: 16px;
}

.tree-container {
  flex: 1;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: auto;
  padding: 8px;
}

.permission-tree {
  width: 100%;
}

.permission-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
}

.permission-node:hover {
  background: #f3f4f6;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.permission-icon {
  flex-shrink: 0;
  color: #6b7280;
}

.permission-name {
  font-weight: 500;
  color: #1f2937;
}

.permission-type {
  flex-shrink: 0;
}

.permission-code {
  flex-shrink: 0;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.permission-status {
  flex-shrink: 0;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.permission-node:hover .node-actions {
  opacity: 1;
}

.table-view {
  height: 100%;
}

.permission-table {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-right {
    justify-content: flex-end;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }

  .stats-cards {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-right {
    flex-direction: column;
    align-items: stretch;
  }

  .node-actions {
    opacity: 1;
  }
}
</style>
