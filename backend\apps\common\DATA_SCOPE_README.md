# 数据范围权限控制系统

## 概述

本系统实现了完整的数据范围权限控制功能，支持基于用户角色和部门关系的细粒度数据访问控制。

## 核心功能

### 1. 数据范围类型

- **ALL（全部数据）**: 无限制访问所有数据
- **DEPT_AND_SUB（本部门及下级）**: 访问用户主部门及其所有子部门的数据
- **DEPT_ONLY（仅本部门）**: 只能访问用户主部门的数据
- **SELF_ONLY（仅本人）**: 只能访问用户自己创建的数据
- **CUSTOM（自定义）**: 支持自定义数据范围规则

### 2. 主要组件

#### DataScopeService（数据范围服务）
- `get_data_scope_filter()`: 获取数据范围过滤器
- `apply_data_scope_to_queryset()`: 应用数据范围到查询集
- `check_data_access_permission()`: 检查对象访问权限
- `get_user_data_scope()`: 获取用户最高数据范围权限
- `get_user_accessible_departments()`: 获取用户可访问部门
- `get_user_departments()`: 获取用户所属部门（支持缓存）
- `get_user_managed_departments()`: 获取用户管理的部门（支持缓存）

#### UserDepartmentService（用户部门服务）
- `assign_user_to_department()`: 分配用户到部门
- `get_user_primary_department()`: 获取用户主部门
- `get_user_managed_departments()`: 获取用户管理的部门
- `remove_user_from_department()`: 从部门移除用户

#### 装饰器和混入类
- `@require_data_scope()`: 数据范围权限验证装饰器
- `@apply_data_scope_filter()`: 自动应用数据范围过滤装饰器
- `DataScopeMixin`: 数据范围权限混入类

## 使用示例

### 1. 基本用法

```python
from apps.common.permissions import DataScopeService
from apps.users.models import UserProfile

# 获取用户的数据范围
user = UserProfile.objects.get(username='testuser')
data_scope = DataScopeService.get_user_data_scope(user)
print(f"用户数据范围: {data_scope}")

# 应用数据范围过滤
queryset = UserProfile.objects.all()
filtered_queryset = DataScopeService.apply_data_scope_to_queryset(
    queryset, user, 'department'
)
```

### 2. 在视图中使用装饰器

```python
from apps.common.decorators import require_data_scope, apply_data_scope_filter

@require_data_scope('DEPT_AND_SUB')
def get_department_users(request):
    """获取部门用户（需要部门及下级权限）"""
    pass

@apply_data_scope_filter('department')
def list_users(request):
    """自动应用数据范围过滤的用户列表"""
    queryset = UserProfile.objects.all()
    return queryset
```

### 3. 在类视图中使用混入

```python
from rest_framework.viewsets import ModelViewSet
from apps.common.decorators import DataScopeMixin

class UserViewSet(DataScopeMixin, ModelViewSet):
    queryset = UserProfile.objects.all()
    data_scope_field = 'department'
    required_data_scope = 'DEPT_ONLY'
    auto_apply_filter = True
```

### 4. 自定义数据范围

```python
# 自定义规则示例
custom_rules = {
    'department_ids': [1, 2, 3],  # 指定部门ID列表
    'user_ids': [10, 20, 30],     # 指定用户ID列表
    'query_filter': Q(status='active')  # 自定义Q对象
}

filter_q = DataScopeService.get_data_scope_filter(
    user, 'CUSTOM', custom_rules=custom_rules
)
```

### 5. 用户部门管理

```python
from apps.common.department_service import UserDepartmentService
from apps.departments.models import Department

# 分配用户到部门
department = Department.objects.get(code='tech')
UserDepartmentService.assign_user_to_department(
    user=user,
    department=department,
    is_primary=True,
    is_manager=True,
    position='技术总监',
    manager_level=1
)

# 获取用户主部门
primary_dept = UserDepartmentService.get_user_primary_department(user)
```

## 缓存机制

系统使用Redis缓存来提高性能：

- **用户角色缓存**: 5分钟
- **用户数据范围缓存**: 5分钟
- **部门关联缓存**: 5分钟
- **可访问部门缓存**: 5分钟

### 缓存管理

```python
# 清除用户缓存
DataScopeService.clear_user_cache(user_id)

# 刷新用户缓存
DataScopeService.refresh_user_cache(user)
```

## 测试

### 运行单元测试

```bash
# 运行所有数据范围测试
python manage.py test apps.common.tests.test_data_scope

# 运行特定测试
python manage.py test apps.common.tests.test_data_scope.DataScopeServiceTest
```

### 使用测试命令

```bash
# 创建测试数据
python manage.py test_data_scope --create-test-data

# 测试权限功能
python manage.py test_data_scope --test-permissions

# 清理测试数据
python manage.py test_data_scope --cleanup
```

## 配置要求

### 1. 中间件配置

确保在`settings.py`中启用了权限中间件：

```python
MIDDLEWARE = [
    # ... 其他中间件
    'apps.common.middleware.PermissionMiddleware',
    # ... 其他中间件
]
```

### 2. 缓存配置

确保配置了Redis缓存：

```python
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## 性能优化建议

1. **合理使用缓存**: 系统已内置缓存机制，避免频繁清除缓存
2. **数据库索引**: 确保部门和用户关联表有适当的索引
3. **查询优化**: 使用`select_related()`和`prefetch_related()`优化查询
4. **批量操作**: 对于大量数据操作，使用批量方法

## 注意事项

1. **超级管理员**: `is_superuser=True`的用户会跳过所有数据范围限制
2. **部门有效期**: 系统会自动检查用户部门关联的有效期
3. **多部门权限**: 用户在多个部门的权限会自动合并，取最高权限
4. **软删除**: 所有相关模型都支持软删除，不会影响权限计算
5. **异常处理**: 权限检查异常时默认返回最严格的权限（无权限）

## 故障排除

### 常见问题

1. **权限不生效**: 检查用户是否有有效的角色和部门关联
2. **缓存问题**: 尝试清除相关缓存
3. **部门树问题**: 确保部门模型正确使用了django-mptt
4. **性能问题**: 检查数据库查询和缓存配置

### 调试方法

```python
# 启用调试日志
import logging
logging.getLogger('apps.common.permissions').setLevel(logging.DEBUG)

# 检查用户权限状态
from apps.common.permissions import DataScopeService
roles = DataScopeService.get_user_roles_with_data_scope(user)
data_scope = DataScopeService.get_user_data_scope(user)
accessible_depts = DataScopeService.get_user_accessible_departments(user)
```

## 扩展开发

### 添加新的数据范围类型

1. 在`DataScopeService.get_data_scope_filter()`中添加新的处理逻辑
2. 更新相关的测试用例
3. 更新文档说明

### 自定义权限验证

```python
def custom_permission_check(user, obj):
    """自定义权限检查函数"""
    # 实现自定义逻辑
    return True

# 在自定义规则中使用
custom_rules = {
    'check_function': custom_permission_check
}
```
