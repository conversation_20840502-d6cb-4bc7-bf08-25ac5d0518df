"""
数据范围权限服务
"""
from django.db.models import Q
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import get_user_model
from apps.departments.models import Department, UserDepartment
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class DataScopeService:
    """数据范围权限服务"""

    # 缓存键前缀
    CACHE_PREFIX = 'data_scope'
    CACHE_TIMEOUT = 300  # 5分钟缓存

    @staticmethod
    def get_data_scope_filter(user, data_scope, data_scope_field='department', custom_rules=None):
        """
        根据数据范围获取查询过滤器

        Args:
            user: 用户对象
            data_scope: 数据范围类型
            data_scope_field: 数据范围字段名，默认为'department'
            custom_rules: 自定义规则（用于CUSTOM类型）

        Returns:
            Q对象查询过滤器
        """
        try:
            if data_scope == 'ALL':
                return Q()  # 无限制

            elif data_scope == 'SELF_ONLY':
                # 支持多种创建者字段
                if hasattr(user, 'id'):
                    return Q(created_by=user) | Q(created_by_id=user.id)
                return Q(created_by=user)

            elif data_scope == 'DEPT_ONLY':
                return DataScopeService._get_dept_only_filter(user, data_scope_field)

            elif data_scope == 'DEPT_AND_SUB':
                return DataScopeService._get_dept_and_sub_filter(user, data_scope_field)

            elif data_scope == 'CUSTOM':
                return DataScopeService._get_custom_filter(user, custom_rules, data_scope_field)

            else:
                logger.warning(f"未知的数据范围类型: {data_scope}")
                return Q(pk__in=[])  # 未知类型默认无数据

        except Exception as e:
            logger.error(f"获取数据范围过滤器失败: {e}")
            return Q(pk__in=[])  # 异常情况默认无数据

    @staticmethod
    def _get_dept_only_filter(user, data_scope_field):
        """获取仅本部门的过滤器"""
        cache_key = f"{DataScopeService.CACHE_PREFIX}:dept_only:{user.id}"
        dept_ids = cache.get(cache_key)

        if dept_ids is None:
            try:
                # 获取用户有效的主部门
                primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                    is_primary=True
                ).first()

                if primary_dept and primary_dept.department:
                    dept_ids = [primary_dept.department.id]
                else:
                    dept_ids = []

            except Exception as e:
                logger.error(f"获取主部门失败: {e}")
                dept_ids = []

            cache.set(cache_key, dept_ids, DataScopeService.CACHE_TIMEOUT)

        if dept_ids:
            if data_scope_field == 'department':
                return Q(department_id__in=dept_ids)
            else:
                return Q(**{f"{data_scope_field}_id__in": dept_ids})

        return Q(pk__in=[])  # 无部门则无数据

    @staticmethod
    def _get_dept_and_sub_filter(user, data_scope_field):
        """获取本部门及下级部门的过滤器"""
        cache_key = f"{DataScopeService.CACHE_PREFIX}:dept_and_sub:{user.id}"
        dept_ids = cache.get(cache_key)

        if dept_ids is None:
            try:
                # 获取用户有效的主部门
                primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                    is_primary=True
                ).first()

                if primary_dept and primary_dept.department:
                    # 使用django-mptt获取部门及其子部门
                    dept = primary_dept.department
                    descendants = dept.get_descendants(include_self=True)
                    dept_ids = list(descendants.values_list('id', flat=True))
                else:
                    dept_ids = []

            except Exception as e:
                logger.error(f"获取部门及子部门失败: {e}")
                dept_ids = []

            cache.set(cache_key, dept_ids, DataScopeService.CACHE_TIMEOUT)

        if dept_ids:
            if data_scope_field == 'department':
                return Q(department_id__in=dept_ids)
            else:
                return Q(**{f"{data_scope_field}_id__in": dept_ids})

        return Q(pk__in=[])  # 无部门则无数据

    @staticmethod
    def _get_custom_filter(user, custom_rules, data_scope_field):
        """获取自定义数据范围的过滤器"""
        if not custom_rules:
            return Q(pk__in=[])

        try:
            # 自定义规则可以是：
            # 1. 部门ID列表
            # 2. 用户ID列表
            # 3. 复杂的Q对象
            if isinstance(custom_rules, dict):
                if 'department_ids' in custom_rules:
                    dept_ids = custom_rules['department_ids']
                    if data_scope_field == 'department':
                        return Q(department_id__in=dept_ids)
                    else:
                        return Q(**{f"{data_scope_field}_id__in": dept_ids})

                elif 'user_ids' in custom_rules:
                    user_ids = custom_rules['user_ids']
                    return Q(created_by_id__in=user_ids)

                elif 'query_filter' in custom_rules:
                    # 直接使用提供的Q对象
                    return custom_rules['query_filter']

            elif isinstance(custom_rules, list):
                # 假设是部门ID列表
                if data_scope_field == 'department':
                    return Q(department_id__in=custom_rules)
                else:
                    return Q(**{f"{data_scope_field}_id__in": custom_rules})

            elif isinstance(custom_rules, Q):
                # 直接使用Q对象
                return custom_rules

        except Exception as e:
            logger.error(f"处理自定义数据范围规则失败: {e}")

        return Q(pk__in=[])  # 默认无数据
    
    @staticmethod
    def get_user_departments(user, include_expired=False):
        """获取用户所属部门"""
        cache_key = f"{DataScopeService.CACHE_PREFIX}:user_depts:{user.id}:{include_expired}"
        departments = cache.get(cache_key)

        if departments is None:
            try:
                queryset = UserDepartment.objects.filter(user=user, is_deleted=False)

                if not include_expired:
                    queryset = UserDepartment.get_effective_relations(user=user)

                departments = list(queryset.select_related('department'))
                cache.set(cache_key, departments, DataScopeService.CACHE_TIMEOUT)

            except Exception as e:
                logger.error(f"获取用户部门失败: {e}")
                departments = []

        return departments
    
    @staticmethod
    def get_user_managed_departments(user, level=None):
        """获取用户管理的部门"""
        cache_key = f"{DataScopeService.CACHE_PREFIX}:managed_depts:{user.id}:{level}"
        managed_depts = cache.get(cache_key)

        if managed_depts is None:
            try:
                queryset = UserDepartment.get_effective_relations(user=user).filter(
                    is_manager=True
                )

                if level:
                    queryset = queryset.filter(manager_level=level)

                managed_depts = list(queryset.select_related('department').order_by('manager_level', 'weight'))
                cache.set(cache_key, managed_depts, DataScopeService.CACHE_TIMEOUT)

            except Exception as e:
                logger.error(f"获取用户管理部门失败: {e}")
                managed_depts = []

        return managed_depts

    @staticmethod
    def get_user_roles_with_data_scope(user):
        """获取用户角色及其数据范围"""
        cache_key = f"{DataScopeService.CACHE_PREFIX}:user_roles:{user.id}"
        roles_data = cache.get(cache_key)

        if roles_data is None:
            try:
                from apps.permissions.models import UserRole

                # 获取用户的有效角色（考虑部门有效期）
                user_departments = UserDepartment.get_effective_relations(user=user)
                dept_ids = list(user_departments.values_list('department_id', flat=True))

                user_roles = UserRole.objects.filter(
                    user=user,
                    is_deleted=False
                ).filter(
                    Q(department__isnull=True) |  # 全局角色
                    Q(department_id__in=dept_ids)  # 有效部门的角色
                ).select_related('role', 'department')

                roles_data = []
                for user_role in user_roles:
                    if user_role.role.is_active:  # 只包含激活的角色
                        roles_data.append({
                            'role': user_role.role,
                            'department': user_role.department,
                            'data_scope': user_role.role.data_scope,
                            'role_id': user_role.role.id,
                            'role_code': user_role.role.code,
                        })

                cache.set(cache_key, roles_data, DataScopeService.CACHE_TIMEOUT)

            except ImportError:
                # 如果权限模块不可用，返回空列表
                logger.warning("权限模块不可用，无法获取用户角色")
                roles_data = []
            except Exception as e:
                logger.error(f"获取用户角色失败: {e}")
                roles_data = []

        return roles_data

    @staticmethod
    def apply_data_scope_to_queryset(queryset, user, data_scope_field='department', custom_rules=None):
        """
        将数据范围过滤应用到查询集

        Args:
            queryset: 要过滤的查询集
            user: 用户对象
            data_scope_field: 数据范围字段名
            custom_rules: 自定义规则字典

        Returns:
            过滤后的查询集
        """
        try:
            # 超级管理员跳过数据范围限制
            if user.is_superuser:
                return queryset

            # 获取用户的角色数据范围权限
            user_roles = DataScopeService.get_user_roles_with_data_scope(user)

            if not user_roles:
                return queryset.filter(pk__in=[])  # 无角色则无数据

            # 获取所有数据范围
            data_scopes = [role['data_scope'] for role in user_roles]

            # 如果有全部数据权限，直接返回
            if 'ALL' in data_scopes:
                return queryset

            # 构建过滤条件（使用OR逻辑合并多个数据范围）
            filters = Q()

            for role_data in user_roles:
                scope = role_data['data_scope']

                # 处理自定义数据范围
                if scope == 'CUSTOM' and custom_rules:
                    # 可以根据角色或部门设置不同的自定义规则
                    role_custom_rules = custom_rules.get(role_data['role_code'], custom_rules)
                    scope_filter = DataScopeService.get_data_scope_filter(
                        user, scope, data_scope_field, role_custom_rules
                    )
                else:
                    scope_filter = DataScopeService.get_data_scope_filter(
                        user, scope, data_scope_field
                    )

                filters |= scope_filter

            return queryset.filter(filters)

        except Exception as e:
            logger.error(f"应用数据范围过滤失败: {e}")
            # 异常情况下返回空查询集，确保数据安全
            return queryset.filter(pk__in=[])

    @staticmethod
    def check_data_access_permission(user, obj, data_scope_field='department', custom_rules=None):
        """
        检查用户是否有权限访问特定数据对象

        Args:
            user: 用户对象
            obj: 要检查的数据对象
            data_scope_field: 数据范围字段名
            custom_rules: 自定义规则

        Returns:
            bool: 是否有权限访问
        """
        try:
            # 超级管理员有所有权限
            if user.is_superuser:
                return True

            user_roles = DataScopeService.get_user_roles_with_data_scope(user)

            if not user_roles:
                return False

            # 检查是否有全部数据权限
            data_scopes = [role['data_scope'] for role in user_roles]
            if 'ALL' in data_scopes:
                return True

            # 检查具体权限
            for role_data in user_roles:
                scope = role_data['data_scope']

                if scope == 'SELF_ONLY':
                    if DataScopeService._check_self_only_access(user, obj):
                        return True

                elif scope == 'DEPT_ONLY':
                    if DataScopeService._check_dept_only_access(user, obj, data_scope_field):
                        return True

                elif scope == 'DEPT_AND_SUB':
                    if DataScopeService._check_dept_and_sub_access(user, obj, data_scope_field):
                        return True

                elif scope == 'CUSTOM':
                    if DataScopeService._check_custom_access(user, obj, custom_rules, data_scope_field):
                        return True

            return False

        except Exception as e:
            logger.error(f"检查数据访问权限失败: {e}")
            return False  # 异常情况默认无权限

    @staticmethod
    def _check_self_only_access(user, obj):
        """检查仅本人数据访问权限"""
        # 支持多种创建者字段
        if hasattr(obj, 'created_by') and obj.created_by == user:
            return True
        if hasattr(obj, 'created_by_id') and obj.created_by_id == user.id:
            return True
        if hasattr(obj, 'user') and obj.user == user:
            return True
        if hasattr(obj, 'user_id') and obj.user_id == user.id:
            return True
        return False

    @staticmethod
    def _check_dept_only_access(user, obj, data_scope_field):
        """检查本部门数据访问权限"""
        primary_dept = UserDepartment.get_effective_relations(user=user).filter(
            is_primary=True
        ).first()

        if not primary_dept:
            return False

        if hasattr(obj, data_scope_field):
            obj_dept = getattr(obj, data_scope_field)
            return obj_dept == primary_dept.department
        elif hasattr(obj, f"{data_scope_field}_id"):
            obj_dept_id = getattr(obj, f"{data_scope_field}_id")
            return obj_dept_id == primary_dept.department.id

        return False

    @staticmethod
    def _check_dept_and_sub_access(user, obj, data_scope_field):
        """检查本部门及下级部门数据访问权限"""
        primary_dept = UserDepartment.get_effective_relations(user=user).filter(
            is_primary=True
        ).first()

        if not primary_dept:
            return False

        # 获取部门及其子部门ID列表
        dept = primary_dept.department
        descendants = dept.get_descendants(include_self=True)
        dept_ids = list(descendants.values_list('id', flat=True))

        if hasattr(obj, data_scope_field):
            obj_dept = getattr(obj, data_scope_field)
            return obj_dept.id in dept_ids
        elif hasattr(obj, f"{data_scope_field}_id"):
            obj_dept_id = getattr(obj, f"{data_scope_field}_id")
            return obj_dept_id in dept_ids

        return False

    @staticmethod
    def _check_custom_access(user, obj, custom_rules, data_scope_field):
        """检查自定义数据访问权限"""
        if not custom_rules:
            return False

        try:
            if isinstance(custom_rules, dict):
                if 'department_ids' in custom_rules:
                    dept_ids = custom_rules['department_ids']
                    if hasattr(obj, data_scope_field):
                        obj_dept = getattr(obj, data_scope_field)
                        return obj_dept.id in dept_ids
                    elif hasattr(obj, f"{data_scope_field}_id"):
                        obj_dept_id = getattr(obj, f"{data_scope_field}_id")
                        return obj_dept_id in dept_ids

                elif 'user_ids' in custom_rules:
                    user_ids = custom_rules['user_ids']
                    return DataScopeService._check_self_only_access(user, obj) and user.id in user_ids

                elif 'check_function' in custom_rules:
                    # 自定义检查函数
                    check_func = custom_rules['check_function']
                    return check_func(user, obj)

        except Exception as e:
            logger.error(f"检查自定义数据访问权限失败: {e}")

        return False

    @staticmethod
    def get_user_data_scope(user):
        """
        获取用户的最高数据范围权限

        Args:
            user: 用户对象

        Returns:
            str: 数据范围类型
        """
        cache_key = f"{DataScopeService.CACHE_PREFIX}:user_data_scope:{user.id}"
        data_scope = cache.get(cache_key)

        if data_scope is None:
            try:
                # 超级管理员拥有全部数据权限
                if user.is_superuser:
                    data_scope = 'ALL'
                else:
                    user_roles = DataScopeService.get_user_roles_with_data_scope(user)

                    if not user_roles:
                        data_scope = 'SELF_ONLY'  # 默认最小权限
                    else:
                        # 数据范围优先级：ALL > DEPT_AND_SUB > DEPT_ONLY > SELF_ONLY > CUSTOM
                        scope_priority = {
                            'ALL': 4,
                            'DEPT_AND_SUB': 3,
                            'DEPT_ONLY': 2,
                            'SELF_ONLY': 1,
                            'CUSTOM': 0
                        }

                        max_priority = 0
                        data_scope = 'SELF_ONLY'

                        for role_data in user_roles:
                            scope = role_data['data_scope']
                            priority = scope_priority.get(scope, 0)
                            if priority > max_priority:
                                max_priority = priority
                                data_scope = scope

                cache.set(cache_key, data_scope, DataScopeService.CACHE_TIMEOUT)

            except Exception as e:
                logger.error(f"获取用户数据范围失败: {e}")
                data_scope = 'SELF_ONLY'  # 异常情况默认最小权限

        return data_scope

    @staticmethod
    def get_user_accessible_departments(user):
        """
        获取用户可访问的部门列表

        Args:
            user: 用户对象

        Returns:
            QuerySet: 部门查询集
        """
        cache_key = f"{DataScopeService.CACHE_PREFIX}:accessible_depts:{user.id}"
        dept_ids = cache.get(cache_key)

        if dept_ids is None:
            try:
                data_scope = DataScopeService.get_user_data_scope(user)

                if data_scope == 'ALL':
                    dept_ids = list(Department.objects.filter(is_deleted=False).values_list('id', flat=True))

                elif data_scope == 'DEPT_AND_SUB':
                    primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                        is_primary=True
                    ).first()
                    if primary_dept:
                        dept = primary_dept.department
                        descendants = dept.get_descendants(include_self=True)
                        dept_ids = list(descendants.values_list('id', flat=True))
                    else:
                        dept_ids = []

                elif data_scope == 'DEPT_ONLY':
                    primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                        is_primary=True
                    ).first()
                    if primary_dept:
                        dept_ids = [primary_dept.department.id]
                    else:
                        dept_ids = []

                else:  # SELF_ONLY 或 CUSTOM
                    dept_ids = []

                cache.set(cache_key, dept_ids, DataScopeService.CACHE_TIMEOUT)

            except Exception as e:
                logger.error(f"获取用户可访问部门失败: {e}")
                dept_ids = []

        return Department.objects.filter(id__in=dept_ids, is_deleted=False)

    @staticmethod
    def clear_user_cache(user_id):
        """
        清除用户相关的数据范围缓存

        Args:
            user_id: 用户ID
        """
        try:
            cache_keys = [
                f"{DataScopeService.CACHE_PREFIX}:user_roles:{user_id}",
                f"{DataScopeService.CACHE_PREFIX}:user_data_scope:{user_id}",
                f"{DataScopeService.CACHE_PREFIX}:accessible_depts:{user_id}",
                f"{DataScopeService.CACHE_PREFIX}:dept_only:{user_id}",
                f"{DataScopeService.CACHE_PREFIX}:dept_and_sub:{user_id}",
                f"{DataScopeService.CACHE_PREFIX}:user_depts:{user_id}:True",
                f"{DataScopeService.CACHE_PREFIX}:user_depts:{user_id}:False",
                f"{DataScopeService.CACHE_PREFIX}:managed_depts:{user_id}:None",
                f"{DataScopeService.CACHE_PREFIX}:managed_depts:{user_id}:1",
                f"{DataScopeService.CACHE_PREFIX}:managed_depts:{user_id}:2",
                f"{DataScopeService.CACHE_PREFIX}:managed_depts:{user_id}:3",
            ]

            cache.delete_many(cache_keys)
            logger.info(f"已清除用户 {user_id} 的数据范围缓存")

        except Exception as e:
            logger.error(f"清除用户缓存失败: {e}")

    @staticmethod
    def refresh_user_cache(user):
        """
        刷新用户的数据范围缓存

        Args:
            user: 用户对象
        """
        try:
            # 清除旧缓存
            DataScopeService.clear_user_cache(user.id)

            # 预热新缓存
            DataScopeService.get_user_roles_with_data_scope(user)
            DataScopeService.get_user_data_scope(user)
            DataScopeService.get_user_accessible_departments(user)

            logger.info(f"已刷新用户 {user.id} 的数据范围缓存")

        except Exception as e:
            logger.error(f"刷新用户缓存失败: {e}")

    @staticmethod
    def validate_data_scope_field(model_class, data_scope_field):
        """
        验证数据范围字段是否存在于模型中

        Args:
            model_class: 模型类
            data_scope_field: 数据范围字段名

        Returns:
            bool: 字段是否有效
        """
        try:
            # 检查字段是否存在
            if hasattr(model_class, data_scope_field):
                return True
            elif hasattr(model_class, f"{data_scope_field}_id"):
                return True
            else:
                logger.warning(f"模型 {model_class.__name__} 中不存在字段 {data_scope_field}")
                return False

        except Exception as e:
            logger.error(f"验证数据范围字段失败: {e}")
            return False
