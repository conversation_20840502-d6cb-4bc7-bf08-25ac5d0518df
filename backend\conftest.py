"""
pytest配置文件
"""
import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')

def pytest_configure():
    """pytest配置"""
    if not settings.configured:
        django.setup()

def pytest_sessionstart(session):
    """测试会话开始"""
    print("🧪 开始HEIM项目测试会话")
    print("📋 使用测试配置: config.settings.test")

def pytest_sessionfinish(session, exitstatus):
    """测试会话结束"""
    if exitstatus == 0:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败，请检查错误信息")

def pytest_collection_modifyitems(config, items):
    """修改测试项目收集"""
    # 为没有标记的测试添加默认标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker("unit")  # 默认为单元测试
