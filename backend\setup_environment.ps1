# HEIM Backend Environment Setup Script
# 使用uv包管理工具，遵循项目技术规范

Write-Host "=== HEIM Backend Environment Setup ===" -ForegroundColor Green

# 检查uv是否安装
Write-Host "检查uv包管理工具..." -ForegroundColor Blue
try {
    $uvVersion = uv --version
    Write-Host "uv版本: $uvVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到uv包管理工具，请先安装uv" -ForegroundColor Red
    Write-Host "安装命令: pip install uv" -ForegroundColor Yellow
    exit 1
}

# 使用uv同步依赖
Write-Host "使用uv同步项目依赖..." -ForegroundColor Blue
uv sync

# 检查Python环境
Write-Host "当前Python环境:" -ForegroundColor Blue
uv run python -c "import sys; print('Python executable:', sys.executable)"

# 验证Django系统
Write-Host "验证Django系统..." -ForegroundColor Blue
uv run python manage.py check

# 验证Celery配置
Write-Host "验证Celery配置..." -ForegroundColor Blue
uv run python -c "from config.celery import app; print('✅ Celery应用导入成功:', app.main)"

# 显示已安装的包
Write-Host "已安装的包:" -ForegroundColor Green
uv pip list

Write-Host "=== Environment setup completed! ===" -ForegroundColor Green
