"""
审计日志模块 - 业务服务
"""
import logging
import traceback
import json
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache
from django.db import transaction
from .models import OperationLog

logger = logging.getLogger(__name__)


class AuditService:
    """审计日志服务"""

    @staticmethod
    def log_operation(user, operation_type, operation_desc, method, path,
                     ip_address, user_agent='', status_code=200, response_time=0):
        """记录操作日志"""
        try:
            OperationLog.objects.create(
                user=user,
                operation_type=operation_type,
                operation_desc=operation_desc,
                method=method,
                path=path,
                ip_address=ip_address,
                user_agent=user_agent,
                status_code=status_code,
                response_time=response_time
            )
        except Exception as e:
            logger.error(f"记录操作日志失败: {e}")

    @staticmethod
    def get_user_operation_logs(user, limit=None):
        """获取用户操作日志"""
        queryset = OperationLog.objects.filter(user=user).order_by('-created_at')
        if limit:
            queryset = queryset[:limit]
        return list(queryset)

    @staticmethod
    def get_operation_statistics():
        """获取操作统计"""
        from django.db.models import Count

        total_logs = OperationLog.objects.count()

        # 按操作类型统计
        type_stats = list(
            OperationLog.objects.values('operation_type')
            .annotate(count=Count('id'))
            .order_by('-count')
        )

        return {
            'total_logs': total_logs,
            'operation_type_stats': type_stats
        }

    @staticmethod
    def get_daily_operation_statistics(days=7):
        """获取每日操作统计"""
        from django.db.models import Count
        from django.utils import timezone
        from datetime import timedelta

        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days-1)

        stats = []
        current_date = start_date

        while current_date <= end_date:
            count = OperationLog.objects.filter(
                created_at__date=current_date
            ).count()

            stats.append({
                'date': current_date.isoformat(),
                'count': count
            })

            current_date += timedelta(days=1)

        return stats

    @staticmethod
    def cleanup_old_logs(days=90, backup=False):
        """清理旧日志"""
        from django.utils import timezone
        from datetime import timedelta

        cutoff_date = timezone.now() - timedelta(days=days)
        old_logs = OperationLog.objects.filter(created_at__lt=cutoff_date)

        if backup:
            # 这里可以实现备份逻辑
            pass

        count = old_logs.count()
        old_logs.delete()

        return count

    @staticmethod
    def export_logs_to_csv(start_date=None, end_date=None):
        """导出日志到CSV"""
        import csv
        from io import StringIO

        queryset = OperationLog.objects.all()

        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        output = StringIO()
        writer = csv.writer(output)

        # 写入头部
        writer.writerow([
            'ID', 'User', 'Operation Type', 'Operation Desc',
            'Method', 'Path', 'IP Address', 'Status Code', 'Created At'
        ])

        # 写入数据
        for log in queryset:
            writer.writerow([
                log.id,
                log.user.username if log.user else 'Anonymous',
                log.operation_type,
                log.operation_desc,
                log.method,
                log.path,
                log.ip_address,
                log.status_code,
                log.created_at.isoformat()
            ])

        return output.getvalue()

    @staticmethod
    def generate_compliance_report(start_date, end_date):
        """生成合规报告"""
        from django.db.models import Count

        logs = OperationLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        )

        total_operations = logs.count()

        # 用户活动统计
        user_activity = list(
            logs.values('user__username')
            .annotate(count=Count('id'))
            .order_by('-count')
        )

        # 操作类型汇总
        operation_summary = list(
            logs.values('operation_type')
            .annotate(count=Count('id'))
            .order_by('-count')
        )

        return {
            'total_operations': total_operations,
            'user_activity': user_activity,
            'operation_summary': operation_summary,
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            }
        }


class ExceptionLogService:
    """异常日志记录服务"""
    
    @staticmethod
    def log_exception(exc, request=None, context=None, level='ERROR'):
        """
        记录异常信息到日志和数据库
        
        Args:
            exc: 异常实例
            request: HTTP请求对象
            context: 异常上下文信息
            level: 日志级别
        """
        try:
            # 构建异常信息
            exception_info = {
                'exception_type': exc.__class__.__name__,
                'exception_message': str(exc),
                'traceback': traceback.format_exc(),
                'timestamp': timezone.now().isoformat(),
            }
            
            # 添加请求信息
            if request:
                exception_info.update({
                    'request_path': request.path,
                    'request_method': request.method,
                    'request_user': getattr(request.user, 'username', 'Anonymous') if hasattr(request, 'user') else 'Unknown',
                    'request_ip': ExceptionLogService._get_client_ip(request),
                    'request_user_agent': request.META.get('HTTP_USER_AGENT', ''),
                    'request_data': ExceptionLogService._safe_serialize_request_data(request),
                })
            
            # 添加上下文信息
            if context:
                exception_info['context'] = context
            
            # 记录到Django日志
            log_message = f"异常记录 - {exception_info['exception_type']}: {exception_info['exception_message']}"
            if request:
                log_message += f" | 路径: {request.path} | 用户: {exception_info['request_user']}"
            
            if level == 'CRITICAL':
                logger.critical(log_message, exc_info=True, extra={'exception_info': exception_info})
            elif level == 'ERROR':
                logger.error(log_message, exc_info=True, extra={'exception_info': exception_info})
            elif level == 'WARNING':
                logger.warning(log_message, exc_info=True, extra={'exception_info': exception_info})
            else:
                logger.info(log_message, exc_info=True, extra={'exception_info': exception_info})
            
            # 记录到操作日志（如果有请求信息）
            if request and hasattr(request, 'user') and request.user.is_authenticated:
                try:
                    with transaction.atomic():
                        OperationLog.objects.create(
                            user=request.user,
                            operation_type='ERROR',  # 添加错误类型到选择中
                            operation_desc=f"系统异常: {exception_info['exception_type']} - {exception_info['exception_message'][:200]}",
                            method=request.method,
                            path=request.path,
                            ip_address=exception_info.get('request_ip', ''),
                            user_agent=exception_info.get('request_user_agent', '')[:500],
                            status_code=500,  # 异常默认500状态码
                            response_time=0  # 异常情况下响应时间设为0
                        )
                except Exception as log_exc:
                    # 记录日志失败不应该影响异常处理
                    logger.error(f"记录异常到操作日志失败: {log_exc}")
            
            # 缓存异常统计信息
            ExceptionLogService._update_exception_stats(exception_info)
            
        except Exception as log_exc:
            # 异常日志记录失败，使用基础日志记录
            logger.error(f"异常日志记录服务失败: {log_exc}", exc_info=True)
    
    @staticmethod
    def _get_client_ip(request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    @staticmethod
    def _safe_serialize_request_data(request):
        """安全地序列化请求数据，过滤敏感信息"""
        try:
            # 获取请求数据
            data = {}
            
            # GET参数
            if request.GET:
                data['GET'] = dict(request.GET)
            
            # POST数据（过滤敏感字段）
            if hasattr(request, 'data') and request.data:
                filtered_data = {}
                sensitive_fields = ['password', 'token', 'secret', 'key', 'captcha']
                
                for key, value in request.data.items():
                    if any(sensitive in key.lower() for sensitive in sensitive_fields):
                        filtered_data[key] = '***FILTERED***'
                    else:
                        # 限制值的长度
                        if isinstance(value, str) and len(value) > 200:
                            filtered_data[key] = value[:200] + '...'
                        else:
                            filtered_data[key] = value
                
                data['POST'] = filtered_data
            
            # 限制总数据大小
            serialized = json.dumps(data, default=str, ensure_ascii=False)
            if len(serialized) > 2000:  # 限制2KB
                return {'message': 'Request data too large, truncated'}
            
            return data
        except Exception:
            return {'message': 'Failed to serialize request data'}
    
    @staticmethod
    def _update_exception_stats(exception_info):
        """更新异常统计信息"""
        try:
            cache_key = 'exception_stats'
            stats = cache.get(cache_key, {})
            
            # 按异常类型统计
            exc_type = exception_info['exception_type']
            if exc_type not in stats:
                stats[exc_type] = {'count': 0, 'last_occurrence': None}
            
            stats[exc_type]['count'] += 1
            stats[exc_type]['last_occurrence'] = exception_info['timestamp']
            
            # 缓存1小时
            cache.set(cache_key, stats, 3600)
        except Exception:
            # 统计更新失败不影响主流程
            pass


class AuditLogService:
    """审计日志业务服务"""
    
    @staticmethod
    def create_operation_log(user, operation_type, operation_desc, request=None, **kwargs):
        """
        创建操作日志
        
        Args:
            user: 操作用户
            operation_type: 操作类型
            operation_desc: 操作描述
            request: HTTP请求对象
            **kwargs: 其他参数
        """
        try:
            log_data = {
                'user': user,
                'operation_type': operation_type,
                'operation_desc': operation_desc,
                'method': kwargs.get('method', 'UNKNOWN'),
                'path': kwargs.get('path', ''),
                'ip_address': kwargs.get('ip_address', ''),
                'user_agent': kwargs.get('user_agent', '')[:500],
                'status_code': kwargs.get('status_code', 200),
                'response_time': kwargs.get('response_time', 0),
            }
            
            # 从请求中提取信息
            if request:
                log_data.update({
                    'method': request.method,
                    'path': request.path,
                    'ip_address': ExceptionLogService._get_client_ip(request),
                    'user_agent': request.META.get('HTTP_USER_AGENT', '')[:500],
                })
            
            # 创建日志记录
            with transaction.atomic():
                return OperationLog.objects.create(**log_data)
                
        except Exception as e:
            logger.error(f"创建操作日志失败: {e}", exc_info=True)
            return None
    
    @staticmethod
    def get_user_operation_stats(user, days=30):
        """
        获取用户操作统计
        
        Args:
            user: 用户对象
            days: 统计天数
            
        Returns:
            dict: 统计信息
        """
        try:
            from django.db.models import Count
            from datetime import timedelta
            
            start_date = timezone.now() - timedelta(days=days)
            
            # 基础统计
            total_operations = OperationLog.objects.filter(
                user=user,
                created_at__gte=start_date
            ).count()
            
            # 按操作类型统计
            operation_type_stats = dict(
                OperationLog.objects.filter(
                    user=user,
                    created_at__gte=start_date
                ).values('operation_type').annotate(
                    count=Count('id')
                ).values_list('operation_type', 'count')
            )
            
            # 按日期统计
            daily_stats = []
            for i in range(days):
                day_start = start_date + timedelta(days=i)
                day_end = day_start + timedelta(days=1)
                count = OperationLog.objects.filter(
                    user=user,
                    created_at__gte=day_start,
                    created_at__lt=day_end
                ).count()
                daily_stats.append({
                    'date': day_start.strftime('%Y-%m-%d'),
                    'count': count
                })
            
            return {
                'total_operations': total_operations,
                'operation_type_stats': operation_type_stats,
                'daily_stats': daily_stats,
                'period_days': days
            }
        except Exception as e:
            logger.error(f"获取用户操作统计失败: {e}", exc_info=True)
            return {}
    
    @staticmethod
    def get_system_health_stats():
        """
        获取系统健康状态统计
        
        Returns:
            dict: 系统健康统计信息
        """
        try:
            from django.db.models import Count, Avg
            from datetime import timedelta
            
            now = timezone.now()
            hour_ago = now - timedelta(hours=1)
            day_ago = now - timedelta(days=1)
            
            # 错误率统计
            total_requests_hour = OperationLog.objects.filter(created_at__gte=hour_ago).count()
            error_requests_hour = OperationLog.objects.filter(
                created_at__gte=hour_ago,
                status_code__gte=400
            ).count()
            
            error_rate = (error_requests_hour / total_requests_hour * 100) if total_requests_hour > 0 else 0
            
            # 平均响应时间
            avg_response_time = OperationLog.objects.filter(
                created_at__gte=hour_ago
            ).aggregate(avg_time=Avg('response_time'))['avg_time'] or 0
            
            # 活跃用户数
            active_users = OperationLog.objects.filter(
                created_at__gte=day_ago,
                user__isnull=False
            ).values('user').distinct().count()
            
            # 异常统计
            exception_stats = cache.get('exception_stats', {})
            
            return {
                'error_rate': round(error_rate, 2),
                'avg_response_time': round(avg_response_time, 2),
                'active_users_24h': active_users,
                'total_requests_1h': total_requests_hour,
                'error_requests_1h': error_requests_hour,
                'exception_types': len(exception_stats),
                'timestamp': now.isoformat()
            }
        except Exception as e:
            logger.error(f"获取系统健康统计失败: {e}", exc_info=True)
            return {}
