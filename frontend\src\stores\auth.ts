import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'
import {
  emitPermissionChange,
  emitDepartmentChange,
  emitLoginStatusChange
} from '@/utils/permission-events'
import type {
  UserInfo,
  LoginForm,
  LoginResponse,
  DepartmentInfo
} from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('access_token') || '')
  const refreshToken = ref<string>(localStorage.getItem('refresh_token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  const currentDepartment = ref<number | null>(null)
  const departments = ref<DepartmentInfo[]>([])
  const currentDepartmentInfo = ref<DepartmentInfo | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)

  // 当前部门信息
  const currentDeptInfo = computed(() => {
    if (!currentDepartment.value || !departments.value.length) {
      return null
    }
    return departments.value.find(dept => dept.id === currentDepartment.value) || null
  })

  // 是否为多部门用户
  const isMultiDepartmentUser = computed(() => {
    return departments.value.length > 1
  })

  // 权限检查
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })

  // 方法
  const login = async (form: LoginForm): Promise<LoginResponse> => {
    try {
      const response = await authApi.login(form)
      
      if (response.code === 1000) {
        // 保存令牌和用户信息
        token.value = response.data.access
        refreshToken.value = response.data.refresh
        userInfo.value = response.data.user
        permissions.value = response.data.permissions || []
        departments.value = response.data.departments || []

        // 设置默认部门（主部门或第一个部门）
        if (departments.value.length > 0) {
          const primaryDept = departments.value.find(dept => dept.is_primary)
          currentDepartment.value = primaryDept?.id || departments.value[0].id
          currentDepartmentInfo.value = primaryDept || departments.value[0]
        }

        // 持久化存储
        localStorage.setItem('access_token', response.data.access)
        localStorage.setItem('refresh_token', response.data.refresh)
        localStorage.setItem('user_info', JSON.stringify(response.data.user))
        localStorage.setItem('permissions', JSON.stringify(response.data.permissions || []))
        localStorage.setItem('departments', JSON.stringify(response.data.departments || []))
        localStorage.setItem('current_department', String(currentDepartment.value || ''))

        // 触发权限变化事件
        emitPermissionChange(response.data.permissions || [])
        emitLoginStatusChange(true, response.data.user)
        if (currentDepartment.value) {
          emitDepartmentChange(currentDepartment.value, currentDepartmentInfo.value)
        }

        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      if (refreshToken.value) {
        await authApi.logout({ refresh: refreshToken.value })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除状态
      token.value = ''
      refreshToken.value = ''
      userInfo.value = null
      permissions.value = []
      departments.value = []
      currentDepartment.value = null
      currentDepartmentInfo.value = null

      // 清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')
      localStorage.removeItem('permissions')
      localStorage.removeItem('departments')
      localStorage.removeItem('current_department')

      // 触发登出事件
      emitLoginStatusChange(false)
    }
  }

  const refreshTokenAction = async (): Promise<void> => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await authApi.refreshToken({ refresh: refreshToken.value })
      
      if (response.code === 1000) {
        token.value = response.data.access
        refreshToken.value = response.data.refresh
        
        localStorage.setItem('access_token', response.data.access)
        localStorage.setItem('refresh_token', response.data.refresh)
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
      await logout()
      throw error
    }
  }

  const refreshUserInfo = async () => {
    try {
      const response = await authApi.getAuthStatus()
      if (response.code === 1000) {
        // 更新用户信息（如果需要的话）
        // userInfo.value = response.data.user_info
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
    }
  }

  // 切换部门
  const switchDepartment = async (departmentId: number): Promise<void> => {
    try {
      const targetDepartment = departments.value.find(dept => dept.id === departmentId)
      if (!targetDepartment) {
        throw new Error('部门不存在')
      }

      // 更新当前部门
      currentDepartment.value = departmentId
      currentDepartmentInfo.value = targetDepartment

      // 持久化存储
      localStorage.setItem('current_department', String(departmentId))

      // TODO: 调用后端API更新用户当前部门和权限
      // const response = await authApi.switchDepartment(departmentId)
      // permissions.value = response.permissions || []
      // emitPermissionChange(response.permissions || [])

      // 触发部门变化事件
      emitDepartmentChange(departmentId, targetDepartment)

      console.log(`已切换到部门: ${targetDepartment.name}`)
    } catch (error) {
      console.error('切换部门失败:', error)
      throw error
    }
  }

  // 初始化时恢复用户信息
  const initializeAuth = () => {
    const storedToken = localStorage.getItem('access_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')
    const storedUserInfo = localStorage.getItem('user_info')
    const storedPermissions = localStorage.getItem('permissions')
    const storedDepartments = localStorage.getItem('departments')
    const storedCurrentDepartment = localStorage.getItem('current_department')

    if (storedToken && storedRefreshToken && storedUserInfo) {
      token.value = storedToken
      refreshToken.value = storedRefreshToken

      try {
        userInfo.value = JSON.parse(storedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('user_info')
      }

      if (storedPermissions) {
        try {
          permissions.value = JSON.parse(storedPermissions)
        } catch (error) {
          console.error('解析权限信息失败:', error)
          localStorage.removeItem('permissions')
        }
      }

      if (storedDepartments) {
        try {
          departments.value = JSON.parse(storedDepartments)
        } catch (error) {
          console.error('解析部门信息失败:', error)
          localStorage.removeItem('departments')
        }
      }

      if (storedCurrentDepartment && departments.value.length > 0) {
        const deptId = parseInt(storedCurrentDepartment)
        const dept = departments.value.find(d => d.id === deptId)
        if (dept) {
          currentDepartment.value = deptId
          currentDepartmentInfo.value = dept
        }
      }
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    userInfo,
    permissions,
    currentDepartment,
    departments,
    currentDepartmentInfo,

    // 计算属性
    isLoggedIn,
    currentDeptInfo,
    isMultiDepartmentUser,
    hasPermission,

    // 方法
    login,
    logout,
    refreshTokenAction,
    refreshUserInfo,
    switchDepartment,
    initializeAuth
  }
})
