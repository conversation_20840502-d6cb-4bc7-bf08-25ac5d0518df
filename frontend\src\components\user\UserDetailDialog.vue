<template>
  <n-modal
    v-model:show="visible"
    preset="dialog"
    title="用户详情"
    class="user-detail-dialog"
    style="width: 900px"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else-if="userDetail" class="user-detail-content">
      <!-- 基础信息 -->
      <n-card title="基础信息" class="detail-card">
        <n-grid :cols="3" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <div class="detail-item">
              <label>用户名</label>
              <span>{{ userDetail.username }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>昵称</label>
              <span>{{ userDetail.nickname }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>邮箱</label>
              <span>{{ userDetail.email || '-' }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>手机号</label>
              <span>{{ userDetail.phone || '-' }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>企业微信ID</label>
              <span>{{ userDetail.wechat_work_id || '-' }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>用户状态</label>
              <n-tag :type="userDetail.is_active ? 'success' : 'error'" size="small">
                {{ userDetail.is_active ? '正常' : '禁用' }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>管理员权限</label>
              <n-tag :type="userDetail.is_staff ? 'info' : 'default'" size="small">
                {{ userDetail.is_staff ? '是' : '否' }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>最后登录时间</label>
              <span>{{ formatDateTime(userDetail.last_login_time) }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>最后登录IP</label>
              <span>{{ userDetail.last_login_ip || '-' }}</span>
            </div>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 部门信息 -->
      <n-card title="部门信息" class="detail-card">
        <div v-if="userDetail.departments.length > 0">
          <div class="department-list">
            <div
              v-for="dept in userDetail.departments"
              :key="dept.id"
              class="department-item"
            >
              <span class="department-name">{{ dept.name }}</span>
              <div class="department-tags">
                <n-tag v-if="dept.is_primary" type="success" size="small">主部门</n-tag>
                <n-tag v-if="dept.is_manager" type="info" size="small">管理者</n-tag>
                <span v-if="dept.position" class="position">{{ dept.position }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <span>暂无部门信息</span>
        </div>
      </n-card>

      <!-- 角色权限 -->
      <n-card title="角色权限" class="detail-card">
        <n-tabs type="line">
          <n-tab-pane name="roles" tab="角色列表">
            <div v-if="userDetail.roles.length > 0" class="role-list">
              <div
                v-for="role in userDetail.roles"
                :key="role.id"
                class="role-item"
              >
                <div class="role-info">
                  <span class="role-name">{{ role.name }}</span>
                  <span class="role-code">{{ role.code }}</span>
                </div>
                <div class="role-description">
                  {{ role.description || '暂无描述' }}
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <span>暂无角色信息</span>
            </div>
          </n-tab-pane>
          
          <n-tab-pane name="permissions" tab="权限列表">
            <div v-if="userDetail.permissions.length > 0" class="permission-list">
              <n-tag
                v-for="permission in userDetail.permissions"
                :key="permission"
                class="permission-tag"
                size="small"
              >
                {{ formatPermissionName(permission) }}
              </n-tag>
            </div>
            <div v-else class="empty-state">
              <span>暂无权限信息</span>
            </div>
          </n-tab-pane>
        </n-tabs>
      </n-card>

      <!-- 会话信息 -->
      <n-card title="活动会话" class="detail-card">
        <div v-if="userDetail.sessions.length > 0">
          <n-data-table
            :columns="sessionColumns"
            :data="userDetail.sessions"
            :pagination="false"
            size="small"
          />
        </div>
        <div v-else class="empty-state">
          <span>暂无活动会话</span>
        </div>
      </n-card>

      <!-- 登录日志 -->
      <n-card title="登录日志" class="detail-card">
        <div v-if="userDetail.login_logs.length > 0">
          <n-data-table
            :columns="loginLogColumns"
            :data="userDetail.login_logs"
            :pagination="{ pageSize: 5 }"
            size="small"
          />
        </div>
        <div v-else class="empty-state">
          <span>暂无登录日志</span>
        </div>
      </n-card>
    </div>
    
    <template #action>
      <n-button @click="handleClose">关闭</n-button>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useMessage, type DataTableColumns } from 'naive-ui'
import { userApi } from '@/api/user'
import { formatPermissionName } from '@/utils/auth'
import type { UserDetail, UserSession, LoginLog } from '@/types/user'

// Props
interface Props {
  visible: boolean
  userId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  userId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const userDetail = ref<UserDetail | null>(null)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 会话表格列
const sessionColumns: DataTableColumns<UserSession> = [
  {
    title: 'IP地址',
    key: 'ip_address',
    width: 120
  },
  {
    title: '设备类型',
    key: 'device_type',
    width: 100
  },
  {
    title: '浏览器',
    key: 'browser',
    width: 120
  },
  {
    title: '操作系统',
    key: 'os',
    width: 120
  },
  {
    title: '最后活动',
    key: 'last_activity',
    width: 160,
    render: (row) => formatDateTime(row.last_activity)
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row) => h(
      'n-tag',
      {
        type: row.is_active ? 'success' : 'default',
        size: 'small'
      },
      row.is_active ? '活跃' : '离线'
    )
  }
]

// 登录日志表格列
const loginLogColumns: DataTableColumns<LoginLog> = [
  {
    title: '登录时间',
    key: 'login_time',
    width: 160,
    render: (row) => formatDateTime(row.login_time)
  },
  {
    title: 'IP地址',
    key: 'ip_address',
    width: 120
  },
  {
    title: '登录结果',
    key: 'login_result',
    width: 100,
    render: (row) => h(
      'n-tag',
      {
        type: row.login_result === 'SUCCESS' ? 'success' : 'error',
        size: 'small'
      },
      row.login_result === 'SUCCESS' ? '成功' : '失败'
    )
  },
  {
    title: '失败原因',
    key: 'fail_reason',
    render: (row) => row.fail_reason || '-'
  },
  {
    title: '位置',
    key: 'location',
    render: (row) => row.location || '-'
  }
]

// 方法
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const fetchUserDetail = async () => {
  if (!props.userId) return
  
  try {
    loading.value = true
    const response = await userApi.getUserDetail(props.userId)
    
    if (response.code === 200) {
      userDetail.value = response.data
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.userId) {
    fetchUserDetail()
  }
})

watch(() => props.userId, (newUserId) => {
  if (props.visible && newUserId) {
    fetchUserDetail()
  }
})
</script>

<style scoped>
.user-detail-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.user-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #1f2937;
}

.department-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.department-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.department-name {
  font-weight: 500;
  color: #1f2937;
}

.department-tags {
  display: flex;
  align-items: center;
  gap: 8px;
}

.position {
  font-size: 12px;
  color: #6b7280;
}

.role-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-item {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.role-name {
  font-weight: 500;
  color: #1f2937;
}

.role-code {
  font-size: 12px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 4px;
}

.role-description {
  font-size: 12px;
  color: #6b7280;
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  margin: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  color: #6b7280;
  font-size: 14px;
}
</style>
