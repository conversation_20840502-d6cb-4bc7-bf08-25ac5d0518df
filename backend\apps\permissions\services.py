"""
权限管理模块 - 业务服务
"""
from django.db.models import Q
from django.contrib.auth import get_user_model
from django.core.cache import cache
from apps.permissions.models import Role, Permission, UserRole
from apps.departments.models import UserDepartment
from apps.common.permissions import DataScopeService

User = get_user_model()


class PermissionService:
    """权限服务类 - 处理权限继承和合并逻辑"""
    
    @staticmethod
    def get_user_permissions(user, include_inactive=False):
        """获取用户的所有权限"""
        cache_key = f"user_permissions_{user.id}_{include_inactive}"
        
        # 尝试从缓存获取
        permissions = cache.get(cache_key)
        if permissions is not None:
            return permissions
        
        # 获取用户的所有角色
        user_roles = PermissionService.get_user_roles(user, include_inactive)
        
        # 收集所有权限
        permission_ids = set()
        for role in user_roles:
            role_permissions = role.permissions.filter(is_deleted=False)
            if not include_inactive:
                role_permissions = role_permissions.filter(is_active=True)
            permission_ids.update(role_permissions.values_list('id', flat=True))
        
        # 获取权限对象
        permissions = Permission.objects.filter(id__in=permission_ids)
        if not include_inactive:
            permissions = permissions.filter(is_active=True)
        
        permissions = list(permissions.order_by('sort_order', 'name'))
        
        # 缓存结果（5分钟）
        cache.set(cache_key, permissions, 300)
        
        return permissions
    
    @staticmethod
    def check_user_permission(user, permission_code):
        """检查用户是否有指定权限（支持多级权限继承）"""
        if user.is_superuser:
            return True

        permissions = PermissionService.get_user_permissions(user)
        permission_codes = [p.code if hasattr(p, 'code') else p for p in permissions]

        # 检查直接权限
        if permission_code in permission_codes:
            return True

        # 检查权限继承（父权限包含子权限）
        # 完整的继承逻辑：支持多级权限继承
        target_permission = Permission.objects.filter(code=permission_code).first()

        if target_permission:
            # 获取目标权限的所有祖先权限ID
            ancestors = target_permission.get_ancestors()
            ancestor_ids = [a.id for a in ancestors]

            for perm in permissions:
                # 检查用户是否拥有目标权限的任何祖先权限（通过ID比较）
                if perm.id in ancestor_ids:
                    return True

        return False

    @staticmethod
    def get_user_data_scope_departments(user):
        """获取用户数据范围内的部门"""
        from apps.departments.models import Department

        if user.is_superuser:
            return Department.objects.filter(is_active=True)

        user_roles = PermissionService.get_user_roles(user)
        department_ids = set()

        for role in user_roles:
            user_role = UserRole.objects.filter(user=user, role=role).first()
            if not user_role or not user_role.department:
                continue

            dept = user_role.department

            if role.data_scope == 'ALL':
                # 全部数据权限
                return Department.objects.filter(is_active=True)
            elif role.data_scope == 'DEPT_AND_SUB':
                # 本部门及下级部门
                department_ids.add(dept.id)
                department_ids.update(dept.get_descendants().values_list('id', flat=True))
            elif role.data_scope == 'DEPT_ONLY':
                # 仅本部门
                department_ids.add(dept.id)
            # SELF_ONLY 不添加部门权限

        return Department.objects.filter(id__in=department_ids, is_active=True)

    @staticmethod
    def filter_queryset_by_data_scope(queryset, user, resource_type):
        """根据数据范围过滤查询集"""
        if user.is_superuser:
            return queryset

        # 这里简化实现，实际应该根据resource_type进行不同的过滤
        departments = PermissionService.get_user_data_scope_departments(user)
        dept_ids = list(departments.values_list('id', flat=True))

        # 根据资源类型进行过滤
        if resource_type == 'user':
            # 过滤用户数据（假设用户有department字段）
            return queryset  # 简化实现

        return queryset

    @staticmethod
    def check_data_scope_permission(user, resource_id, resource_type):
        """检查数据范围权限"""
        if user.is_superuser:
            return True

        if resource_type == 'department':
            departments = PermissionService.get_user_data_scope_departments(user)
            return departments.filter(id=resource_id).exists()

        return False

    @staticmethod
    def get_user_roles(user, include_inactive=False):
        """获取用户的所有角色（合并多部门角色）"""
        cache_key = f"user_roles_{user.id}_{include_inactive}"
        
        # 尝试从缓存获取
        roles = cache.get(cache_key)
        if roles is not None:
            return roles
        
        # 获取用户的有效部门关联
        from apps.users.models import UserDepartment
        user_departments = UserDepartment.get_effective_relations(user=user)
        department_ids = list(user_departments.values_list('department_id', flat=True))

        # 获取用户角色关联
        user_role_query = UserRole.objects.filter(
            user=user,
            is_deleted=False
        ).filter(
            Q(department__isnull=True) |  # 全局角色
            Q(department_id__in=department_ids)  # 部门角色
        ).select_related('role')
        
        # 过滤非活跃角色
        if not include_inactive:
            user_role_query = user_role_query.filter(role__is_active=True)
        
        # 获取唯一角色
        role_ids = set(user_role_query.values_list('role_id', flat=True))
        roles = list(Role.objects.filter(id__in=role_ids, is_deleted=False))
        
        # 缓存结果（5分钟）
        cache.set(cache_key, roles, 300)
        
        return roles
    
    @staticmethod
    def get_user_data_scope(user):
        """获取用户的数据范围权限（取最宽泛的）"""
        cache_key = f"user_data_scope_{user.id}"
        
        # 尝试从缓存获取
        data_scope = cache.get(cache_key)
        if data_scope is not None:
            return data_scope
        
        # 获取用户角色
        roles = PermissionService.get_user_roles(user)
        
        # 数据范围优先级：ALL > DEPT_AND_SUB > DEPT_ONLY > SELF_ONLY > CUSTOM
        scope_priority = {
            'ALL': 4,
            'DEPT_AND_SUB': 3,
            'DEPT_ONLY': 2,
            'SELF_ONLY': 1,
            'CUSTOM': 0
        }
        
        max_scope = 'SELF_ONLY'  # 默认最小权限
        max_priority = 0
        
        for role in roles:
            priority = scope_priority.get(role.data_scope, 0)
            if priority > max_priority:
                max_priority = priority
                max_scope = role.data_scope
        
        # 缓存结果（5分钟）
        cache.set(cache_key, max_scope, 300)
        
        return max_scope
    
    # 重复的check_user_permission方法已删除，使用上面的完整版本
    
    @staticmethod
    def get_user_menu_permissions(user):
        """获取用户的菜单权限（用于前端菜单生成）"""
        cache_key = f"user_menu_permissions_{user.id}"
        
        # 尝试从缓存获取
        menu_permissions = cache.get(cache_key)
        if menu_permissions is not None:
            return menu_permissions
        
        # 获取用户的菜单权限
        permissions = PermissionService.get_user_permissions(user)
        menu_permissions = [p for p in permissions if p.permission_type == 'MENU']
        
        # 构建树形结构
        menu_tree = PermissionService._build_permission_tree(menu_permissions)
        
        # 缓存结果（10分钟）
        cache.set(cache_key, menu_tree, 600)
        
        return menu_tree
    
    @staticmethod
    def get_user_button_permissions(user):
        """获取用户的按钮权限"""
        cache_key = f"user_button_permissions_{user.id}"
        
        # 尝试从缓存获取
        button_permissions = cache.get(cache_key)
        if button_permissions is not None:
            return button_permissions
        
        # 获取用户的按钮权限
        permissions = PermissionService.get_user_permissions(user)
        button_permissions = [p.code for p in permissions if p.permission_type == 'BUTTON']
        
        # 缓存结果（5分钟）
        cache.set(cache_key, button_permissions, 300)
        
        return button_permissions
    
    @staticmethod
    def get_user_api_permissions(user):
        """获取用户的API权限"""
        cache_key = f"user_api_permissions_{user.id}"
        
        # 尝试从缓存获取
        api_permissions = cache.get(cache_key)
        if api_permissions is not None:
            return api_permissions
        
        # 获取用户的API权限
        permissions = PermissionService.get_user_permissions(user)
        api_permissions = {}
        
        for permission in permissions:
            if permission.permission_type == 'API' and permission.path:
                method = permission.http_method or 'GET'
                if permission.path not in api_permissions:
                    api_permissions[permission.path] = []
                api_permissions[permission.path].append(method)
        
        # 缓存结果（5分钟）
        cache.set(cache_key, api_permissions, 300)
        
        return api_permissions
    
    @staticmethod
    def _build_permission_tree(permissions):
        """构建权限树形结构"""
        # 创建权限字典
        permission_dict = {p.id: {
            'id': p.id,
            'name': p.name,
            'code': p.code,
            'path': p.path,
            'component': p.component,
            'icon': p.icon,
            'sort_order': p.sort_order,
            'parent_id': p.parent_id,
            'children': []
        } for p in permissions}
        
        # 构建树形结构
        root_permissions = []
        for permission in permission_dict.values():
            if permission['parent_id'] is None:
                root_permissions.append(permission)
            else:
                parent = permission_dict.get(permission['parent_id'])
                if parent:
                    parent['children'].append(permission)
        
        # 排序
        def sort_permissions(perms):
            perms.sort(key=lambda x: (x['sort_order'], x['name']))
            for perm in perms:
                if perm['children']:
                    sort_permissions(perm['children'])
        
        sort_permissions(root_permissions)
        return root_permissions
    
    @staticmethod
    def clear_user_permission_cache(user_id):
        """清除用户权限缓存"""
        cache_keys = [
            f"user_permissions_{user_id}_True",
            f"user_permissions_{user_id}_False",
            f"user_roles_{user_id}_True",
            f"user_roles_{user_id}_False",
            f"user_data_scope_{user_id}",
            f"user_menu_permissions_{user_id}",
            f"user_button_permissions_{user_id}",
            f"user_api_permissions_{user_id}",
        ]
        
        # 删除权限检查缓存（模糊匹配）
        cache.delete_many(cache_keys)
        
        # 清除权限检查缓存
        # 注意：这里需要根据实际缓存后端实现来清除模糊匹配的缓存
        # 如果使用Redis，可以使用SCAN命令
    
    @staticmethod
    def refresh_user_permissions(user_id):
        """刷新用户权限缓存"""
        PermissionService.clear_user_permission_cache(user_id)
        
        # 预热缓存
        try:
            user = User.objects.get(id=user_id)
            PermissionService.get_user_permissions(user)
            PermissionService.get_user_roles(user)
            PermissionService.get_user_data_scope(user)
            PermissionService.get_user_menu_permissions(user)
            PermissionService.get_user_button_permissions(user)
            PermissionService.get_user_api_permissions(user)
        except User.DoesNotExist:
            pass


class RoleService:
    """角色服务类"""
    
    @staticmethod
    def check_role_can_delete(role):
        """检查角色是否可以删除"""
        # 检查是否有用户使用该角色
        if UserRole.objects.filter(role=role, is_deleted=False).exists():
            return False, "该角色正在被用户使用，无法删除"
        
        return True, ""
    
    @staticmethod
    def get_role_users(role):
        """获取角色的所有用户"""
        user_roles = UserRole.objects.filter(
            role=role, 
            is_deleted=False
        ).select_related('user', 'department')
        
        return user_roles
    
    @staticmethod
    def assign_role_to_users(role, user_ids, department_id=None):
        """批量分配角色给用户"""
        from django.db import transaction
        
        with transaction.atomic():
            user_roles = []
            for user_id in user_ids:
                # 检查是否已存在
                if not UserRole.objects.filter(
                    user_id=user_id,
                    role=role,
                    department_id=department_id,
                    is_deleted=False
                ).exists():
                    user_roles.append(UserRole(
                        user_id=user_id,
                        role=role,
                        department_id=department_id
                    ))
            
            UserRole.objects.bulk_create(user_roles)
            
            # 清除相关用户的权限缓存
            for user_id in user_ids:
                PermissionService.clear_user_permission_cache(user_id)
            
            return len(user_roles)
