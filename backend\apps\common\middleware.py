"""
公共模块 - 中间件
"""
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.utils import timezone
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from apps.audit.models import OperationLog
from apps.authentication.models import UserSession
import time
import json


class JWTAuthenticationMiddleware(MiddlewareMixin):
    """JWT认证中间件"""
    
    def process_request(self, request):
        # 跳过不需要认证的路径
        skip_paths = [
            '/api/auth/login',
            '/api/auth/captcha',
            '/api/auth/refresh',
            '/admin/',
            '/static/',
            '/media/',
        ]
        
        # 检查是否需要跳过认证
        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return None
        
        # JWT令牌验证
        jwt_auth = JWTAuthentication()
        try:
            user_token = jwt_auth.authenticate(request)
            if user_token:
                user, token = user_token
                request.user = user
                request.token = token

                # 验证会话是否有效
                session_id = token.payload.get('session_id')
                if session_id:
                    try:
                        session = UserSession.objects.get(
                            id=session_id,
                            user=user,
                            is_active=True,
                            expires_at__gt=timezone.now()
                        )
                        # 更新会话活动时间
                        session.last_activity = timezone.now()
                        session.save(update_fields=['last_activity'])
                        request.session_obj = session
                    except UserSession.DoesNotExist:
                        # 会话无效，返回401
                        if request.path.startswith('/api/'):
                            return JsonResponse({
                                'code': 2003,
                                'message': '会话已失效，请重新登录',
                                'data': None,
                                'timestamp': timezone.now().isoformat()
                            }, status=401)
        except (InvalidToken, TokenError):
            # 对于API请求返回401
            if request.path.startswith('/api/'):
                return JsonResponse({
                    'code': 2003,
                    'message': '令牌无效或已过期',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=401)
        
        return None


class PermissionMiddleware(MiddlewareMixin):
    """权限验证中间件"""

    def process_request(self, request):
        # 跳过不需要权限验证的路径
        skip_paths = [
            '/api/auth/',
            '/admin/',
            '/static/',
            '/media/',
        ]

        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return None

        # 检查用户权限
        if hasattr(request, 'user') and request.user.is_authenticated:
            # 将数据范围权限服务添加到请求中
            from apps.common.permissions import DataScopeService
            request.data_scope_service = DataScopeService

            # 超级管理员跳过权限检查
            if request.user.is_superuser:
                return None

            # 暂时跳过API权限检查，因为PermissionService不存在
            # 后续可以根据需要重新启用
            # if request.path.startswith('/api/'):
            #     return self._check_api_permission(request)

        elif request.path.startswith('/api/'):
            return JsonResponse({
                'code': 2101,
                'message': '权限不足',
                'data': None,
                'timestamp': timezone.now().isoformat()
            }, status=403)

        return None

    def _check_api_permission(self, request):
        """检查API权限（暂时禁用）"""
        # 暂时禁用API权限检查，因为PermissionService不存在
        # 后续可以根据需要重新实现
        return None

    def _path_matches(self, request_path, permission_path):
        """检查路径是否匹配（支持简单的通配符）"""
        # 支持 * 通配符
        if '*' in permission_path:
            import re
            pattern = permission_path.replace('*', '.*')
            return re.match(f'^{pattern}$', request_path) is not None

        return request_path == permission_path


class AuditLogMiddleware(MiddlewareMixin):
    """审计日志中间件"""

    def process_request(self, request):
        # 记录请求开始时间
        request._start_time = time.time()

        # 记录请求体大小（用于监控）
        if hasattr(request, 'body'):
            request._request_size = len(request.body)

        return None

    def process_response(self, request, response):
        # 跳过不需要记录的路径
        skip_paths = [
            '/static/',
            '/media/',
            '/api/auth/captcha',
            '/api/auth/refresh',  # 令牌刷新不需要记录
            '/favicon.ico',
            '/robots.txt',
            '/health/',  # 健康检查接口
        ]

        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return response

        # 记录操作日志
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                # 计算响应时间
                response_time = int((time.time() - getattr(request, '_start_time', time.time())) * 1000)

                # 确定操作类型
                operation_type = self._get_operation_type(request)

                # 生成操作描述
                operation_desc = self._get_operation_desc(request, response)

                # 只记录重要操作，避免日志过多
                if self._should_log_operation(request, response):
                    # 异步记录日志（这里先同步记录，后续可以改为异步）
                    OperationLog.objects.create(
                        user=request.user,
                        operation_type=operation_type,
                        operation_desc=operation_desc,
                        method=request.method,
                        path=request.path,
                        ip_address=self._get_client_ip(request),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')[:500],
                        status_code=response.status_code,
                        response_time=response_time
                    )
            except Exception as e:
                # 记录日志失败不应该影响正常响应
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"记录审计日志失败: {e}", exc_info=True)

        # 记录未认证用户的敏感操作
        elif self._is_sensitive_operation(request):
            try:
                response_time = int((time.time() - getattr(request, '_start_time', time.time())) * 1000)
                operation_desc = self._get_operation_desc(request, response)

                OperationLog.objects.create(
                    user=None,  # 未认证用户
                    operation_type=self._get_operation_type(request),
                    operation_desc=operation_desc,
                    method=request.method,
                    path=request.path,
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')[:500],
                    status_code=response.status_code,
                    response_time=response_time
                )
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"记录未认证用户操作日志失败: {e}", exc_info=True)

        return response
    
    def _get_operation_type(self, request):
        """根据HTTP方法和路径确定操作类型"""
        method = request.method
        path = request.path

        # 特殊操作类型判断
        if '/api/auth/login' in path:
            return 'LOGIN'
        elif '/api/auth/logout' in path:
            return 'LOGOUT'

        # 根据HTTP方法确定操作类型
        method_mapping = {
            'GET': 'QUERY',
            'POST': 'CREATE',
            'PUT': 'UPDATE',
            'PATCH': 'UPDATE',
            'DELETE': 'DELETE',
        }
        return method_mapping.get(method, 'QUERY')

    def _get_operation_desc(self, request, response=None):
        """生成操作描述"""
        path = request.path
        method = request.method

        # 登录相关操作
        if '/api/auth/login' in path:
            if response and response.status_code == 200:
                return '用户登录成功'
            else:
                return '用户登录失败'
        elif '/api/auth/logout' in path:
            return '用户登出'

        # 用户管理操作
        elif '/api/users/' in path:
            if 'profile' in path:
                if method == 'GET':
                    return '查看个人资料'
                elif method in ['PUT', 'PATCH']:
                    return '更新个人资料'
            elif 'reset_password' in path:
                return '重置用户密码'
            elif 'change_password' in path:
                return '修改密码'
            else:
                if method == 'GET':
                    if path.endswith('/'):
                        return '查询用户列表'
                    else:
                        return '查看用户详情'
                elif method == 'POST':
                    return '创建用户'
                elif method in ['PUT', 'PATCH']:
                    return '更新用户信息'
                elif method == 'DELETE':
                    return '删除用户'

        # 部门管理操作
        elif '/api/departments/' in path:
            if 'tree' in path:
                return '查询部门树结构'
            elif 'members' in path:
                if method == 'POST':
                    return '添加部门成员'
                elif method == 'DELETE':
                    return '移除部门成员'
                else:
                    return '查询部门成员'
            else:
                if method == 'GET':
                    if path.endswith('/'):
                        return '查询部门列表'
                    else:
                        return '查看部门详情'
                elif method == 'POST':
                    return '创建部门'
                elif method in ['PUT', 'PATCH']:
                    return '更新部门信息'
                elif method == 'DELETE':
                    return '删除部门'

        # 角色权限管理操作
        elif '/api/roles/' in path:
            if 'permissions' in path:
                if method == 'POST':
                    return '分配角色权限'
                elif method == 'DELETE':
                    return '移除角色权限'
                else:
                    return '查询角色权限'
            else:
                if method == 'GET':
                    if path.endswith('/'):
                        return '查询角色列表'
                    else:
                        return '查看角色详情'
                elif method == 'POST':
                    return '创建角色'
                elif method in ['PUT', 'PATCH']:
                    return '更新角色信息'
                elif method == 'DELETE':
                    return '删除角色'

        elif '/api/permissions/' in path:
            if method == 'GET':
                if path.endswith('/'):
                    return '查询权限列表'
                else:
                    return '查看权限详情'
            elif method == 'POST':
                return '创建权限'
            elif method in ['PUT', 'PATCH']:
                return '更新权限信息'
            elif method == 'DELETE':
                return '删除权限'

        # 审计日志操作
        elif '/api/audit/' in path:
            if 'export' in path:
                return '导出审计报告'
            elif method == 'GET':
                return '查询操作日志'

        # 默认描述
        return f"{method} {path}"

    def _should_log_operation(self, request, response):
        """判断是否应该记录操作日志"""
        # 只记录重要操作，避免日志过多
        path = request.path
        method = request.method

        # 跳过不需要记录的路径
        skip_paths = [
            '/static/',
            '/media/',
            '/api/auth/captcha',
            '/api/auth/refresh',  # 令牌刷新不需要记录
            '/favicon.ico',
            '/robots.txt',
            '/health/',  # 健康检查接口
        ]

        for skip_path in skip_paths:
            if path.startswith(skip_path):
                return False

        # 总是记录的操作
        always_log_paths = [
            '/api/auth/login',
            '/api/auth/logout',
            '/api/users/',
            '/api/departments/',
            '/api/roles/',
            '/api/permissions/',
        ]

        for log_path in always_log_paths:
            if log_path in path:
                return True

        # 只记录非GET请求（修改操作）
        if method != 'GET':
            return True

        # GET请求只记录详情查看和列表查询（不记录分页请求）
        if method == 'GET' and not request.GET.get('page'):
            return True

        return False

    def _is_sensitive_operation(self, request):
        """判断是否为敏感操作（需要记录未认证用户的操作）"""
        sensitive_paths = [
            '/api/auth/login',
            '/admin/login/',
        ]

        for sensitive_path in sensitive_paths:
            if sensitive_path in request.path:
                return True

        return False

    def _get_client_ip(self, request):
        """获取客户端真实IP地址"""
        # 优先级：X-Real-IP > X-Forwarded-For > REMOTE_ADDR
        real_ip = request.META.get('HTTP_X_REAL_IP')
        if real_ip:
            return real_ip.strip()

        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # X-Forwarded-For可能包含多个IP，取第一个
            ip = x_forwarded_for.split(',')[0].strip()
            return ip

        # 最后使用REMOTE_ADDR
        return request.META.get('REMOTE_ADDR', '127.0.0.1')