<template>
  <n-card title="成员管理" class="member-card">
    <template #header-extra>
      <div class="member-actions">
        <n-button
          v-permission="'department:manage_members'"
          size="small"
          type="primary"
          @click="handleAddMember"
        >
          <template #icon>
            <n-icon><UserPlusIcon /></n-icon>
          </template>
          添加成员
        </n-button>
        <n-button
          size="small"
          @click="handleRefresh"
          :loading="loading"
        >
          <template #icon>
            <n-icon><RefreshIcon /></n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </template>
    
    <div class="member-content">
      <!-- 筛选器 -->
      <div class="member-filters">
        <n-space>
          <n-select
            v-model:value="filterManager"
            placeholder="成员类型"
            style="width: 120px"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="true" label="主管" />
            <n-option value="false" label="普通成员" />
          </n-select>
          
          <n-select
            v-model:value="filterPrimary"
            placeholder="部门类型"
            style="width: 120px"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="true" label="主部门" />
            <n-option value="false" label="兼职部门" />
          </n-select>
        </n-space>
      </div>

      <!-- 成员列表 -->
      <div v-if="loading" class="loading-container">
        <n-spin size="large" />
      </div>
      
      <div v-else-if="filteredMembers.length > 0" class="member-list">
        <div
          v-for="member in filteredMembers"
          :key="member.id"
          class="member-item"
        >
          <div class="member-info">
            <div class="member-basic">
              <n-avatar
                :size="40"
                :src="member.user_info.avatar"
                class="member-avatar"
              >
                {{ member.user_info.nickname?.charAt(0) || member.user_info.username?.charAt(0) }}
              </n-avatar>
              
              <div class="member-details">
                <div class="member-name">
                  {{ member.user_info.nickname || member.user_info.username }}
                </div>
                <div class="member-meta">
                  <span class="member-email">{{ member.user_info.email || '-' }}</span>
                </div>
              </div>
            </div>
            
            <div class="member-tags">
              <n-tag v-if="member.is_primary" type="success" size="small">
                主部门
              </n-tag>
              <n-tag v-if="member.is_manager" type="info" size="small">
                {{ getManagerLevelText(member.manager_level) }}
              </n-tag>
              <n-tag v-if="member.position" size="small">
                {{ member.position }}
              </n-tag>
              <n-tag v-if="!member.is_effective_now" type="warning" size="small">
                未生效
              </n-tag>
            </div>
          </div>
          
          <div class="member-extra">
            <div class="member-time">
              <div class="time-item">
                <label>生效时间</label>
                <span>{{ formatDate(member.effective_date) }}</span>
              </div>
              <div v-if="member.expiry_date" class="time-item">
                <label>到期时间</label>
                <span>{{ formatDate(member.expiry_date) }}</span>
              </div>
              <div v-if="member.is_manager" class="time-item">
                <label>管理权重</label>
                <span>{{ member.weight }}</span>
              </div>
            </div>
            
            <div class="member-actions-dropdown">
              <n-dropdown
                :options="getMemberActionOptions(member)"
                @select="(key) => handleMemberAction(key, member)"
              >
                <n-button size="small" quaternary>
                  <template #icon>
                    <n-icon><MoreIcon /></n-icon>
                  </template>
                </n-button>
              </n-dropdown>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <n-empty description="暂无成员">
          <template #icon>
            <n-icon size="48"><UsersIcon /></n-icon>
          </template>
          <template #extra>
            <n-button
              v-permission="'department:manage_members'"
              size="small"
              type="primary"
              @click="handleAddMember"
            >
              添加成员
            </n-button>
          </template>
        </n-empty>
      </div>
    </div>

    <!-- 添加成员对话框 -->
    <AddMemberDialog
      v-model:visible="addMemberVisible"
      :department-id="departmentId"
      @success="handleAddSuccess"
    />

    <!-- 编辑成员对话框 -->
    <EditMemberDialog
      v-model:visible="editMemberVisible"
      :member="editingMember"
      :department-id="departmentId"
      @success="handleEditSuccess"
    />
  </n-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, h } from 'vue'
import { useMessage, type DropdownOption } from 'naive-ui'
import {
  UserPlusIcon,
  RefreshIcon,
  MoreIcon,
  UsersIcon,
  EditIcon,
  TrashIcon,
  CrownIcon
} from '@vicons/tabler'
import { departmentApi } from '@/api/department'
import { hasPermission } from '@/utils/auth'
import type { UserDepartment, DepartmentMemberSearchParams } from '@/types/department'
import AddMemberDialog from './AddMemberDialog.vue'
import EditMemberDialog from './EditMemberDialog.vue'

// Props
interface Props {
  departmentId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'refresh': []
}>()

// 响应式数据
const loading = ref(false)
const members = ref<UserDepartment[]>([])
const filterManager = ref<string | null>(null)
const filterPrimary = ref<string | null>(null)
const addMemberVisible = ref(false)
const editMemberVisible = ref(false)
const editingMember = ref<UserDepartment | null>(null)

// 计算属性
const filteredMembers = computed(() => {
  let result = members.value
  
  if (filterManager.value !== null) {
    result = result.filter(member => 
      member.is_manager === (filterManager.value === 'true')
    )
  }
  
  if (filterPrimary.value !== null) {
    result = result.filter(member => 
      member.is_primary === (filterPrimary.value === 'true')
    )
  }
  
  return result
})

// 方法
const formatDate = (date?: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const getManagerLevelText = (level?: number) => {
  switch (level) {
    case 1:
      return '一级主管'
    case 2:
      return '二级主管'
    case 3:
      return '三级主管'
    default:
      return '主管'
  }
}

const fetchMembers = async () => {
  try {
    loading.value = true
    const params: DepartmentMemberSearchParams = {}
    
    if (filterManager.value !== null) {
      params.is_manager = filterManager.value === 'true'
    }
    
    if (filterPrimary.value !== null) {
      params.is_primary = filterPrimary.value === 'true'
    }
    
    const response = await departmentApi.getDepartmentMembers(props.departmentId, params)
    
    if (response.code === 200) {
      members.value = response.data
    }
  } catch (error) {
    console.error('获取部门成员失败:', error)
  } finally {
    loading.value = false
  }
}

const getMemberActionOptions = (member: UserDepartment): DropdownOption[] => {
  const options: DropdownOption[] = []
  
  // 编辑成员
  if (hasPermission('department:manage_members')) {
    options.push({
      label: '编辑成员',
      key: 'edit',
      icon: () => h(EditIcon)
    })
  }
  
  // 设为主管/取消主管
  if (hasPermission('department:manage_members')) {
    options.push({
      label: member.is_manager ? '取消主管' : '设为主管',
      key: 'toggle_manager',
      icon: () => h(CrownIcon)
    })
  }
  
  // 分隔线
  if (options.length > 0) {
    options.push({ type: 'divider' })
  }
  
  // 移除成员
  if (hasPermission('department:manage_members')) {
    options.push({
      label: '移除成员',
      key: 'remove',
      icon: () => h(TrashIcon)
    })
  }
  
  return options
}

const handleFilter = () => {
  fetchMembers()
}

const handleAddMember = () => {
  addMemberVisible.value = true
}

const handleRefresh = () => {
  fetchMembers()
  emit('refresh')
}

const handleMemberAction = async (key: string, member: UserDepartment) => {
  switch (key) {
    case 'edit':
      editingMember.value = member
      editMemberVisible.value = true
      break
      
    case 'toggle_manager':
      await handleToggleManager(member)
      break
      
    case 'remove':
      await handleRemoveMember(member)
      break
  }
}

const handleToggleManager = async (member: UserDepartment) => {
  try {
    const updateData = {
      user_id: member.user,
      is_manager: !member.is_manager,
      manager_level: member.is_manager ? null : 1,
      weight: member.weight
    }
    
    await departmentApi.updateDepartmentMember(props.departmentId, updateData)
    message.success(member.is_manager ? '取消主管成功' : '设为主管成功')
    await fetchMembers()
  } catch (error) {
    message.error('操作失败')
  }
}

const handleRemoveMember = async (member: UserDepartment) => {
  try {
    await departmentApi.removeDepartmentMember(props.departmentId, member.user)
    message.success('移除成员成功')
    await fetchMembers()
  } catch (error) {
    message.error('移除成员失败')
  }
}

const handleAddSuccess = () => {
  addMemberVisible.value = false
  message.success('添加成员成功')
  fetchMembers()
}

const handleEditSuccess = () => {
  editMemberVisible.value = false
  message.success('编辑成员成功')
  fetchMembers()
}

// 监听器
watch(() => props.departmentId, (newId) => {
  if (newId) {
    fetchMembers()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.departmentId) {
    fetchMembers()
  }
})
</script>

<style scoped>
.member-card {
  margin-bottom: 24px;
}

.member-actions {
  display: flex;
  gap: 8px;
}

.member-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.member-filters {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.member-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.member-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.member-basic {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-avatar {
  flex-shrink: 0;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.member-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.member-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-email {
  font-size: 12px;
  color: #6b7280;
}

.member-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.member-extra {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.member-time {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
}

.time-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-item label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.time-item span {
  font-size: 12px;
  color: #1f2937;
}

.member-actions-dropdown {
  flex-shrink: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
