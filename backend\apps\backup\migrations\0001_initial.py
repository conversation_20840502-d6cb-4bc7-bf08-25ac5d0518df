# Generated by Django 4.2.23 on 2025-08-08 09:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BackupJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=200, verbose_name='备份名称')),
                ('description', models.TextField(blank=True, verbose_name='备份描述')),
                ('backup_type', models.CharField(choices=[('full', '全量备份'), ('incremental', '增量备份'), ('differential', '差异备份')], max_length=20, verbose_name='备份类型')),
                ('compression', models.CharField(choices=[('none', '无压缩'), ('gzip', 'GZIP压缩'), ('bzip2', 'BZIP2压缩'), ('lzma', 'LZMA压缩')], default='gzip', max_length=20, verbose_name='压缩方式')),
                ('include_tables', models.JSONField(default=list, verbose_name='包含的表')),
                ('exclude_tables', models.JSONField(default=list, verbose_name='排除的表')),
                ('include_media', models.BooleanField(default=True, verbose_name='包含媒体文件')),
                ('include_logs', models.BooleanField(default=False, verbose_name='包含日志文件')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='任务状态')),
                ('progress', models.IntegerField(default=0, verbose_name='进度百分比')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True, verbose_name='计划执行时间')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('execution_log', models.TextField(blank=True, verbose_name='执行日志')),
                ('backup_file_path', models.CharField(blank=True, max_length=500, verbose_name='备份文件路径')),
                ('backup_file_size', models.BigIntegerField(default=0, verbose_name='备份文件大小(bytes)')),
                ('backup_file_hash', models.CharField(blank=True, max_length=64, verbose_name='备份文件哈希')),
                ('retention_days', models.IntegerField(default=30, verbose_name='保留天数')),
                ('auto_cleanup', models.BooleanField(default=True, verbose_name='自动清理')),
                ('executed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='执行人')),
            ],
            options={
                'verbose_name': '备份任务',
                'verbose_name_plural': '备份任务',
                'db_table': 'backup_job',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BackupStorage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=100, verbose_name='存储名称')),
                ('storage_type', models.CharField(choices=[('local', '本地存储'), ('ftp', 'FTP服务器'), ('sftp', 'SFTP服务器'), ('s3', 'Amazon S3'), ('oss', '阿里云OSS'), ('cos', '腾讯云COS')], max_length=20, verbose_name='存储类型')),
                ('config', models.JSONField(default=dict, verbose_name='存储配置')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认')),
                ('total_capacity', models.BigIntegerField(blank=True, null=True, verbose_name='总容量(bytes)')),
                ('used_capacity', models.BigIntegerField(default=0, verbose_name='已用容量(bytes)')),
                ('last_test_time', models.DateTimeField(blank=True, null=True, verbose_name='上次测试时间')),
                ('test_result', models.BooleanField(default=False, verbose_name='测试结果')),
                ('test_message', models.TextField(blank=True, verbose_name='测试信息')),
            ],
            options={
                'verbose_name': '备份存储',
                'verbose_name_plural': '备份存储',
                'db_table': 'backup_storage',
                'indexes': [models.Index(fields=['storage_type', 'is_active'], name='backup_stor_storage_9f745b_idx'), models.Index(fields=['is_default'], name='backup_stor_is_defa_b59b2a_idx')],
            },
        ),
        migrations.CreateModel(
            name='BackupSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=200, verbose_name='计划名称')),
                ('description', models.TextField(blank=True, verbose_name='计划描述')),
                ('frequency', models.CharField(choices=[('hourly', '每小时'), ('daily', '每天'), ('weekly', '每周'), ('monthly', '每月'), ('custom', '自定义')], max_length=20, verbose_name='执行频率')),
                ('cron_expression', models.CharField(blank=True, max_length=100, verbose_name='Cron表达式')),
                ('backup_type', models.CharField(choices=[('full', '全量备份'), ('incremental', '增量备份'), ('differential', '差异备份')], max_length=20, verbose_name='备份类型')),
                ('compression', models.CharField(choices=[('none', '无压缩'), ('gzip', 'GZIP压缩'), ('bzip2', 'BZIP2压缩'), ('lzma', 'LZMA压缩')], default='gzip', max_length=20, verbose_name='压缩方式')),
                ('include_tables', models.JSONField(default=list, verbose_name='包含的表')),
                ('exclude_tables', models.JSONField(default=list, verbose_name='排除的表')),
                ('include_media', models.BooleanField(default=True, verbose_name='包含媒体文件')),
                ('include_logs', models.BooleanField(default=False, verbose_name='包含日志文件')),
                ('retention_days', models.IntegerField(default=30, verbose_name='保留天数')),
                ('max_backups', models.IntegerField(default=10, verbose_name='最大备份数量')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('next_run_time', models.DateTimeField(blank=True, null=True, verbose_name='下次执行时间')),
                ('last_run_time', models.DateTimeField(blank=True, null=True, verbose_name='上次执行时间')),
                ('notify_on_success', models.BooleanField(default=False, verbose_name='成功时通知')),
                ('notify_on_failure', models.BooleanField(default=True, verbose_name='失败时通知')),
                ('notification_emails', models.JSONField(default=list, verbose_name='通知邮箱')),
            ],
            options={
                'verbose_name': '备份计划',
                'verbose_name_plural': '备份计划',
                'db_table': 'backup_schedule',
                'ordering': ['next_run_time'],
                'indexes': [models.Index(fields=['is_active', 'next_run_time'], name='backup_sche_is_acti_19f03a_idx'), models.Index(fields=['frequency'], name='backup_sche_frequen_eeb5ca_idx')],
            },
        ),
        migrations.CreateModel(
            name='RestoreJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=200, verbose_name='恢复名称')),
                ('description', models.TextField(blank=True, verbose_name='恢复描述')),
                ('restore_mode', models.CharField(choices=[('full', '完全恢复'), ('partial', '部分恢复'), ('schema_only', '仅结构'), ('data_only', '仅数据')], max_length=20, verbose_name='恢复模式')),
                ('target_database', models.CharField(blank=True, max_length=100, verbose_name='目标数据库')),
                ('include_tables', models.JSONField(default=list, verbose_name='包含的表')),
                ('exclude_tables', models.JSONField(default=list, verbose_name='排除的表')),
                ('restore_media', models.BooleanField(default=True, verbose_name='恢复媒体文件')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='任务状态')),
                ('progress', models.IntegerField(default=0, verbose_name='进度百分比')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True, verbose_name='计划执行时间')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('execution_log', models.TextField(blank=True, verbose_name='执行日志')),
                ('pre_restore_verification', models.BooleanField(default=True, verbose_name='恢复前验证')),
                ('post_restore_verification', models.BooleanField(default=True, verbose_name='恢复后验证')),
                ('verification_passed', models.BooleanField(default=False, verbose_name='验证通过')),
                ('backup_job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='backup.backupjob', verbose_name='备份任务')),
                ('executed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='执行人')),
            ],
            options={
                'verbose_name': '恢复任务',
                'verbose_name_plural': '恢复任务',
                'db_table': 'backup_restore_job',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status', 'created_at'], name='backup_rest_status_a4e644_idx'), models.Index(fields=['backup_job', 'created_at'], name='backup_rest_backup__9afc8d_idx'), models.Index(fields=['scheduled_at'], name='backup_rest_schedul_922edb_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='backupjob',
            index=models.Index(fields=['status', 'created_at'], name='backup_job_status_b7a61e_idx'),
        ),
        migrations.AddIndex(
            model_name='backupjob',
            index=models.Index(fields=['backup_type', 'created_at'], name='backup_job_backup__29d060_idx'),
        ),
        migrations.AddIndex(
            model_name='backupjob',
            index=models.Index(fields=['scheduled_at'], name='backup_job_schedul_ce3d97_idx'),
        ),
        migrations.AddIndex(
            model_name='backupjob',
            index=models.Index(fields=['completed_at'], name='backup_job_complet_cfc447_idx'),
        ),
    ]
