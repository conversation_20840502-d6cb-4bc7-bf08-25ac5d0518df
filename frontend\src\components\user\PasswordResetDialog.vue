<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="false"
    preset="dialog"
    title="重置密码"
    class="password-reset-dialog"
    style="width: 500px"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="新密码" path="new_password">
        <n-input
          v-model:value="formData.new_password"
          type="password"
          placeholder="请输入新密码"
          show-password-on="mousedown"
        />
      </n-form-item>
      
      <n-form-item label="确认密码" path="confirm_password">
        <n-input
          v-model:value="formData.confirm_password"
          type="password"
          placeholder="请再次输入新密码"
          show-password-on="mousedown"
        />
      </n-form-item>
    </n-form>
    
    <n-alert type="warning" class="reset-warning">
      <template #icon>
        <n-icon><WarningIcon /></n-icon>
      </template>
      重置密码后，用户需要使用新密码重新登录。请确保将新密码安全地告知用户。
    </n-alert>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          重置密码
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { WarningIcon } from '@vicons/tabler'
import { userApi } from '@/api/user'
import type { PasswordResetForm } from '@/types/user'

// Props
interface Props {
  visible: boolean
  userId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  userId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)

// 表单数据
const formData = ref<PasswordResetForm>({
  new_password: '',
  confirm_password: ''
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单验证规则
const formRules: FormRules = {
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: '密码必须包含大小写字母和数字',
      trigger: 'blur'
    }
  ],
  confirm_password: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        return value === formData.value.new_password
      },
      message: '两次输入的密码不一致',
      trigger: 'blur'
    }
  ]
}

// 方法
const resetForm = () => {
  formData.value = {
    new_password: '',
    confirm_password: ''
  }
}

const handleSubmit = async () => {
  if (!props.userId) return
  
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    const response = await userApi.resetUserPassword(props.userId, formData.value)
    
    if (response.code === 200) {
      emit('success')
    }
  } catch (error) {
    console.error('重置密码失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      resetForm()
    })
  }
})
</script>

<style scoped>
.password-reset-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.reset-warning {
  margin: 16px 0;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
