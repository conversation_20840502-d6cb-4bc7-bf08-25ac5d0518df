<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">权限系统测试页面</h1>
      <p class="text-gray-600">测试各种权限控制功能</p>
    </div>

    <!-- 用户信息 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4">当前用户信息</h2>
      <div v-if="authStore.userInfo" class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-500">用户名:</label>
          <p class="text-gray-900">{{ authStore.userInfo.username }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-500">昵称:</label>
          <p class="text-gray-900">{{ authStore.userInfo.nickname }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-500">当前部门:</label>
          <p class="text-gray-900">{{ authStore.currentDeptInfo?.name || '未设置' }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-500">是否多部门用户:</label>
          <p class="text-gray-900">{{ authStore.isMultiDepartmentUser ? '是' : '否' }}</p>
        </div>
      </div>
    </div>

    <!-- 权限列表 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4">用户权限列表</h2>
      <div v-if="authStore.permissions.length > 0" class="flex flex-wrap gap-2">
        <span 
          v-for="permission in authStore.permissions" 
          :key="permission"
          class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
        >
          {{ formatPermissionName(permission) }}
        </span>
      </div>
      <p v-else class="text-gray-500">暂无权限</p>
    </div>

    <!-- 权限指令测试 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4">权限指令测试</h2>
      <div class="space-y-4">
        <!-- v-permission 测试 -->
        <div>
          <h3 class="font-medium mb-2">v-permission 指令测试</h3>
          <div class="space-x-2">
            <n-button v-permission="'user:create'" type="primary">
              创建用户 (需要 user:create 权限)
            </n-button>
            <n-button v-permission="'user:delete'" type="error">
              删除用户 (需要 user:delete 权限)
            </n-button>
            <n-button v-permission="['admin:manage', 'super:admin']" type="warning">
              管理员功能 (需要管理员权限)
            </n-button>
          </div>
        </div>

        <!-- v-permission-hide 测试 -->
        <div>
          <h3 class="font-medium mb-2">v-permission-hide 指令测试</h3>
          <div class="space-x-2">
            <n-button v-permission-hide="'system:config'" type="info">
              系统配置 (需要 system:config 权限)
            </n-button>
            <n-button v-permission-hide="'audit:view'" type="default">
              查看审计日志 (需要 audit:view 权限)
            </n-button>
          </div>
        </div>

        <!-- v-permission-disable 测试 -->
        <div>
          <h3 class="font-medium mb-2">v-permission-disable 指令测试</h3>
          <div class="space-x-2">
            <n-button v-permission-disable="'data:export'" type="success">
              导出数据 (需要 data:export 权限)
            </n-button>
            <n-button v-permission-disable="'backup:create'" type="warning">
              创建备份 (需要 backup:create 权限)
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限检查函数测试 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4">权限检查函数测试</h2>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span>hasPermission('user:create'):</span>
          <span :class="hasPermission('user:create') ? 'text-green-600' : 'text-red-600'">
            {{ hasPermission('user:create') ? '✓ 有权限' : '✗ 无权限' }}
          </span>
        </div>
        <div class="flex justify-between">
          <span>hasPermission(['user:create', 'user:update']):</span>
          <span :class="hasPermission(['user:create', 'user:update']) ? 'text-green-600' : 'text-red-600'">
            {{ hasPermission(['user:create', 'user:update']) ? '✓ 有权限' : '✗ 无权限' }}
          </span>
        </div>
        <div class="flex justify-between">
          <span>hasAllPermissions(['user:create', 'user:update']):</span>
          <span :class="hasAllPermissions(['user:create', 'user:update']) ? 'text-green-600' : 'text-red-600'">
            {{ hasAllPermissions(['user:create', 'user:update']) ? '✓ 有权限' : '✗ 无权限' }}
          </span>
        </div>
        <div class="flex justify-between">
          <span>isSuperAdmin():</span>
          <span :class="isSuperAdmin() ? 'text-green-600' : 'text-red-600'">
            {{ isSuperAdmin() ? '✓ 是超级管理员' : '✗ 不是超级管理员' }}
          </span>
        </div>
      </div>
    </div>

    <!-- 权限事件测试 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4">权限事件测试</h2>
      <div class="space-y-4">
        <div>
          <n-button @click="simulatePermissionChange" type="primary">
            模拟权限变化
          </n-button>
          <n-button @click="simulateDepartmentChange" type="info" class="ml-2">
            模拟部门切换
          </n-button>
          <n-button @click="clearEventHistory" type="default" class="ml-2">
            清除事件历史
          </n-button>
        </div>
        
        <div>
          <h3 class="font-medium mb-2">事件历史 (最近10条)</h3>
          <div v-if="eventHistory.length > 0" class="space-y-1 max-h-40 overflow-y-auto">
            <div 
              v-for="(event, index) in eventHistory.slice(-10)" 
              :key="index"
              class="text-sm p-2 bg-gray-50 rounded"
            >
              <span class="font-medium">{{ event.type }}</span>
              <span class="text-gray-500 ml-2">{{ formatTime(event.timestamp) }}</span>
            </div>
          </div>
          <p v-else class="text-gray-500 text-sm">暂无事件历史</p>
        </div>
      </div>
    </div>

    <!-- 部门切换测试 -->
    <div v-if="authStore.isMultiDepartmentUser" class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-semibold mb-4">部门切换测试</h2>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">可用部门:</label>
          <div class="space-y-2">
            <div 
              v-for="dept in authStore.departments" 
              :key="dept.id"
              class="flex items-center justify-between p-3 border rounded"
              :class="dept.id === authStore.currentDepartment ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
            >
              <div>
                <span class="font-medium">{{ dept.name }}</span>
                <span v-if="dept.is_primary" class="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                  主部门
                </span>
                <span v-if="dept.is_manager" class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  管理者
                </span>
              </div>
              <n-button 
                v-if="dept.id !== authStore.currentDepartment"
                @click="switchToDepartment(dept.id)"
                size="small"
                type="primary"
              >
                切换
              </n-button>
              <span v-else class="text-blue-600 font-medium">当前部门</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import { 
  hasPermission, 
  hasAllPermissions, 
  isSuperAdmin, 
  formatPermissionName 
} from '@/utils/auth'
import { 
  usePermissionEvents,
  emitPermissionChange,
  emitDepartmentChange
} from '@/utils/permission-events'

const authStore = useAuthStore()
const message = useMessage()
const { getEventHistory, clearEventHistory: clearHistory } = usePermissionEvents()

// 事件历史
const eventHistory = ref<any[]>([])

// 更新事件历史
const updateEventHistory = () => {
  eventHistory.value = getEventHistory()
}

// 模拟权限变化
const simulatePermissionChange = () => {
  const newPermissions = [...authStore.permissions, 'test:permission']
  emitPermissionChange(newPermissions)
  message.info('已触发权限变化事件')
  updateEventHistory()
}

// 模拟部门切换
const simulateDepartmentChange = () => {
  if (authStore.currentDepartment && authStore.currentDeptInfo) {
    emitDepartmentChange(authStore.currentDepartment, authStore.currentDeptInfo)
    message.info('已触发部门切换事件')
    updateEventHistory()
  }
}

// 清除事件历史
const clearEventHistory = () => {
  clearHistory()
  updateEventHistory()
  message.success('已清除事件历史')
}

// 切换部门
const switchToDepartment = async (departmentId: number) => {
  try {
    await authStore.switchDepartment(departmentId)
    message.success('部门切换成功')
    updateEventHistory()
  } catch (error) {
    message.error('部门切换失败')
  }
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

onMounted(() => {
  updateEventHistory()
})
</script>
