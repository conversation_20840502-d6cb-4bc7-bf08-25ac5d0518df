/**
 * 部门管理状态管理
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { departmentApi } from '@/api/department'
import type {
  Department,
  DepartmentTree,
  DepartmentListItem,
  DepartmentDetail,
  DepartmentTreeNode,
  DepartmentSearchParams,
  DepartmentCreateForm,
  DepartmentEditForm,
  DepartmentStats,
  UserDepartment,
  DepartmentMemberForm,
  DepartmentBatchOperationParams
} from '@/types/department'

// 分页信息类型
interface PaginationInfo {
  page: number
  page_size: number
  total: number
  total_pages: number
}

export const useDepartmentStore = defineStore('department', () => {
  // 状态
  const departmentList = ref<DepartmentListItem[]>([])
  const departmentTree = ref<DepartmentTree[]>([])
  const currentDepartment = ref<DepartmentDetail | null>(null)
  const departmentStats = ref<DepartmentStats | null>(null)
  const loading = ref(false)
  const treeLoading = ref(false)
  
  // 搜索和分页
  const searchParams = ref<DepartmentSearchParams>({
    page: 1,
    page_size: 20,
    ordering: 'tree_id,lft'
  })
  const pagination = ref<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0
  })
  
  // 选中状态
  const selectedDepartmentIds = ref<number[]>([])
  const selectedDepartmentId = ref<number | null>(null)
  
  // 树形组件状态
  const expandedKeys = ref<number[]>([])
  const checkedKeys = ref<number[]>([])
  
  // 计算属性
  const hasSelectedDepartments = computed(() => selectedDepartmentIds.value.length > 0)
  const selectedDepartmentsCount = computed(() => selectedDepartmentIds.value.length)
  
  // 获取部门列表
  const fetchDepartmentList = async (params?: DepartmentSearchParams) => {
    try {
      loading.value = true
      
      const mergedParams = { ...searchParams.value, ...params }
      searchParams.value = mergedParams
      
      const response = await departmentApi.getDepartmentList(mergedParams)
      
      if (response.code === 200) {
        departmentList.value = response.data.results
        
        pagination.value = {
          page: mergedParams.page || 1,
          page_size: mergedParams.page_size || 20,
          total: response.data.count,
          total_pages: Math.ceil(response.data.count / (mergedParams.page_size || 20))
        }
      }
      
      return response
    } catch (error) {
      console.error('获取部门列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取部门树形结构
  const fetchDepartmentTree = async () => {
    try {
      treeLoading.value = true
      const response = await departmentApi.getDepartmentTree()
      
      if (response.code === 200) {
        departmentTree.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取部门树形结构失败:', error)
      throw error
    } finally {
      treeLoading.value = false
    }
  }
  
  // 获取部门详情
  const fetchDepartmentDetail = async (id: number) => {
    try {
      loading.value = true
      const response = await departmentApi.getDepartmentDetail(id)
      
      if (response.code === 200) {
        currentDepartment.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取部门详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 创建部门
  const createDepartment = async (data: DepartmentCreateForm) => {
    try {
      loading.value = true
      const response = await departmentApi.createDepartment(data)
      
      if (response.code === 200 || response.code === 1001) {
        // 重新获取部门列表和树形结构
        await Promise.all([
          fetchDepartmentList(),
          fetchDepartmentTree()
        ])
      }
      
      return response
    } catch (error) {
      console.error('创建部门失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 更新部门
  const updateDepartment = async (id: number, data: DepartmentEditForm) => {
    try {
      loading.value = true
      const response = await departmentApi.updateDepartment(id, data)
      
      if (response.code === 200) {
        // 更新列表中的部门信息
        const index = departmentList.value.findIndex(dept => dept.id === id)
        if (index !== -1) {
          departmentList.value[index] = { ...departmentList.value[index], ...response.data }
        }
        
        // 如果是当前查看的部门，更新详情
        if (currentDepartment.value?.id === id) {
          currentDepartment.value = { ...currentDepartment.value, ...response.data }
        }
        
        // 重新获取树形结构
        await fetchDepartmentTree()
      }
      
      return response
    } catch (error) {
      console.error('更新部门失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 删除部门
  const deleteDepartment = async (id: number) => {
    try {
      loading.value = true
      const response = await departmentApi.deleteDepartment(id)
      
      if (response.code === 200) {
        // 从列表中移除部门
        departmentList.value = departmentList.value.filter(dept => dept.id !== id)
        
        // 更新分页信息
        pagination.value.total -= 1
        
        // 如果是当前查看的部门，清空详情
        if (currentDepartment.value?.id === id) {
          currentDepartment.value = null
        }
        
        // 重新获取树形结构
        await fetchDepartmentTree()
      }
      
      return response
    } catch (error) {
      console.error('删除部门失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 切换部门状态
  const toggleDepartmentActive = async (id: number) => {
    try {
      const response = await departmentApi.toggleDepartmentActive(id)
      
      if (response.code === 200) {
        // 更新列表中的部门状态
        const index = departmentList.value.findIndex(dept => dept.id === id)
        if (index !== -1) {
          departmentList.value[index].is_active = response.data.is_active
        }
        
        // 如果是当前查看的部门，更新详情
        if (currentDepartment.value?.id === id) {
          currentDepartment.value.is_active = response.data.is_active
        }
        
        // 重新获取树形结构
        await fetchDepartmentTree()
      }
      
      return response
    } catch (error) {
      console.error('切换部门状态失败:', error)
      throw error
    }
  }
  
  // 批量操作部门
  const batchOperateDepartments = async (params: DepartmentBatchOperationParams) => {
    try {
      loading.value = true
      const response = await departmentApi.batchOperateDepartments(params)
      
      if (response.code === 200) {
        // 重新获取部门列表和树形结构
        await Promise.all([
          fetchDepartmentList(),
          fetchDepartmentTree()
        ])
        // 清空选中的部门
        selectedDepartmentIds.value = []
      }
      
      return response
    } catch (error) {
      console.error('批量操作失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取部门统计信息
  const fetchDepartmentStats = async () => {
    try {
      const response = await departmentApi.getDepartmentStats()
      
      if (response.code === 200) {
        departmentStats.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取部门统计失败:', error)
      throw error
    }
  }
  
  // 搜索部门
  const searchDepartments = async (keyword: string) => {
    await fetchDepartmentList({ ...searchParams.value, search: keyword, page: 1 })
  }
  
  // 筛选部门
  const filterDepartments = async (filters: Partial<DepartmentSearchParams>) => {
    await fetchDepartmentList({ ...searchParams.value, ...filters, page: 1 })
  }
  
  // 排序部门
  const sortDepartments = async (ordering: string) => {
    await fetchDepartmentList({ ...searchParams.value, ordering, page: 1 })
  }
  
  // 切换页面
  const changePage = async (page: number) => {
    await fetchDepartmentList({ ...searchParams.value, page })
  }
  
  // 改变每页大小
  const changePageSize = async (pageSize: number) => {
    await fetchDepartmentList({ ...searchParams.value, page_size: pageSize, page: 1 })
  }
  
  // 选择部门
  const selectDepartment = (departmentId: number) => {
    selectedDepartmentId.value = departmentId
    if (!selectedDepartmentIds.value.includes(departmentId)) {
      selectedDepartmentIds.value.push(departmentId)
    }
  }
  
  // 取消选择部门
  const unselectDepartment = (departmentId: number) => {
    const index = selectedDepartmentIds.value.indexOf(departmentId)
    if (index > -1) {
      selectedDepartmentIds.value.splice(index, 1)
    }
    if (selectedDepartmentId.value === departmentId) {
      selectedDepartmentId.value = null
    }
  }
  
  // 切换部门选择状态
  const toggleDepartmentSelection = (departmentId: number) => {
    if (selectedDepartmentIds.value.includes(departmentId)) {
      unselectDepartment(departmentId)
    } else {
      selectDepartment(departmentId)
    }
  }
  
  // 清空选择
  const clearSelection = () => {
    selectedDepartmentIds.value = []
    selectedDepartmentId.value = null
  }
  
  // 展开/折叠树节点
  const toggleExpanded = (departmentId: number) => {
    const index = expandedKeys.value.indexOf(departmentId)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    } else {
      expandedKeys.value.push(departmentId)
    }
  }
  
  // 移动部门
  const moveDepartment = async (data: any) => {
    try {
      const response = await departmentApi.moveDepartment(data)
      return response
    } catch (error) {
      console.error('移动部门失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    departmentList.value = []
    departmentTree.value = []
    currentDepartment.value = null
    departmentStats.value = null
    loading.value = false
    treeLoading.value = false
    searchParams.value = {
      page: 1,
      page_size: 20,
      ordering: 'tree_id,lft'
    }
    pagination.value = {
      page: 1,
      page_size: 20,
      total: 0,
      total_pages: 0
    }
    selectedDepartmentIds.value = []
    selectedDepartmentId.value = null
    expandedKeys.value = []
    checkedKeys.value = []
  }
  
  return {
    // 状态
    departmentList,
    departmentTree,
    currentDepartment,
    departmentStats,
    loading,
    treeLoading,
    searchParams,
    pagination,
    selectedDepartmentIds,
    selectedDepartmentId,
    expandedKeys,
    checkedKeys,
    
    // 计算属性
    hasSelectedDepartments,
    selectedDepartmentsCount,
    
    // 方法
    fetchDepartmentList,
    fetchDepartmentTree,
    fetchDepartmentDetail,
    createDepartment,
    updateDepartment,
    deleteDepartment,
    toggleDepartmentActive,
    batchOperateDepartments,
    fetchDepartmentStats,
    searchDepartments,
    filterDepartments,
    sortDepartments,
    changePage,
    changePageSize,
    selectDepartment,
    unselectDepartment,
    toggleDepartmentSelection,
    clearSelection,
    toggleExpanded,
    moveDepartment,
    resetState
  }
})
