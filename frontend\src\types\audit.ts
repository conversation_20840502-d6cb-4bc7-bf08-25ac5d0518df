/**
 * 审计日志相关类型定义
 */

// 操作类型枚举
export enum OperationType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  QUERY = 'QUERY',
  ERROR = 'ERROR'
}

// 导出格式枚举
export enum ExportFormat {
  EXCEL = 'excel',
  PDF = 'pdf'
}

// 用户信息（简化）
export interface UserInfo {
  id: number
  username: string
  nickname: string
  email?: string
}

// 操作日志基础信息
export interface OperationLog {
  id: number
  user?: number | null
  user_nickname?: string
  user_username?: string
  operation_type: OperationType
  operation_type_display: string
  operation_desc: string
  method: string
  path: string
  ip_address: string
  user_agent: string
  status_code: number
  response_time: number
  created_at: string
  created_at_formatted: string
}

// 操作日志详情
export interface OperationLogDetail extends OperationLog {
  user_info: UserInfo | null
  user_agent_parsed: {
    browser?: string
    os?: string
    device?: string
  }
}

// 操作日志列表项（简化版）
export interface OperationLogListItem {
  id: number
  user_nickname?: string
  operation_type: OperationType
  operation_type_display: string
  operation_desc: string
  method: string
  ip_address: string
  status_code: number
  response_time: number
  created_at_formatted: string
}

// 操作日志搜索参数
export interface OperationLogSearchParams {
  search?: string
  start_date?: string
  end_date?: string
  user_id?: number
  operation_type?: OperationType
  method?: string
  ip_address?: string
  status_code?: number
  ordering?: string
  page?: number
  page_size?: number
}

// 操作日志导出参数
export interface OperationLogExportParams {
  start_date?: string
  end_date?: string
  user_id?: number
  operation_type?: OperationType
  export_format: ExportFormat
}

// 操作日志统计信息
export interface OperationLogStats {
  total_count: number
  today_count: number
  login_count: number
  operation_type_stats: Record<string, number>
  user_stats: Array<{
    user_id: number
    user_nickname: string
    count: number
  }>
  hourly_stats: Array<{
    hour: number
    count: number
  }>
}

// 用户操作统计
export interface UserOperationStats {
  total_operations: number
  today_operations: number
  operation_types: Record<string, number>
  recent_operations: OperationLogListItem[]
}

// 系统健康状态
export interface SystemHealthStats {
  total_logs: number
  error_rate: number
  avg_response_time: number
  active_users: number
  recent_errors: Array<{
    operation_desc: string
    count: number
    last_occurrence: string
  }>
}

// 日志清理参数
export interface LogCleanupParams {
  days: number
}

// 日志清理结果
export interface LogCleanupResult {
  deleted_count: number
}

// 操作类型选项
export interface OperationTypeOption {
  value: OperationType
  label: string
}

// HTTP方法选项
export interface HttpMethodOption {
  value: string
  label: string
}

// 状态码选项
export interface StatusCodeOption {
  value: number
  label: string
}

// 分页信息
export interface PaginationInfo {
  page: number
  page_size: number
  total: number
  total_pages: number
}

// 图表数据类型
export interface ChartDataItem {
  name: string
  value: number
}

export interface TimeSeriesDataItem {
  time: string
  value: number
}

export interface OperationTrendData {
  dates: string[]
  series: Array<{
    name: string
    data: number[]
  }>
}

// 实时日志数据
export interface RealTimeLogData {
  log: OperationLogListItem
  timestamp: number
}

// 实时统计数据
export interface RealTimeStatsData {
  online_users: number
  operations_per_minute: number
  error_rate: number
  avg_response_time: number
}

// 搜索历史
export interface SearchHistory {
  id: string
  query: string
  filters: OperationLogSearchParams
  timestamp: string
  name?: string
}

// 常用搜索
export interface SavedSearch {
  id: string
  name: string
  filters: OperationLogSearchParams
  created_at: string
}

// 日志清理策略
export interface LogCleanupPolicy {
  id: string
  name: string
  enabled: boolean
  retention_days: number
  max_size_mb?: number
  max_count?: number
  schedule_cron?: string
  created_at: string
  updated_at: string
}

// 导出任务状态
export enum ExportTaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// 导出任务
export interface ExportTask {
  id: string
  name: string
  status: ExportTaskStatus
  progress: number
  file_url?: string
  error_message?: string
  created_at: string
  completed_at?: string
}

// 日志过滤器
export interface LogFilter {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startswith' | 'endswith'
  value: any
}

// 高级搜索条件
export interface AdvancedSearchCondition {
  filters: LogFilter[]
  logic: 'and' | 'or'
}

// 日志告警规则
export interface LogAlertRule {
  id: string
  name: string
  enabled: boolean
  condition: AdvancedSearchCondition
  threshold: number
  time_window: number // 分钟
  notification_channels: string[]
  created_at: string
  updated_at: string
}

// 日志告警事件
export interface LogAlertEvent {
  id: string
  rule_id: string
  rule_name: string
  triggered_at: string
  count: number
  logs: OperationLogListItem[]
  acknowledged: boolean
  acknowledged_by?: string
  acknowledged_at?: string
}

// API响应类型
export interface OperationLogListResponse {
  results: OperationLogListItem[]
  count: number
  next?: string
  previous?: string
}

// 表单验证规则
export interface FormValidationRule {
  required?: boolean
  message?: string
  pattern?: RegExp
  min?: number
  max?: number
  validator?: (value: any) => boolean | string
}

// 审计日志权限
export interface AuditLogPermissions {
  canView: boolean
  canExport: boolean
  canCleanup: boolean
  canViewStats: boolean
  canManageAlerts: boolean
  canViewSystemHealth: boolean
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'log' | 'stats' | 'alert' | 'health'
  data: any
  timestamp: number
}

// 日志高亮配置
export interface LogHighlightConfig {
  keywords: string[]
  colors: string[]
  caseSensitive: boolean
  wholeWord: boolean
}

// 日志显示配置
export interface LogDisplayConfig {
  pageSize: number
  autoRefresh: boolean
  refreshInterval: number // 秒
  showColumns: string[]
  highlight: LogHighlightConfig
}

// 日志分析结果
export interface LogAnalysisResult {
  summary: {
    total_logs: number
    unique_users: number
    unique_ips: number
    error_rate: number
    avg_response_time: number
  }
  trends: {
    operation_trends: OperationTrendData
    user_activity: ChartDataItem[]
    error_distribution: ChartDataItem[]
    response_time_trends: TimeSeriesDataItem[]
  }
  insights: Array<{
    type: 'warning' | 'info' | 'error'
    title: string
    description: string
    recommendation?: string
  }>
}

// 日志导出模板
export interface LogExportTemplate {
  id: string
  name: string
  fields: string[]
  format: ExportFormat
  filters?: OperationLogSearchParams
  created_at: string
  updated_at: string
}

// 日志归档配置
export interface LogArchiveConfig {
  enabled: boolean
  retention_days: number
  archive_format: 'json' | 'csv'
  compression: boolean
  storage_path: string
}

// 日志监控配置
export interface LogMonitorConfig {
  realtime_enabled: boolean
  alert_enabled: boolean
  max_realtime_logs: number
  refresh_interval: number
  websocket_url: string
}
