["tests/test_audit_logging.py::TestAuditLogAPI::test_filter_logs_by_date_range", "tests/test_audit_logging.py::TestAuditLogAPI::test_filter_logs_by_ip_address", "tests/test_audit_logging.py::TestAuditLogAPI::test_filter_logs_by_operation_type", "tests/test_audit_logging.py::TestAuditLogAPI::test_filter_logs_by_user", "tests/test_audit_logging.py::TestAuditLogAPI::test_get_operation_log_detail", "tests/test_audit_logging.py::TestAuditLogAPI::test_get_operation_logs_list", "tests/test_audit_logging.py::TestAuditLogAPI::test_insufficient_permission_for_logs", "tests/test_audit_logging.py::TestAuditLogAPI::test_logs_ordering", "tests/test_audit_logging.py::TestAuditLogAPI::test_logs_pagination", "tests/test_audit_logging.py::TestAuditLogAPI::test_unauthorized_access_to_logs", "tests/test_audit_logging.py::TestAuditLogCleanup::test_cleanup_command", "tests/test_audit_logging.py::TestAuditLogCleanup::test_cleanup_with_backup", "tests/test_audit_logging.py::TestAuditLogIntegrationScenarios::test_compliance_audit_report", "tests/test_audit_logging.py::TestAuditLogIntegrationScenarios::test_security_incident_audit_trail", "tests/test_audit_logging.py::TestAuditLogIntegrationScenarios::test_user_lifecycle_audit_trail", "tests/test_audit_logging.py::TestAuditLogMiddleware::test_middleware_excludes_static_files", "tests/test_audit_logging.py::TestAuditLogMiddleware::test_middleware_handles_anonymous_user", "tests/test_audit_logging.py::TestAuditLogMiddleware::test_middleware_logs_request", "tests/test_audit_logging.py::TestAuditService::test_cleanup_old_logs", "tests/test_audit_logging.py::TestAuditService::test_export_logs_to_csv", "tests/test_audit_logging.py::TestAuditService::test_get_daily_operation_statistics", "tests/test_audit_logging.py::TestAuditService::test_get_operation_statistics", "tests/test_audit_logging.py::TestAuditService::test_get_user_operation_logs", "tests/test_audit_logging.py::TestAuditService::test_log_operation", "tests/test_authentication.py::TestAuthenticationAPI::test_account_lockout_after_failed_attempts", "tests/test_authentication.py::TestAuthenticationAPI::test_login_with_captcha_verification", "tests/test_authentication.py::TestAuthenticationAPI::test_login_with_inactive_user", "tests/test_authentication.py::TestAuthenticationAPI::test_login_with_invalid_credentials", "tests/test_authentication.py::TestAuthenticationAPI::test_successful_login", "tests/test_authentication.py::TestAuthenticationAPI::test_successful_logout", "tests/test_authentication.py::TestAuthenticationAPI::test_token_refresh", "tests/test_authentication.py::TestAuthenticationAPI::test_token_refresh_with_invalid_token", "tests/test_authentication.py::TestAuthenticationService::test_authenticate_user_failure", "tests/test_authentication.py::TestAuthenticationService::test_authenticate_user_success", "tests/test_authentication.py::TestAuthenticationService::test_check_account_lockout", "tests/test_authentication.py::TestAuthenticationService::test_create_user_session", "tests/test_authentication.py::TestAuthenticationService::test_logout_user", "tests/test_authentication.py::TestAuthenticationService::test_password_reset_token_generation", "tests/test_authentication.py::TestAuthenticationService::test_validate_password_strength", "tests/test_authentication.py::TestLoginAttemptModel::test_failed_attempt_creation", "tests/test_authentication.py::TestLoginAttemptModel::test_recent_failed_attempts_count", "tests/test_authentication.py::TestLoginAttemptModel::test_successful_attempt_creation", "tests/test_authentication.py::TestUserSessionModel::test_session_creation", "tests/test_authentication.py::TestUserSessionModel::test_session_deactivation", "tests/test_authentication.py::TestUserSessionModel::test_session_duration_calculation", "tests/test_basic.py::TestBasicFunctionality::test_database_connection", "tests/test_basic.py::TestBasicFunctionality::test_department_factory", "tests/test_basic.py::TestBasicFunctionality::test_user_authentication", "tests/test_basic.py::TestBasicFunctionality::test_user_creation", "tests/test_basic.py::TestBasicFunctionality::test_user_factory", "tests/test_basic.py::TestBasicFunctionality::test_user_profile_factory", "tests/test_basic.py::TestFactoryBoy::test_create_multiple_users", "tests/test_basic.py::TestFactoryBoy::test_department_hierarchy", "tests/test_basic.py::TestFactoryBoy::test_user_with_profile", "tests/test_basic.py::TestModelIntegration::test_department_user_relationship", "tests/test_basic.py::TestModelIntegration::test_user_profile_relationship", "tests/test_basic.py::TestPytestMarkers::test_integration_marker", "tests/test_basic.py::TestPytestMarkers::test_slow_marker", "tests/test_basic.py::TestPytestMarkers::test_unit_marker", "tests/test_data_scope_permissions.py::TestDataScopeIntegrationScenarios::test_cross_department_collaboration_scenario", "tests/test_data_scope_permissions.py::TestDataScopeIntegrationScenarios::test_hierarchical_data_access_scenario", "tests/test_data_scope_permissions.py::TestDataScopeIntegrationScenarios::test_temporary_permission_elevation_scenario", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_all_data_scope_access", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_cross_department_access_denied", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_data_scope_boundary_conditions", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_data_scope_permission_cache_invalidation", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_data_scope_permission_caching", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_dept_and_sub_scope_access", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_dept_only_scope_access", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_multiple_roles_data_scope", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_operation_log_data_scope_filtering", "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_self_only_scope_access", "tests/test_data_scope_permissions.py::TestDataScopeService::test_check_data_scope_permission", "tests/test_data_scope_permissions.py::TestDataScopeService::test_filter_queryset_by_data_scope", "tests/test_data_scope_permissions.py::TestDataScopeService::test_get_user_data_scope_departments", "tests/test_inheritance_debug.py::TestInheritanceDebug::test_inheritance_debug", "tests/test_permission_debug.py::TestPermissionDebug::test_basic_permission_creation", "tests/test_permission_debug.py::TestPermissionDebug::test_basic_role_creation", "tests/test_permission_debug.py::TestPermissionDebug::test_check_user_permission_debug", "tests/test_permission_debug.py::TestPermissionDebug::test_get_user_permissions_debug", "tests/test_permission_debug.py::TestPermissionDebug::test_role_permission_assignment", "tests/test_permission_debug.py::TestPermissionDebug::test_user_role_assignment", "tests/test_permission_inheritance.py::TestPermissionInheritanceComplete::test_circular_reference_protection", "tests/test_permission_inheritance.py::TestPermissionInheritanceComplete::test_complex_inheritance_scenario", "tests/test_permission_inheritance.py::TestPermissionInheritanceComplete::test_inheritance_performance", "tests/test_permission_inheritance.py::TestPermissionInheritanceComplete::test_inheritance_with_multiple_roles", "tests/test_permission_inheritance.py::TestPermissionInheritanceComplete::test_multi_level_inheritance", "tests/test_permission_inheritance.py::TestPermissionInheritanceComplete::test_permission_tree_methods", "tests/test_permission_inheritance.py::TestPermissionInheritanceComplete::test_single_level_inheritance", "tests/test_permission_inheritance.py::TestPermissionInheritanceIntegration::test_real_world_permission_structure", "tests/test_role_permission_management.py::TestPermissionDecorator::test_permission_decorator_failure", "tests/test_role_permission_management.py::TestPermissionDecorator::test_permission_decorator_success", "tests/test_role_permission_management.py::TestPermissionDecorator::test_superuser_bypass", "tests/test_role_permission_management.py::TestPermissionDecorator::test_unauthenticated_user", "tests/test_role_permission_management.py::TestPermissionManagementAPI::test_create_permission_hierarchy", "tests/test_role_permission_management.py::TestPermissionManagementAPI::test_create_permission_success", "tests/test_role_permission_management.py::TestPermissionManagementAPI::test_get_permission_tree", "tests/test_role_permission_management.py::TestPermissionService::test_check_user_permission_failure", "tests/test_role_permission_management.py::TestPermissionService::test_check_user_permission_success", "tests/test_role_permission_management.py::TestPermissionService::test_get_user_permissions", "tests/test_role_permission_management.py::TestPermissionService::test_get_user_roles", "tests/test_role_permission_management.py::TestPermissionService::test_permission_inheritance", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_create_role_duplicate_code", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_create_role_success", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_delete_role_soft_delete", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_get_role_detail", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_get_role_list", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_insufficient_permission", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_role_permission_assignment", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_role_permission_revocation", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_role_user_assignment", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_unauthorized_access", "tests/test_role_permission_management.py::TestRoleManagementAPI::test_update_role_success", "tests/test_simple.py::TestFactoryBasics::test_department_factory_sequence", "tests/test_simple.py::TestFactoryBasics::test_user_factory_sequence", "tests/test_simple.py::TestModelIntegrationBasic::test_department_creation_and_retrieval", "tests/test_simple.py::TestModelIntegrationBasic::test_user_creation_and_retrieval", "tests/test_simple.py::TestSimpleFunctionality::test_create_user_directly", "tests/test_simple.py::TestSimpleFunctionality::test_database_operations", "tests/test_simple.py::TestSimpleFunctionality::test_department_factory_basic", "tests/test_simple.py::TestSimpleFunctionality::test_department_hierarchy", "tests/test_simple.py::TestSimpleFunctionality::test_department_str_representation", "tests/test_simple.py::TestSimpleFunctionality::test_user_authentication", "tests/test_simple.py::TestSimpleFunctionality::test_user_factory_basic", "tests/test_simple.py::TestSimpleFunctionality::test_user_model_is_userprofile", "tests/test_simple.py::TestSimpleFunctionality::test_user_str_representation"]