"""
审计日志功能测试
测试操作日志自动记录、日志查询过滤分页、日志统计分析、日志清理归档机制
"""
import pytest
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from django.core.management import call_command
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock
from datetime import datetime, timedelta
from freezegun import freeze_time

from apps.audit.models import OperationLog
from apps.audit.services import AuditService, ExceptionLogService
from apps.audit.middleware import AuditLogMiddleware
from apps.users.models import UserProfile
from apps.departments.models import Department
from tests.factories import (
    UserProfileFactory, DepartmentFactory, OperationLogFactory
)


@pytest.mark.django_db
@pytest.mark.audit
class TestAuditLogAPI(APITestCase):
    """审计日志API测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建管理员用户
        self.admin_user = UserProfileFactory(is_staff=True, is_superuser=True)
        self.admin_user.set_password('admin123')
        self.admin_user.save()
        
        # 创建普通用户
        self.normal_user = UserProfileFactory()
        self.normal_user.set_password('user123')
        self.normal_user.save()
        
        # API端点
        self.logs_url = reverse('audit:operationlog-list')
        self.log_detail_url = lambda pk: reverse('audit:operationlog-detail', kwargs={'pk': pk})
        # 注意：stats端点可能需要在视图中添加
        # self.log_stats_url = reverse('audit:operationlog-stats')
        
        # 管理员认证
        refresh = RefreshToken.for_user(self.admin_user)
        self.admin_token = str(refresh.access_token)
    
    def test_get_operation_logs_list(self):
        """测试获取操作日志列表"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建测试日志
        logs = OperationLogFactory.create_batch(10, user=self.admin_user)
        
        response = self.client.get(self.logs_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['results']), 10)
    
    def test_get_operation_log_detail(self):
        """测试获取操作日志详情"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        log = OperationLogFactory(user=self.admin_user)
        url = self.log_detail_url(log.id)
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], log.id)
        self.assertEqual(response.data['operation_desc'], log.operation_desc)
    
    def test_filter_logs_by_operation_type(self):
        """测试按操作类型过滤日志"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建不同类型的日志
        login_logs = OperationLogFactory.create_batch(3, operation_type='LOGIN')
        create_logs = OperationLogFactory.create_batch(2, operation_type='CREATE')
        
        # 过滤登录日志
        response = self.client.get(self.logs_url, {'operation_type': 'LOGIN'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)
        
        for log in response.data['results']:
            self.assertEqual(log['operation_type'], 'LOGIN')
    
    def test_filter_logs_by_user(self):
        """测试按用户过滤日志"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建不同用户的日志
        admin_logs = OperationLogFactory.create_batch(3, user=self.admin_user)
        user_logs = OperationLogFactory.create_batch(2, user=self.normal_user)
        
        # 过滤管理员日志
        response = self.client.get(self.logs_url, {'user': self.admin_user.id})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)
        
        for log in response.data['results']:
            self.assertEqual(log['user']['id'], self.admin_user.id)
    
    def test_filter_logs_by_date_range(self):
        """测试按日期范围过滤日志"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建不同时间的日志
        with freeze_time("2024-01-01"):
            old_logs = OperationLogFactory.create_batch(2)
        
        with freeze_time("2024-01-15"):
            recent_logs = OperationLogFactory.create_batch(3)
        
        # 过滤最近的日志
        response = self.client.get(self.logs_url, {
            'start_date': '2024-01-10',
            'end_date': '2024-01-20'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)
    
    def test_filter_logs_by_ip_address(self):
        """测试按IP地址过滤日志"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建不同IP的日志
        local_logs = OperationLogFactory.create_batch(2, ip_address='127.0.0.1')
        remote_logs = OperationLogFactory.create_batch(3, ip_address='*************')
        
        # 过滤本地IP日志
        response = self.client.get(self.logs_url, {'ip_address': '127.0.0.1'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        
        for log in response.data['results']:
            self.assertEqual(log['ip_address'], '127.0.0.1')
    
    def test_logs_pagination(self):
        """测试日志分页功能"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建大量日志
        OperationLogFactory.create_batch(50)
        
        # 测试第一页
        response = self.client.get(self.logs_url, {'page': 1, 'page_size': 10})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 10)
        self.assertIsNotNone(response.data['next'])
        
        # 测试第二页
        response = self.client.get(self.logs_url, {'page': 2, 'page_size': 10})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 10)
    
    def test_logs_ordering(self):
        """测试日志排序功能"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建不同时间的日志
        with freeze_time("2024-01-01"):
            old_log = OperationLogFactory(operation_desc='旧日志')
        
        with freeze_time("2024-01-02"):
            new_log = OperationLogFactory(operation_desc='新日志')
        
        # 按时间倒序排列
        response = self.client.get(self.logs_url, {'ordering': '-created_at'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data['results']
        self.assertEqual(results[0]['operation_desc'], '新日志')
        self.assertEqual(results[1]['operation_desc'], '旧日志')
    
    # TODO: 统计功能测试 - 需要在视图中添加stats端点
    # def test_operation_log_statistics(self):
    #     """测试操作日志统计"""
    #     pass
    
    def test_unauthorized_access_to_logs(self):
        """测试未授权访问日志"""
        # 不设置认证头
        response = self.client.get(self.logs_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_insufficient_permission_for_logs(self):
        """测试权限不足访问日志"""
        # 使用普通用户
        refresh = RefreshToken.for_user(self.normal_user)
        normal_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {normal_token}')
        
        response = self.client.get(self.logs_url)
        
        # 根据权限设计，可能返回403或只显示自己的日志
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_200_OK])


@pytest.mark.django_db
@pytest.mark.unit
class TestAuditLogMiddleware(TestCase):
    """审计日志中间件测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user = UserProfileFactory()
        self.middleware = AuditLogMiddleware(lambda request: Mock(status_code=200))
    
    def test_middleware_logs_request(self):
        """测试中间件记录请求"""
        # 模拟请求
        request = Mock()
        request.user = self.user
        request.method = 'POST'
        request.path = '/api/users/'
        request.META = {
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_USER_AGENT': 'Test Agent'
        }
        
        # 处理请求
        response = self.middleware(request)
        
        # 验证日志记录
        log = OperationLog.objects.filter(user=self.user).first()
        self.assertIsNotNone(log)
        self.assertEqual(log.method, 'POST')
        self.assertEqual(log.path, '/api/users/')
        self.assertEqual(log.ip_address, '127.0.0.1')
    
    def test_middleware_handles_anonymous_user(self):
        """测试中间件处理匿名用户"""
        # 模拟匿名请求
        request = Mock()
        request.user.is_authenticated = False
        request.method = 'GET'
        request.path = '/api/public/'
        request.META = {
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_USER_AGENT': 'Test Agent'
        }
        
        # 处理请求
        response = self.middleware(request)
        
        # 验证日志记录（匿名用户的日志）
        log = OperationLog.objects.filter(user=None).first()
        self.assertIsNotNone(log)
        self.assertEqual(log.method, 'GET')
        self.assertEqual(log.path, '/api/public/')
    
    def test_middleware_excludes_static_files(self):
        """测试中间件排除静态文件"""
        # 模拟静态文件请求
        request = Mock()
        request.user = self.user
        request.method = 'GET'
        request.path = '/static/css/style.css'
        request.META = {
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_USER_AGENT': 'Test Agent'
        }
        
        # 处理请求
        response = self.middleware(request)
        
        # 验证不记录静态文件日志
        log_count = OperationLog.objects.filter(path='/static/css/style.css').count()
        self.assertEqual(log_count, 0)


@pytest.mark.django_db
@pytest.mark.unit
class TestAuditService(TestCase):
    """审计服务单元测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user = UserProfileFactory()
        self.audit_service = AuditService()
    
    def test_log_operation(self):
        """测试记录操作"""
        self.audit_service.log_operation(
            user=self.user,
            operation_type='CREATE',
            operation_desc='创建用户',
            method='POST',
            path='/api/users/',
            ip_address='127.0.0.1',
            user_agent='Test Agent',
            status_code=201,
            response_time=150
        )
        
        # 验证日志记录
        log = OperationLog.objects.filter(user=self.user).first()
        self.assertIsNotNone(log)
        self.assertEqual(log.operation_type, 'CREATE')
        self.assertEqual(log.operation_desc, '创建用户')
        self.assertEqual(log.status_code, 201)
        self.assertEqual(log.response_time, 150)
    
    def test_get_user_operation_logs(self):
        """测试获取用户操作日志"""
        # 创建用户日志
        logs = OperationLogFactory.create_batch(5, user=self.user)
        other_logs = OperationLogFactory.create_batch(3, user=UserProfileFactory())
        
        user_logs = self.audit_service.get_user_operation_logs(self.user)
        
        self.assertEqual(len(user_logs), 5)
        for log in user_logs:
            self.assertEqual(log.user, self.user)
    
    def test_get_operation_statistics(self):
        """测试获取操作统计"""
        # 创建不同类型的日志
        OperationLogFactory.create_batch(3, operation_type='LOGIN')
        OperationLogFactory.create_batch(2, operation_type='CREATE')
        OperationLogFactory.create_batch(1, operation_type='UPDATE')
        
        stats = self.audit_service.get_operation_statistics()
        
        self.assertIn('total_logs', stats)
        self.assertIn('operation_type_stats', stats)
        self.assertEqual(stats['total_logs'], 6)
        
        # 验证操作类型统计
        type_stats = {item['operation_type']: item['count'] for item in stats['operation_type_stats']}
        self.assertEqual(type_stats['LOGIN'], 3)
        self.assertEqual(type_stats['CREATE'], 2)
        self.assertEqual(type_stats['UPDATE'], 1)
    
    def test_get_daily_operation_statistics(self):
        """测试获取每日操作统计"""
        # 创建不同日期的日志
        with freeze_time("2024-01-01"):
            OperationLogFactory.create_batch(3)
        
        with freeze_time("2024-01-02"):
            OperationLogFactory.create_batch(5)
        
        stats = self.audit_service.get_daily_operation_statistics(days=7)
        
        self.assertIsInstance(stats, list)
        self.assertGreaterEqual(len(stats), 2)
        
        # 验证统计数据格式
        for day_stat in stats:
            self.assertIn('date', day_stat)
            self.assertIn('count', day_stat)
    
    def test_cleanup_old_logs(self):
        """测试清理旧日志"""
        # 创建旧日志
        with freeze_time("2023-01-01"):
            old_logs = OperationLogFactory.create_batch(5)
        
        # 创建新日志
        with freeze_time("2024-01-01"):
            new_logs = OperationLogFactory.create_batch(3)
        
        # 清理90天前的日志
        deleted_count = self.audit_service.cleanup_old_logs(days=90)
        
        self.assertEqual(deleted_count, 5)
        
        # 验证旧日志被删除
        remaining_logs = OperationLog.objects.all()
        self.assertEqual(len(remaining_logs), 3)
    
    def test_export_logs_to_csv(self):
        """测试导出日志到CSV"""
        # 创建测试日志
        logs = OperationLogFactory.create_batch(3, user=self.user)
        
        csv_content = self.audit_service.export_logs_to_csv(
            start_date=timezone.now() - timedelta(days=1),
            end_date=timezone.now() + timedelta(days=1)
        )
        
        self.assertIsNotNone(csv_content)
        self.assertIn('operation_type', csv_content)
        self.assertIn('operation_desc', csv_content)
        
        # 验证包含日志数据
        lines = csv_content.split('\n')
        self.assertGreaterEqual(len(lines), 4)  # 头部 + 3条日志


@pytest.mark.django_db
@pytest.mark.integration
class TestAuditLogCleanup(TestCase):
    """审计日志清理测试"""
    
    def test_cleanup_command(self):
        """测试清理命令"""
        # 创建旧日志
        with freeze_time("2023-01-01"):
            old_logs = OperationLogFactory.create_batch(10)
        
        # 创建新日志
        new_logs = OperationLogFactory.create_batch(5)
        
        # 执行清理命令
        call_command('cleanup_audit_logs', days=90, verbosity=0)
        
        # 验证清理结果
        remaining_logs = OperationLog.objects.all()
        self.assertEqual(len(remaining_logs), 5)
    
    def test_cleanup_with_backup(self):
        """测试带备份的清理"""
        # 创建旧日志
        with freeze_time("2023-01-01"):
            old_logs = OperationLogFactory.create_batch(5)
        
        # 执行带备份的清理
        audit_service = AuditService()
        deleted_count = audit_service.cleanup_old_logs(days=90, backup=True)
        
        self.assertEqual(deleted_count, 5)
        
        # 验证备份文件创建（这里简化测试）
        # 实际实现中应该验证备份文件的存在和内容


@pytest.mark.django_db
@pytest.mark.integration
class TestAuditLogIntegrationScenarios(TestCase):
    """审计日志集成场景测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user = UserProfileFactory()
        self.department = DepartmentFactory()
    
    def test_user_lifecycle_audit_trail(self):
        """测试用户生命周期审计轨迹"""
        audit_service = AuditService()
        
        # 用户创建
        audit_service.log_operation(
            user=self.user,
            operation_type='CREATE',
            operation_desc=f'创建用户: {self.user.username}',
            method='POST',
            path='/api/users/',
            ip_address='127.0.0.1',
            status_code=201
        )
        
        # 用户更新
        audit_service.log_operation(
            user=self.user,
            operation_type='UPDATE',
            operation_desc=f'更新用户信息: {self.user.username}',
            method='PUT',
            path=f'/api/users/{self.user.id}/',
            ip_address='127.0.0.1',
            status_code=200
        )
        
        # 用户删除
        audit_service.log_operation(
            user=self.user,
            operation_type='DELETE',
            operation_desc=f'删除用户: {self.user.username}',
            method='DELETE',
            path=f'/api/users/{self.user.id}/',
            ip_address='127.0.0.1',
            status_code=204
        )
        
        # 验证完整的审计轨迹
        user_logs = OperationLog.objects.filter(user=self.user).order_by('created_at')
        self.assertEqual(len(user_logs), 3)
        
        operations = [log.operation_type for log in user_logs]
        self.assertEqual(operations, ['CREATE', 'UPDATE', 'DELETE'])
    
    def test_security_incident_audit_trail(self):
        """测试安全事件审计轨迹"""
        audit_service = AuditService()
        
        # 多次登录失败
        for i in range(5):
            audit_service.log_operation(
                user=None,
                operation_type='ERROR',
                operation_desc=f'登录失败: {self.user.username}',
                method='POST',
                path='/api/auth/login/',
                ip_address='*************',
                status_code=401
            )
        
        # 账户锁定
        audit_service.log_operation(
            user=None,
            operation_type='ERROR',
            operation_desc=f'账户锁定: {self.user.username}',
            method='POST',
            path='/api/auth/login/',
            ip_address='*************',
            status_code=423
        )
        
        # 验证安全事件记录
        security_logs = OperationLog.objects.filter(
            operation_type='ERROR',
            ip_address='*************'
        )
        self.assertEqual(len(security_logs), 6)
    
    def test_compliance_audit_report(self):
        """测试合规审计报告"""
        audit_service = AuditService()
        
        # 创建各种操作日志
        operations = [
            ('LOGIN', '用户登录'),
            ('CREATE', '创建数据'),
            ('UPDATE', '修改数据'),
            ('DELETE', '删除数据'),
            ('QUERY', '查询数据'),
            ('LOGOUT', '用户登出')
        ]
        
        for op_type, op_desc in operations:
            audit_service.log_operation(
                user=self.user,
                operation_type=op_type,
                operation_desc=op_desc,
                method='POST',
                path='/api/test/',
                ip_address='127.0.0.1',
                status_code=200
            )
        
        # 生成合规报告
        report = audit_service.generate_compliance_report(
            start_date=timezone.now() - timedelta(days=1),
            end_date=timezone.now() + timedelta(days=1)
        )
        
        self.assertIn('total_operations', report)
        self.assertIn('user_activity', report)
        self.assertIn('operation_summary', report)
        self.assertEqual(report['total_operations'], 6)
