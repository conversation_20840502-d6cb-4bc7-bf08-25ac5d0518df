<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">8%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_9e189ee61a034fc9___init___py.html">apps\__init__.py</a></td>
                <td class="name left"><a href="z_9e189ee61a034fc9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250___init___py.html">apps\audit\__init__.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_apps_py.html">apps\audit\apps.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f81546b0b2653676___init___py.html">apps\audit\management\__init__.py</a></td>
                <td class="name left"><a href="z_f81546b0b2653676___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6___init___py.html">apps\audit\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t19">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t19"><data value='add_arguments'>Command.add_arguments</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t72">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t72"><data value='handle'>Command.handle</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t97">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t97"><data value='handle_comprehensive_cleanup'>Command._handle_comprehensive_cleanup</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t114">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t114"><data value='handle_archive'>Command._handle_archive</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t147">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t147"><data value='handle_cleanup'>Command._handle_cleanup</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t174">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t174"><data value='cleanup_by_time'>Command._cleanup_by_time</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t189">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t189"><data value='cleanup_by_count'>Command._cleanup_by_count</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0001_initial_py.html">apps\audit\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0001_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0002_initial_py.html">apps\audit\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0002_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html">apps\audit\migrations\0003_add_error_operation_type.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd___init___py.html">apps\audit\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t47">apps\audit\models.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t47"><data value='cleanup_old_logs'>OperationLog.cleanup_old_logs</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t53">apps\audit\models.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t53"><data value='str__'>OperationLog.__str__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html">apps\audit\models.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t29">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t29"><data value='get_created_at_formatted'>OperationLogSerializer.get_created_at_formatted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t49">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t49"><data value='get_created_at_formatted'>OperationLogListSerializer.get_created_at_formatted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t71">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t71"><data value='get_user_info'>OperationLogDetailSerializer.get_user_info</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t87">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t87"><data value='get_created_at_formatted'>OperationLogDetailSerializer.get_created_at_formatted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t91">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t91"><data value='get_user_agent_parsed'>OperationLogDetailSerializer.get_user_agent_parsed</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t158">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t158"><data value='validate'>OperationLogExportSerializer.validate</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t20">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t20"><data value='log_exception'>ExceptionLogService.log_exception</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t95">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t95"><data value='get_client_ip'>ExceptionLogService._get_client_ip</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t105">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t105"><data value='safe_serialize_request_data'>ExceptionLogService._safe_serialize_request_data</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t142">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t142"><data value='update_exception_stats'>ExceptionLogService._update_exception_stats</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t167">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t167"><data value='create_operation_log'>AuditLogService.create_operation_log</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t209">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t209"><data value='get_user_operation_stats'>AuditLogService.get_user_operation_stats</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t268">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t268"><data value='get_system_health_stats'>AuditLogService.get_system_health_stats</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t16">apps\audit\tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t16"><data value='cleanup_old_operation_logs'>cleanup_old_operation_logs</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t46">apps\audit\tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t46"><data value='cleanup_large_operation_logs'>cleanup_large_operation_logs</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t96">apps\audit\tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t96"><data value='generate_audit_statistics'>generate_audit_statistics</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t164">apps\audit\tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t164"><data value='comprehensive_audit_cleanup'>comprehensive_audit_cleanup</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t198">apps\audit\tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html#t198"><data value='archive_old_operation_logs'>archive_old_operation_logs</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html">apps\audit\tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t28">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t28"><data value='setUp'>AuditTasksTest.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t38">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t38"><data value='test_cleanup_old_operation_logs'>AuditTasksTest.test_cleanup_old_operation_logs</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t80">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t80"><data value='test_cleanup_large_operation_logs'>AuditTasksTest.test_cleanup_large_operation_logs</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t109">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t109"><data value='test_cleanup_large_operation_logs_no_cleanup_needed'>AuditTasksTest.test_cleanup_large_operation_logs_no_cleanup_needed</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t132">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t132"><data value='test_generate_audit_statistics'>AuditTasksTest.test_generate_audit_statistics</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t202">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t202"><data value='test_archive_old_operation_logs_json'>AuditTasksTest.test_archive_old_operation_logs_json</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t262">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t262"><data value='test_archive_old_operation_logs_csv'>AuditTasksTest.test_archive_old_operation_logs_csv</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t303">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t303"><data value='test_archive_old_operation_logs_no_data'>AuditTasksTest.test_archive_old_operation_logs_no_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t326">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t326"><data value='test_comprehensive_audit_cleanup'>AuditTasksTest.test_comprehensive_audit_cleanup</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t26">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t26"><data value='setUp'>OperationLogModelTest.setUp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t33">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t33"><data value='test_create_operation_log'>OperationLogModelTest.test_create_operation_log</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t53">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t53"><data value='test_operation_log_str'>OperationLogModelTest.test_operation_log_str</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t69">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t69"><data value='test_cleanup_old_logs'>OperationLogModelTest.test_cleanup_old_logs</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t109">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t109"><data value='setUp'>ExceptionLogServiceTest.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t117">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t117"><data value='test_log_exception_with_request'>ExceptionLogServiceTest.test_log_exception_with_request</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t137">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t137"><data value='test_log_exception_without_request'>ExceptionLogServiceTest.test_log_exception_without_request</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t148">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t148"><data value='test_safe_serialize_request_data'>ExceptionLogServiceTest.test_safe_serialize_request_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t173">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t173"><data value='setUp'>AuditLogServiceTest.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t181">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t181"><data value='test_create_operation_log'>AuditLogServiceTest.test_create_operation_log</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t203">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t203"><data value='test_get_user_operation_stats'>AuditLogServiceTest.test_get_user_operation_stats</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t238">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t238"><data value='test_get_system_health_stats'>AuditLogServiceTest.test_get_system_health_stats</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t283">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t283"><data value='setUp'>AuditLogMiddlewareTest.setUp</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t292">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t292"><data value='test_process_request'>AuditLogMiddlewareTest.test_process_request</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t301">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t301"><data value='test_should_log_operation'>AuditLogMiddlewareTest.test_should_log_operation</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t315">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t315"><data value='test_get_operation_desc'>AuditLogMiddlewareTest.test_get_operation_desc</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t332">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t332"><data value='setUp'>OperationLogViewSetTest.setUp</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t374">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t374"><data value='tearDown'>OperationLogViewSetTest.tearDown</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t379">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t379"><data value='test_list_operation_logs'>OperationLogViewSetTest.test_list_operation_logs</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t397">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t397"><data value='test_filter_operation_logs'>OperationLogViewSetTest.test_filter_operation_logs</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t411">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t411"><data value='test_get_operation_log_detail'>OperationLogViewSetTest.test_get_operation_log_detail</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t423">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t423"><data value='test_get_stats'>OperationLogViewSetTest.test_get_stats</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t433">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t433"><data value='test_export_logs'>OperationLogViewSetTest.test_export_logs</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t449">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t449"><data value='setUp'>AuditLogMiddlewareIntegrationTest.setUp</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t458">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t458"><data value='test_middleware_with_authenticated_user'>AuditLogMiddlewareIntegrationTest.test_middleware_with_authenticated_user</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t481">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t481"><data value='test_middleware_skip_static_paths'>AuditLogMiddlewareIntegrationTest.test_middleware_skip_static_paths</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_urls_py.html">apps\audit\urls.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t38">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t38"><data value='get_serializer_class'>OperationLogViewSet.get_serializer_class</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t50">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t50"><data value='get_queryset'>OperationLogViewSet.get_queryset</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t104">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t104"><data value='list'>OperationLogViewSet.list</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t120">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t120"><data value='retrieve'>OperationLogViewSet.retrieve</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t130">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t130"><data value='stats'>OperationLogViewSet.stats</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t190">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t190"><data value='export'>OperationLogViewSet.export</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t225">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t225"><data value='export_excel'>OperationLogViewSet._export_excel</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t271">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t271"><data value='export_pdf'>OperationLogViewSet._export_pdf</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t329">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t329"><data value='cleanup'>OperationLogViewSet.cleanup</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t350">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t350"><data value='health'>OperationLogViewSet.health</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t362">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t362"><data value='user_stats'>OperationLogViewSet.user_stats</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa___init___py.html">apps\authentication\__init__.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t24">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t24"><data value='init__'>LoginAnomalyDetector.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t39">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t39"><data value='detect_login_anomaly'>LoginAnomalyDetector.detect_login_anomaly</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t124">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t124"><data value='detect_failed_attempts'>LoginAnomalyDetector._detect_failed_attempts</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t161">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t161"><data value='detect_new_device'>LoginAnomalyDetector._detect_new_device</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t196">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t196"><data value='detect_new_ip'>LoginAnomalyDetector._detect_new_ip</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t233">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t233"><data value='detect_unusual_time'>LoginAnomalyDetector._detect_unusual_time</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t277">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t277"><data value='detect_location_anomaly'>LoginAnomalyDetector._detect_location_anomaly</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t330">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t330"><data value='detect_concurrent_sessions'>LoginAnomalyDetector._detect_concurrent_sessions</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t352">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t352"><data value='detect_login_frequency'>LoginAnomalyDetector._detect_login_frequency</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t378">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t378"><data value='generate_device_fingerprint'>LoginAnomalyDetector._generate_device_fingerprint</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t382">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t382"><data value='calculate_distance'>LoginAnomalyDetector._calculate_distance</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t401">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t401"><data value='calculate_risk_score'>LoginAnomalyDetector._calculate_risk_score</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t420">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t420"><data value='generate_recommendations'>LoginAnomalyDetector._generate_recommendations</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t448">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t448"><data value='log_detection_result'>LoginAnomalyDetector._log_detection_result</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t463">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t463"><data value='trigger_security_alert'>LoginAnomalyDetector._trigger_security_alert</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_apps_py.html">apps\authentication\apps.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91a8e3ad6c12af09___init___py.html">apps\authentication\management\__init__.py</a></td>
                <td class="name left"><a href="z_91a8e3ad6c12af09___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458___init___py.html">apps\authentication\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_0d354c5377fd6458___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html#t13">apps\authentication\management\commands\cleanup_auth.py</a></td>
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html#t13"><data value='add_arguments'>Command.add_arguments</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html#t30">apps\authentication\management\commands\cleanup_auth.py</a></td>
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html#t30"><data value='handle'>Command.handle</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html">apps\authentication\management\commands\cleanup_auth.py</a></td>
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0001_initial_py.html">apps\authentication\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0001_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0002_initial_py.html">apps\authentication\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0002_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html">apps\authentication\migrations\0003_simplecaptcha.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html">apps\authentication\migrations\0004_loginattempt_ipwhitelist.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html">apps\authentication\migrations\0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9___init___py.html">apps\authentication\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t47">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t47"><data value='str__'>UserSession.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t67">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t67"><data value='str__'>SimpleCaptcha.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t71">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t71"><data value='generate_captcha'>SimpleCaptcha.generate_captcha</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t96">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t96"><data value='verify_captcha'>SimpleCaptcha.verify_captcha</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t112">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t112"><data value='cleanup_expired'>SimpleCaptcha.cleanup_expired</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t135">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t135"><data value='str__'>IPWhitelist.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t138">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t138"><data value='is_valid'>IPWhitelist.is_valid</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t172">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t172"><data value='str__'>LoginAttempt.__str__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t245">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t245"><data value='str__'>SecurityAlert.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t293">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t293"><data value='str__'>DeviceFingerprint.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t297">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t297"><data value='generate_fingerprint'>DeviceFingerprint.generate_fingerprint</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t305">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t305"><data value='update_trust_score'>DeviceFingerprint.update_trust_score</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>133</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="133 133">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t20">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t20"><data value='check_ip_whitelist'>SecurityService.check_ip_whitelist</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t33">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t33"><data value='add_ip_to_whitelist'>SecurityService.add_ip_to_whitelist</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t55">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t55"><data value='generate_device_fingerprint'>SecurityService.generate_device_fingerprint</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t66">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t66"><data value='generate_browser_fingerprint'>SecurityService.generate_browser_fingerprint</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t78">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t78"><data value='record_login_attempt'>SecurityService.record_login_attempt</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t97">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t97"><data value='check_suspicious_activity'>SecurityService.check_suspicious_activity</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t127">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t127"><data value='check_device_change'>SecurityService.check_device_change</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t149">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t149"><data value='check_location_change'>SecurityService.check_location_change</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t163">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t163"><data value='apply_security_policies'>SecurityService.apply_security_policies</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t219">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t219"><data value='get_client_ip'>SecurityService.get_client_ip</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t229">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t229"><data value='cleanup_old_login_attempts'>SecurityService.cleanup_old_login_attempts</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t25">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t25"><data value='validate'>LoginSerializer.validate</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t84">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t84"><data value='validate_captcha'>LoginSerializer._validate_captcha</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t20">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t20"><data value='generate_captcha'>CaptchaService.generate_captcha</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t29">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t29"><data value='cleanup_expired_captcha'>CaptchaService.cleanup_expired_captcha</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t38">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t38"><data value='create_session'>SessionService.create_session</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t73">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t73"><data value='get_client_ip'>SessionService.get_client_ip</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t83">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t83"><data value='get_device_type'>SessionService.get_device_type</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t93">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t93"><data value='get_browser'>SessionService.get_browser</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t107">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t107"><data value='get_os'>SessionService.get_os</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t123">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t123"><data value='cleanup_expired_sessions'>SessionService.cleanup_expired_sessions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t130">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t130"><data value='invalidate_user_sessions'>SessionService.invalidate_user_sessions</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t142">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t142"><data value='login'>AuthenticationService.login</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t168">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t168"><data value='logout'>AuthenticationService.logout</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t190">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t190"><data value='refresh_token'>AuthenticationService.refresh_token</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t25">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t25"><data value='get_user_sessions'>get_user_sessions</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t97">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t97"><data value='terminate_session'>terminate_session</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t132">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t132"><data value='terminate_user_sessions'>terminate_user_sessions</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t172">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t172"><data value='get_session_statistics'>get_session_statistics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t233">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t233"><data value='cleanup_expired_sessions'>cleanup_expired_sessions</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t263">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t263"><data value='get_security_alerts'>get_security_alerts</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t340">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html#t340"><data value='update_alert_status'>update_alert_status</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t13">apps\authentication\tasks.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t13"><data value='cleanup_expired_captcha'>cleanup_expired_captcha</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t30">apps\authentication\tasks.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t30"><data value='cleanup_expired_sessions'>cleanup_expired_sessions</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t47">apps\authentication\tasks.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t47"><data value='cleanup_auth_data'>cleanup_auth_data</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t71">apps\authentication\tasks.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t71"><data value='send_security_alert'>send_security_alert</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t98">apps\authentication\tasks.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html#t98"><data value='check_suspicious_login_attempts'>check_suspicious_login_attempts</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html">apps\authentication\tasks.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t18">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t18"><data value='test_generate_captcha'>CaptchaServiceTestCase.test_generate_captcha</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t26">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t26"><data value='test_cleanup_expired_captcha'>CaptchaServiceTestCase.test_cleanup_expired_captcha</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t42">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t42"><data value='setUp'>AuthenticationAPITestCase.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t51">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t51"><data value='test_captcha_generation'>AuthenticationAPITestCase.test_captcha_generation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t61">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t61"><data value='test_login_without_captcha'>AuthenticationAPITestCase.test_login_without_captcha</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t75">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t75"><data value='test_login_with_invalid_credentials'>AuthenticationAPITestCase.test_login_with_invalid_credentials</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t93">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t93"><data value='test_successful_login'>AuthenticationAPITestCase.test_successful_login</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t114">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t114"><data value='test_logout'>AuthenticationAPITestCase.test_logout</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t143">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t143"><data value='setUp'>SessionServiceTestCase.setUp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t150">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t150"><data value='test_create_session'>SessionServiceTestCase.test_create_session</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t166">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t166"><data value='test_cleanup_expired_sessions'>SessionServiceTestCase.test_cleanup_expired_sessions</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t175">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t175"><data value='setUp'>AccountLockTestCase.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t183">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t183"><data value='test_account_lock_after_failed_attempts'>AccountLockTestCase.test_account_lock_after_failed_attempts</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_urls_py.html">apps\authentication\urls.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t24">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t24"><data value='captcha_view'>captcha_view</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t42">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t42"><data value='login_view'>login_view</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t140">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t140"><data value='refresh_token_view'>refresh_token_view</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t173">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t173"><data value='logout_view'>logout_view</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t209">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t209"><data value='get_queryset'>UserSessionViewSet.get_queryset</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t215">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t215"><data value='list'>UserSessionViewSet.list</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t224">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t224"><data value='destroy'>UserSessionViewSet.destroy</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t243">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t243"><data value='logout_all_view'>logout_all_view</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t278">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t278"><data value='auth_status_view'>auth_status_view</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t331">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t331"><data value='force_logout_session_view'>force_logout_session_view</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t363">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t363"><data value='security_info_view'>security_info_view</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t443">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t443"><data value='add_ip_whitelist_view'>add_ip_whitelist_view</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0___init___py.html">apps\backup\__init__.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_apps_py.html">apps\backup\apps.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac143cddc957a9a8_0001_initial_py.html">apps\backup\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_ac143cddc957a9a8_0001_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac143cddc957a9a8___init___py.html">apps\backup\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_ac143cddc957a9a8___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t89">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t89"><data value='str__'>BackupJob.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t92">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t92"><data value='calculate_file_hash'>BackupJob.calculate_file_hash</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t104">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t104"><data value='verify_integrity'>BackupJob.verify_integrity</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t113">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t113"><data value='duration'>BackupJob.duration</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t189">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t189"><data value='str__'>RestoreJob.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t243">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t243"><data value='str__'>BackupSchedule.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t287">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t287"><data value='str__'>BackupStorage.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t291">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t291"><data value='usage_percentage'>BackupStorage.usage_percentage</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>117</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="117 117">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t27">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t27"><data value='init__'>BackupService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t31">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t31"><data value='ensure_backup_directory'>BackupService.ensure_backup_directory</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t37">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t37"><data value='create_backup'>BackupService.create_backup</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t91">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t91"><data value='create_full_backup'>BackupService._create_full_backup</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t133">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t133"><data value='create_incremental_backup'>BackupService._create_incremental_backup</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t154">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t154"><data value='create_differential_backup'>BackupService._create_differential_backup</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t174">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t174"><data value='backup_media_files'>BackupService._backup_media_files</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t185">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t185"><data value='compress_backup'>BackupService._compress_backup</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t215">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t215"><data value='verify_backup'>BackupService.verify_backup</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t252">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t252"><data value='cleanup_old_backups'>BackupService.cleanup_old_backups</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t289">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t289"><data value='init__'>RestoreService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t292">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t292"><data value='restore_backup'>RestoreService.restore_backup</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t352">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t352"><data value='verify_backup_before_restore'>RestoreService._verify_backup_before_restore</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t356">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t356"><data value='restore_full_backup'>RestoreService._restore_full_backup</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t398">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t398"><data value='restore_partial_backup'>RestoreService._restore_partial_backup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t403">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t403"><data value='restore_schema_only'>RestoreService._restore_schema_only</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t409">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t409"><data value='restore_data_only'>RestoreService._restore_data_only</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t414">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t414"><data value='verify_after_restore'>RestoreService._verify_after_restore</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5___init___py.html">apps\common\__init__.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_apps_py.html">apps\common\apps.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t193">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t193"><data value='init__'>ConfigManager.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t197">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t197"><data value='get_config'>ConfigManager.get_config</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t226">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t226"><data value='set_config'>ConfigManager.set_config</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t272">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t272"><data value='get_all_configs'>ConfigManager.get_all_configs</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t295">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t295"><data value='get_categories'>ConfigManager.get_categories</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t303">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t303"><data value='reset_config'>ConfigManager.reset_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t320">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t320"><data value='validate_all_configs'>ConfigManager.validate_all_configs</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t339">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t339"><data value='get_env_value'>ConfigManager._get_env_value</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t357">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t357"><data value='validate_config_value'>ConfigManager._validate_config_value</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t392">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t392"><data value='save_to_env_file'>ConfigManager._save_to_env_file</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t422">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t422"><data value='log_config_change'>ConfigManager._log_config_change</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t18">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t18"><data value='is_admin_user'>is_admin_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t25">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t25"><data value='get_config_categories'>get_config_categories</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t59">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t59"><data value='get_configs'>get_configs</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t82">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t82"><data value='get_config'>get_config</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t116">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t116"><data value='update_config'>update_config</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t156">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t156"><data value='reset_config'>reset_config</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t192">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t192"><data value='batch_update_configs'>batch_update_configs</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t242">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t242"><data value='validate_configs'>validate_configs</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t268">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t268"><data value='export_configs'>export_configs</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t310">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html#t310"><data value='import_configs'>import_configs</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t14">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t14"><data value='require_data_scope'>require_data_scope</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t27">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t27"><data value='decorator'>require_data_scope.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t29">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t29"><data value='wrapper'>require_data_scope.decorator.wrapper</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t71">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t71"><data value='apply_data_scope_filter'>apply_data_scope_filter</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t86">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t86"><data value='decorator'>apply_data_scope_filter.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t88">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t88"><data value='wrapper'>apply_data_scope_filter.decorator.wrapper</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t114">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t114"><data value='check_data_scope_permission'>DataScopeMixin.check_data_scope_permission</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t144">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t144"><data value='apply_data_scope_filter'>DataScopeMixin.apply_data_scope_filter</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t161">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t161"><data value='get_queryset'>DataScopeMixin.get_queryset</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t170">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t170"><data value='dispatch'>DataScopeMixin.dispatch</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t188">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t188"><data value='check_object_access'>check_object_access</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t203">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t203"><data value='decorator'>check_object_access.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t205">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t205"><data value='wrapper'>check_object_access.decorator.wrapper</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t23">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t23"><data value='get_user_effective_departments'>UserDepartmentService.get_user_effective_departments</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t59">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t59"><data value='get_user_primary_department'>UserDepartmentService.get_user_primary_department</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t87">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t87"><data value='get_user_managed_departments'>UserDepartmentService.get_user_managed_departments</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t120">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t120"><data value='assign_user_to_department'>UserDepartmentService.assign_user_to_department</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t191">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t191"><data value='remove_user_from_department'>UserDepartmentService.remove_user_from_department</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t230">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t230"><data value='reassign_primary_department'>UserDepartmentService._reassign_primary_department</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t247">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t247"><data value='update_department_relation'>UserDepartmentService.update_department_relation</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t289">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t289"><data value='check_department_relation_validity'>UserDepartmentService.check_department_relation_validity</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t332">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t332"><data value='clear_user_cache'>UserDepartmentService.clear_user_cache</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t352">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t352"><data value='get_department_members'>UserDepartmentService.get_department_members</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t20">apps\common\example_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t20"><data value='get'>ExceptionDemoView.get</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t83">apps\common\example_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t83"><data value='create_user_demo'>create_user_demo</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t133">apps\common\example_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t133"><data value='paginated_demo'>paginated_demo</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html">apps\common\example_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t32">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t32"><data value='get'>ExampleAPIView.get</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t63">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t63"><data value='post'>ExampleAPIView.post</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t109">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t109"><data value='example_function_view'>example_function_view</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t151">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t151"><data value='example_update_view'>example_update_view</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t195">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t195"><data value='example_delete_view'>example_delete_view</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t238">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t238"><data value='post'>BestPracticeView.post</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t264">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t264"><data value='validate_request_data'>BestPracticeView._validate_request_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t283">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t283"><data value='process_business_logic'>BestPracticeView._process_business_logic</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t98">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t98"><data value='init__'>BusinessException.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t112">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t112"><data value='str__'>BusinessException.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t116">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t116"><data value='custom_exception_handler'>custom_exception_handler</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t227">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t227"><data value='get_error_code_from_status'>_get_error_code_from_status</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t243">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t243"><data value='get_error_message_from_response'>_get_error_message_from_response</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t72">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t72"><data value='init__'>FileSecurityValidator.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t82">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t82"><data value='validate_file'>FileSecurityValidator.validate_file</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t140">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t140"><data value='extract_file_info'>FileSecurityValidator._extract_file_info</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t160">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t160"><data value='validate_filename'>FileSecurityValidator._validate_filename</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t178">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t178"><data value='validate_file_size'>FileSecurityValidator._validate_file_size</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t188">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t188"><data value='validate_file_extension'>FileSecurityValidator._validate_file_extension</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t199">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t199"><data value='validate_mime_type'>FileSecurityValidator._validate_mime_type</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t223">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t223"><data value='validate_file_header'>FileSecurityValidator._validate_file_header</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t248">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t248"><data value='scan_file_content'>FileSecurityValidator._scan_file_content</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t296">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t296"><data value='init__'>SecureFileUploadHandler.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t305">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t305"><data value='handle_upload'>SecureFileUploadHandler.handle_upload</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t338">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t338"><data value='generate_safe_filename'>SecureFileUploadHandler._generate_safe_filename</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t353">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t353"><data value='create_upload_directory'>SecureFileUploadHandler._create_upload_directory</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t364">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t364"><data value='save_file'>SecureFileUploadHandler._save_file</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t21">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t21"><data value='upload_file'>upload_file</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t84">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t84"><data value='validate_file'>validate_file</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t125">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t125"><data value='get_upload_config'>get_upload_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t167">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t167"><data value='delete_file'>delete_file</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t203">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t203"><data value='list_uploaded_files'>list_uploaded_files</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t241">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html#t241"><data value='batch_upload'>batch_upload</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_789b0fc39a74c14f___init___py.html">apps\common\management\__init__.py</a></td>
                <td class="name left"><a href="z_789b0fc39a74c14f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8___init___py.html">apps\common\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t19">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t19"><data value='add_arguments'>Command.add_arguments</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t36">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t36"><data value='handle'>Command.handle</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t46">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t46"><data value='create_test_data'>Command.create_test_data</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t154">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t154"><data value='test_permissions'>Command.test_permissions</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t253">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t253"><data value='cleanup_test_data'>Command.cleanup_test_data</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t272">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t272"><data value='display_test_results'>Command.display_test_results</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t18">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t18"><data value='process_request'>JWTAuthenticationMiddleware.process_request</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t82">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t82"><data value='process_request'>PermissionMiddleware.process_request</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t120">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t120"><data value='check_api_permission'>PermissionMiddleware._check_api_permission</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t126">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t126"><data value='path_matches'>PermissionMiddleware._path_matches</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t140">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t140"><data value='process_request'>AuditLogMiddleware.process_request</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t150">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t150"><data value='process_response'>AuditLogMiddleware.process_response</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t222">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t222"><data value='get_operation_type'>AuditLogMiddleware._get_operation_type</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t243">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t243"><data value='get_operation_desc'>AuditLogMiddleware._get_operation_desc</data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t350">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t350"><data value='should_log_operation'>AuditLogMiddleware._should_log_operation</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t395">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t395"><data value='is_sensitive_operation'>AuditLogMiddleware._is_sensitive_operation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t408">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t408"><data value='get_client_ip'>AuditLogMiddleware._get_client_ip</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t18">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t18"><data value='soft_delete'>BaseModel.soft_delete</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t24">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t24"><data value='restore'>BaseModel.restore</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t33">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t33"><data value='get_queryset'>ActiveManager.get_queryset</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t23">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t23"><data value='get_data_scope_filter'>DataScopeService.get_data_scope_filter</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t64">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t64"><data value='get_dept_only_filter'>DataScopeService._get_dept_only_filter</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t96">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t96"><data value='get_dept_and_sub_filter'>DataScopeService._get_dept_and_sub_filter</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t131">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t131"><data value='get_custom_filter'>DataScopeService._get_custom_filter</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t174">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t174"><data value='get_user_departments'>DataScopeService.get_user_departments</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t196">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t196"><data value='get_user_managed_departments'>DataScopeService.get_user_managed_departments</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t220">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t220"><data value='get_user_roles_with_data_scope'>DataScopeService.get_user_roles_with_data_scope</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t265">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t265"><data value='apply_data_scope_to_queryset'>DataScopeService.apply_data_scope_to_queryset</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t324">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t324"><data value='check_data_access_permission'>DataScopeService.check_data_access_permission</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t379">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t379"><data value='check_self_only_access'>DataScopeService._check_self_only_access</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t393">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t393"><data value='check_dept_only_access'>DataScopeService._check_dept_only_access</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t412">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t412"><data value='check_dept_and_sub_access'>DataScopeService._check_dept_and_sub_access</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t436">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t436"><data value='check_custom_access'>DataScopeService._check_custom_access</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t467">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t467"><data value='get_user_data_scope'>DataScopeService.get_user_data_scope</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t519">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t519"><data value='get_user_accessible_departments'>DataScopeService.get_user_accessible_departments</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t571">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t571"><data value='clear_user_cache'>DataScopeService.clear_user_cache</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t600">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t600"><data value='refresh_user_cache'>DataScopeService.refresh_user_cache</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t622">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t622"><data value='validate_data_scope_field'>DataScopeService.validate_data_scope_field</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t14">apps\common\response.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t14"><data value='success'>ApiResponse.success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t34">apps\common\response.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t34"><data value='error'>ApiResponse.error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t55">apps\common\response.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t55"><data value='paginated_success'>ApiResponse.paginated_success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html">apps\common\response.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t32">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t32"><data value='test_error_code_constants'>ErrorCodeTestCase.test_error_code_constants</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t64">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t64"><data value='test_business_exception_creation'>BusinessExceptionTestCase.test_business_exception_creation</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t82">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t82"><data value='test_business_exception_str'>BusinessExceptionTestCase.test_business_exception_str</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t95">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t95"><data value='setUp'>CustomExceptionHandlerTestCase.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t104">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t104"><data value='test_business_exception_handling'>CustomExceptionHandlerTestCase.test_business_exception_handling</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t131">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t131"><data value='test_validation_error_handling'>CustomExceptionHandlerTestCase.test_validation_error_handling</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t156">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t156"><data value='test_integrity_error_handling'>CustomExceptionHandlerTestCase.test_integrity_error_handling</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t178">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t178"><data value='test_drf_exception_handling'>CustomExceptionHandlerTestCase.test_drf_exception_handling</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t207">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t207"><data value='test_unhandled_exception'>CustomExceptionHandlerTestCase.test_unhandled_exception</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t235">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t235"><data value='test_get_error_code_from_status'>HelperFunctionsTestCase.test_get_error_code_from_status</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t255">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t255"><data value='test_get_error_message_from_response'>HelperFunctionsTestCase.test_get_error_message_from_response</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t287">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t287"><data value='get'>TestAPIView.get</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t309">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t309"><data value='setUp'>ExceptionHandlerIntegrationTestCase.setUp</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t319">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t319"><data value='test_business_exception_integration'>ExceptionHandlerIntegrationTestCase.test_business_exception_integration</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t332">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t332"><data value='test_api_response_format'>ExceptionHandlerIntegrationTestCase.test_api_response_format</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t379">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t379"><data value='setUp'>ExceptionLoggingTestCase.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t389">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t389"><data value='test_exception_logging'>ExceptionLoggingTestCase.test_exception_logging</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t418">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t418"><data value='test_system_exception_logging'>ExceptionLoggingTestCase.test_system_exception_logging</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t21">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t21"><data value='setUp'>FileSecurityValidatorTest.setUp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t24">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t24"><data value='test_valid_image_file'>FileSecurityValidatorTest.test_valid_image_file</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t38">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t38"><data value='test_invalid_file_extension'>FileSecurityValidatorTest.test_invalid_file_extension</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t50">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t50"><data value='test_file_size_limit'>FileSecurityValidatorTest.test_file_size_limit</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t64">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t64"><data value='test_dangerous_filename'>FileSecurityValidatorTest.test_dangerous_filename</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t76">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t76"><data value='test_empty_file'>FileSecurityValidatorTest.test_empty_file</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t88">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t88"><data value='test_malicious_content'>FileSecurityValidatorTest.test_malicious_content</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t105">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t105"><data value='setUp'>SecureFileUploadHandlerTest.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t109">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t109"><data value='tearDown'>SecureFileUploadHandlerTest.tearDown</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t116">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t116"><data value='test_handle_upload_success'>SecureFileUploadHandlerTest.test_handle_upload_success</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t131">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t131"><data value='test_generate_safe_filename'>SecureFileUploadHandlerTest.test_generate_safe_filename</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t143">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t143"><data value='setUp'>FileUploadAPITest.setUp</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t151">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t151"><data value='test_upload_config_api'>FileUploadAPITest.test_upload_config_api</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t163">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t163"><data value='test_upload_file_api'>FileUploadAPITest.test_upload_file_api</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t184">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t184"><data value='test_upload_invalid_file'>FileUploadAPITest.test_upload_invalid_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t203">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t203"><data value='test_validate_file_api'>FileUploadAPITest.test_validate_file_api</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t225">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t225"><data value='test_batch_upload_api'>FileUploadAPITest.test_batch_upload_api</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t249">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t249"><data value='test_unauthorized_access'>FileUploadAPITest.test_unauthorized_access</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t261">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t261"><data value='test_file_type_detection'>FileSecurityIntegrationTest.test_file_type_detection</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t276">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t276"><data value='test_security_scan_comprehensive'>FileSecurityIntegrationTest.test_security_scan_comprehensive</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t18">apps\common\test_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t18"><data value='get'>TestExceptionView.get</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t25">apps\common\test_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t25"><data value='post'>TestExceptionView.post</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t45">apps\common\test_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t45"><data value='test_response_helper'>test_response_helper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html">apps\common\test_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t21">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t21"><data value='test_success_response'>ApiResponseTestCase.test_success_response</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t32">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t32"><data value='test_success_response_with_custom_code'>ApiResponseTestCase.test_success_response_with_custom_code</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t39">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t39"><data value='test_error_response'>ApiResponseTestCase.test_error_response</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t52">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t52"><data value='test_error_response_with_custom_status'>ApiResponseTestCase.test_error_response_with_custom_status</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t63">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t63"><data value='test_paginated_success_response'>ApiResponseTestCase.test_paginated_success_response</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t84">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t84"><data value='test_error_code_constants'>ErrorCodeTestCase.test_error_code_constants</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t110">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t110"><data value='test_business_exception_creation'>BusinessExceptionTestCase.test_business_exception_creation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t123">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t123"><data value='test_business_exception_default_values'>BusinessExceptionTestCase.test_business_exception_default_values</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t131">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t131"><data value='test_business_exception_inheritance'>BusinessExceptionTestCase.test_business_exception_inheritance</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t141">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t141"><data value='setUp'>CustomExceptionHandlerTestCase.setUp</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t153">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t153"><data value='test_handle_business_exception'>CustomExceptionHandlerTestCase.test_handle_business_exception</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t169">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t169"><data value='test_handle_validation_error_with_message_dict'>CustomExceptionHandlerTestCase.test_handle_validation_error_with_message_dict</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t183">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t183"><data value='test_handle_validation_error_with_messages'>CustomExceptionHandlerTestCase.test_handle_validation_error_with_messages</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t194">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t194"><data value='test_handle_integrity_error'>CustomExceptionHandlerTestCase.test_handle_integrity_error</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t205">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t205"><data value='test_handle_drf_exception'>CustomExceptionHandlerTestCase.test_handle_drf_exception</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t222">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t222"><data value='test_handle_unhandled_exception'>CustomExceptionHandlerTestCase.test_handle_unhandled_exception</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t233">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t233"><data value='test_handle_exception_without_request'>CustomExceptionHandlerTestCase.test_handle_exception_without_request</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t248">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t248"><data value='test_api_business_exception_response'>ExceptionHandlerIntegrationTestCase.test_api_business_exception_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t254">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t254"><data value='test_api_validation_error_response'>ExceptionHandlerIntegrationTestCase.test_api_validation_error_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t258">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t258"><data value='test_api_permission_denied_response'>ExceptionHandlerIntegrationTestCase.test_api_permission_denied_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t266">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t266"><data value='test_raise_business_error'>UtilsTestCase.test_raise_business_error</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t282">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t282"><data value='test_validate_required_fields_success'>UtilsTestCase.test_validate_required_fields_success</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t292">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t292"><data value='test_validate_required_fields_missing'>UtilsTestCase.test_validate_required_fields_missing</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t307">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t307"><data value='test_validate_required_fields_empty_values'>UtilsTestCase.test_validate_required_fields_empty_values</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t321">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t321"><data value='test_validate_user_exists_success'>UtilsTestCase.test_validate_user_exists_success</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t329">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t329"><data value='test_validate_user_exists_failure'>UtilsTestCase.test_validate_user_exists_failure</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t340">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t340"><data value='test_validate_user_active_success'>UtilsTestCase.test_validate_user_active_success</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t350">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t350"><data value='test_validate_user_active_failure'>UtilsTestCase.test_validate_user_active_failure</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t368">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t368"><data value='test_success_with_data'>ResponseHelperTestCase.test_success_with_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t380">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t380"><data value='test_success_without_data'>ResponseHelperTestCase.test_success_without_data</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t391">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t391"><data value='test_created_success'>ResponseHelperTestCase.test_created_success</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t401">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t401"><data value='test_updated_success'>ResponseHelperTestCase.test_updated_success</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t410">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t410"><data value='test_deleted_success'>ResponseHelperTestCase.test_deleted_success</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t419">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t419"><data value='test_validation_error'>ResponseHelperTestCase.test_validation_error</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t429">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t429"><data value='test_permission_denied'>ResponseHelperTestCase.test_permission_denied</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t439">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t439"><data value='test_not_found'>ResponseHelperTestCase.test_not_found</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6___init___py.html">apps\common\tests\__init__.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t22">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t22"><data value='setUp'>DataScopeServiceTest.setUp</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t102">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t102"><data value='test_get_data_scope_filter_all'>DataScopeServiceTest.test_get_data_scope_filter_all</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t107">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t107"><data value='test_get_data_scope_filter_self_only'>DataScopeServiceTest.test_get_data_scope_filter_self_only</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t113">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t113"><data value='test_get_data_scope_filter_dept_only'>DataScopeServiceTest.test_get_data_scope_filter_dept_only</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t119">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t119"><data value='test_get_data_scope_filter_dept_and_sub'>DataScopeServiceTest.test_get_data_scope_filter_dept_and_sub</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t134">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t134"><data value='test_get_data_scope_filter_custom'>DataScopeServiceTest.test_get_data_scope_filter_custom</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t143">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t143"><data value='test_get_user_roles_with_data_scope'>DataScopeServiceTest.test_get_user_roles_with_data_scope</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t153">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t153"><data value='test_get_user_data_scope'>DataScopeServiceTest.test_get_user_data_scope</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t168">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t168"><data value='test_apply_data_scope_to_queryset'>DataScopeServiceTest.test_apply_data_scope_to_queryset</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t184">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t184"><data value='test_check_data_access_permission'>DataScopeServiceTest.test_check_data_access_permission</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t210">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t210"><data value='test_get_user_accessible_departments'>DataScopeServiceTest.test_get_user_accessible_departments</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t232">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t232"><data value='test_cache_functionality'>DataScopeServiceTest.test_cache_functionality</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t256">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t256"><data value='setUp'>UserDepartmentServiceTest.setUp</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t275">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t275"><data value='test_assign_user_to_department'>UserDepartmentServiceTest.test_assign_user_to_department</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t287">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t287"><data value='test_get_user_primary_department'>UserDepartmentServiceTest.test_get_user_primary_department</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t297">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t297"><data value='test_remove_user_from_department'>UserDepartmentServiceTest.test_remove_user_from_department</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t315">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t315"><data value='test_department_relation_validity'>UserDepartmentServiceTest.test_department_relation_validity</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t333">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t333"><data value='setUp'>DataScopeMixinTest.setUp</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t354">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t354"><data value='test_data_scope_mixin_permission_check'>DataScopeMixinTest.test_data_scope_mixin_permission_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t371">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t371"><data value='test_data_scope_mixin_superuser'>DataScopeMixinTest.test_data_scope_mixin_superuser</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t396">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t396"><data value='setUp'>DataScopeIntegrationTest.setUp</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t468">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t468"><data value='test_hierarchical_data_access'>DataScopeIntegrationTest.test_hierarchical_data_access</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t505">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t505"><data value='test_multi_role_data_scope'>DataScopeIntegrationTest.test_multi_role_data_scope</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t514">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t514"><data value='test_department_change_impact'>DataScopeIntegrationTest.test_department_change_impact</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_urls_py.html">apps\common\urls.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t9">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t9"><data value='raise_business_error'>raise_business_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t24">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t24"><data value='validate_required_fields'>validate_required_fields</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t47">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t47"><data value='validate_user_exists'>validate_user_exists</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t64">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t64"><data value='validate_user_active'>validate_user_active</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t81">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t81"><data value='validate_permission'>validate_permission</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t101">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t101"><data value='success_with_data'>ResponseHelper.success_with_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t106">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t106"><data value='success_without_data'>ResponseHelper.success_without_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t111">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t111"><data value='created_success'>ResponseHelper.created_success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t116">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t116"><data value='updated_success'>ResponseHelper.updated_success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t121">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t121"><data value='deleted_success'>ResponseHelper.deleted_success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t126">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t126"><data value='validation_error'>ResponseHelper.validation_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t134">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t134"><data value='permission_denied'>ResponseHelper.permission_denied</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t143">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t143"><data value='not_found'>ResponseHelper.not_found</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c___init___py.html">apps\departments\__init__.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_apps_py.html">apps\departments\apps.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0001_initial_py.html">apps\departments\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7_0001_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0002_initial_py.html">apps\departments\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7_0002_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7___init___py.html">apps\departments\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t10">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t10"><data value='get_current_date'>get_current_date</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t43">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t43"><data value='get_managers'>Department.get_managers</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t53">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t53"><data value='get_primary_manager'>Department.get_primary_manager</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t57">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t57"><data value='str__'>Department.__str__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t116">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t116"><data value='is_effective'>UserDepartment.is_effective</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t126">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t126"><data value='get_effective_relations'>UserDepartment.get_effective_relations</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t143">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t143"><data value='str__'>UserDepartment.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t47">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t47"><data value='get_children_count'>DepartmentSerializer.get_children_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t51">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t51"><data value='get_member_count'>DepartmentSerializer.get_member_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t55">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t55"><data value='get_manager_count'>DepartmentSerializer.get_manager_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t59">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t59"><data value='validate_code'>DepartmentSerializer.validate_code</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t69">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t69"><data value='validate_parent'>DepartmentSerializer.validate_parent</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t102">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t102"><data value='get_children'>DepartmentTreeSerializer.get_children</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t107">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t107"><data value='get_member_count'>DepartmentTreeSerializer.get_member_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t111">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t111"><data value='get_manager_info'>DepartmentTreeSerializer.get_manager_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t138">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t138"><data value='get_member_count'>DepartmentListSerializer.get_member_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t173">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t173"><data value='get_user_info'>UserDepartmentSerializer.get_user_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t183">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t183"><data value='get_department_info'>UserDepartmentSerializer.get_department_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t192">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t192"><data value='get_is_effective_now'>UserDepartmentSerializer.get_is_effective_now</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t196">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t196"><data value='validate'>UserDepartmentSerializer.validate</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t259">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t259"><data value='validate_user_id'>DepartmentMemberSerializer.validate_user_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t268">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t268"><data value='validate'>DepartmentMemberSerializer.validate</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t299">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t299"><data value='get_path'>DepartmentPathSerializer.get_path</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t304">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t304"><data value='get_ancestors'>DepartmentPathSerializer.get_ancestors</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t20">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t20"><data value='setUp'>DepartmentModelTestCase.setUp</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t50">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t50"><data value='test_department_creation'>DepartmentModelTestCase.test_department_creation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t62">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t62"><data value='test_department_hierarchy'>DepartmentModelTestCase.test_department_hierarchy</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t82">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t82"><data value='test_department_soft_delete'>DepartmentModelTestCase.test_department_soft_delete</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t94">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t94"><data value='test_department_restore'>DepartmentModelTestCase.test_department_restore</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t105">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t105"><data value='test_user_department_relation'>DepartmentModelTestCase.test_user_department_relation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t123">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t123"><data value='test_user_department_effective_date'>DepartmentModelTestCase.test_user_department_effective_date</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t141">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t141"><data value='test_department_managers'>DepartmentModelTestCase.test_department_managers</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t182">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t182"><data value='setUp'>DepartmentAPITestCase.setUp</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t229">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t229"><data value='get_jwt_token'>DepartmentAPITestCase._get_jwt_token</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t234">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t234"><data value='authenticate_as_admin'>DepartmentAPITestCase._authenticate_as_admin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t238">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t238"><data value='authenticate_as_user'>DepartmentAPITestCase._authenticate_as_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t242">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t242"><data value='test_get_department_list'>DepartmentAPITestCase.test_get_department_list</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t252">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t252"><data value='test_get_department_tree'>DepartmentAPITestCase.test_get_department_tree</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t278">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t278"><data value='test_create_department'>DepartmentAPITestCase.test_create_department</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t298">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t298"><data value='test_create_department_duplicate_code'>DepartmentAPITestCase.test_create_department_duplicate_code</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t311">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t311"><data value='test_update_department'>DepartmentAPITestCase.test_update_department</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t329">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t329"><data value='test_delete_department_with_children'>DepartmentAPITestCase.test_delete_department_with_children</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t338">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t338"><data value='test_delete_department_with_members'>DepartmentAPITestCase.test_delete_department_with_members</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t353">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t353"><data value='test_delete_empty_department'>DepartmentAPITestCase.test_delete_empty_department</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t373">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t373"><data value='test_restore_department'>DepartmentAPITestCase.test_restore_department</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t394">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t394"><data value='test_toggle_department_active'>DepartmentAPITestCase.test_toggle_department_active</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t407">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t407"><data value='test_add_department_member'>DepartmentAPITestCase.test_add_department_member</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t432">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t432"><data value='test_add_duplicate_member'>DepartmentAPITestCase.test_add_duplicate_member</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t450">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t450"><data value='test_get_department_members'>DepartmentAPITestCase.test_get_department_members</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t472">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t472"><data value='test_update_department_member'>DepartmentAPITestCase.test_update_department_member</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t502">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t502"><data value='test_remove_department_member'>DepartmentAPITestCase.test_remove_department_member</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t524">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t524"><data value='test_get_department_path'>DepartmentAPITestCase.test_get_department_path</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t538">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t538"><data value='test_get_department_statistics'>DepartmentAPITestCase.test_get_department_statistics</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t553">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t553"><data value='test_unauthorized_access'>DepartmentAPITestCase.test_unauthorized_access</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_urls_py.html">apps\departments\urls.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t25">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t25"><data value='get_serializer_class'>DepartmentViewSet.get_serializer_class</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t37">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t37"><data value='get_queryset'>DepartmentViewSet.get_queryset</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t75">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t75"><data value='list'>DepartmentViewSet.list</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t114">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t114"><data value='create'>DepartmentViewSet.create</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t134">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t134"><data value='retrieve'>DepartmentViewSet.retrieve</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t155">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t155"><data value='update'>DepartmentViewSet.update</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t180">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t180"><data value='destroy'>DepartmentViewSet.destroy</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t220">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t220"><data value='tree'>DepartmentViewSet.tree</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t241">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t241"><data value='members'>DepartmentViewSet.members</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t279">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t279"><data value='add_member'>DepartmentViewSet.add_member</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t336">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t336"><data value='update_member'>DepartmentViewSet.update_member</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t390">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t390"><data value='remove_member'>DepartmentViewSet.remove_member</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t434">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t434"><data value='restore'>DepartmentViewSet.restore</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t465">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t465"><data value='toggle_active'>DepartmentViewSet.toggle_active</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t490">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t490"><data value='path'>DepartmentViewSet.path</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t512">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t512"><data value='statistics'>DepartmentViewSet.statistics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358___init___py.html">apps\health\__init__.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_apps_py.html">apps\health\apps.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_urls_py.html">apps\health\urls.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t21">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t21"><data value='health_check'>health_check</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t80">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t80"><data value='liveness_probe'>liveness_probe</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t93">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t93"><data value='readiness_probe'>readiness_probe</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t114">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t114"><data value='check_database'>check_database</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t137">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t137"><data value='check_cache'>check_cache</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t170">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t170"><data value='check_message_queue'>check_message_queue</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t205">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t205"><data value='check_system_resources'>check_system_resources</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t262">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t262"><data value='check_disk_space'>check_disk_space</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t299">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html#t299"><data value='system_info'>system_info</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc___init___py.html">apps\monitoring\__init__.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html#t12">apps\monitoring\apps.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html#t12"><data value='ready'>MonitoringConfig.ready</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html">apps\monitoring\apps.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_62e9cac5df6ba8e3_0001_initial_py.html">apps\monitoring\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_62e9cac5df6ba8e3_0001_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_62e9cac5df6ba8e3___init___py.html">apps\monitoring\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_62e9cac5df6ba8e3___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t54">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t54"><data value='str__'>SystemMetrics.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t103">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t103"><data value='str__'>ApplicationMetrics.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t149">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t149"><data value='str__'>ServiceHealthCheck.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t211">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t211"><data value='str__'>AlertRule.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t270">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t270"><data value='str__'>AlertRecord.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>108</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="108 108">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t25">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t25"><data value='init__'>SystemMonitorService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t28">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t28"><data value='collect_system_metrics'>SystemMonitorService.collect_system_metrics</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t93">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t93"><data value='collect_application_metrics'>SystemMonitorService.collect_application_metrics</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t169">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t169"><data value='get_database_connections'>SystemMonitorService._get_database_connections</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t189">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t189"><data value='get_cache_hit_rate'>SystemMonitorService._get_cache_hit_rate</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t206">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t206"><data value='get_queue_stats'>SystemMonitorService._get_queue_stats</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t243">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t243"><data value='init__'>HealthCheckService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t246">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t246"><data value='check_all_services'>HealthCheckService.check_all_services</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t264">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t264"><data value='check_database'>HealthCheckService.check_database</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t300">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t300"><data value='check_cache'>HealthCheckService.check_cache</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t338">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t338"><data value='check_queue'>HealthCheckService.check_queue</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t375">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t375"><data value='check_storage'>HealthCheckService.check_storage</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t423">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t423"><data value='init__'>AlertService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t426">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t426"><data value='check_alert_rules'>AlertService.check_alert_rules</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t436">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t436"><data value='check_single_rule'>AlertService._check_single_rule</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t473">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t473"><data value='get_metric_value'>AlertService._get_metric_value</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t499">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t499"><data value='evaluate_condition'>AlertService._evaluate_condition</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t516">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t516"><data value='send_alert_notification'>AlertService._send_alert_notification</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t16">apps\monitoring\tasks.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t16"><data value='collect_system_metrics_task'>collect_system_metrics_task</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t50">apps\monitoring\tasks.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t50"><data value='health_check_task'>health_check_task</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t89">apps\monitoring\tasks.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t89"><data value='check_alert_rules_task'>check_alert_rules_task</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t120">apps\monitoring\tasks.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t120"><data value='cleanup_old_metrics_task'>cleanup_old_metrics_task</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t162">apps\monitoring\tasks.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html#t162"><data value='generate_monitoring_report_task'>generate_monitoring_report_task</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html">apps\monitoring\tasks.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_urls_py.html">apps\monitoring\urls.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t26">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t26"><data value='get_system_overview'>get_system_overview</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t86">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t86"><data value='get_system_metrics'>get_system_metrics</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t172">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t172"><data value='get_application_metrics'>get_application_metrics</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t238">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t238"><data value='get_service_health'>get_service_health</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t289">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t289"><data value='run_health_check'>run_health_check</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t325">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t325"><data value='collect_metrics'>collect_metrics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t363">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html#t363"><data value='get_alerts'>get_alerts</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f___init___py.html">apps\permissions\__init__.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_apps_py.html">apps\permissions\apps.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t12">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t12"><data value='require_permission'>require_permission</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t24">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t24"><data value='decorator'>require_permission.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t26">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t26"><data value='wrapper'>require_permission.decorator.wrapper</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t54">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t54"><data value='require_permissions'>require_permissions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t67">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t67"><data value='decorator'>require_permissions.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t69">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t69"><data value='wrapper'>require_permissions.decorator.wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t117">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t117"><data value='require_data_scope'>require_data_scope</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t129">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t129"><data value='decorator'>require_data_scope.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t131">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t131"><data value='wrapper'>require_data_scope.decorator.wrapper</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t173">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t173"><data value='require_role'>require_role</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t185">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t185"><data value='decorator'>require_role.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t187">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t187"><data value='wrapper'>require_role.decorator.wrapper</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t226">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t226"><data value='check_permissions'>PermissionMixin.check_permissions</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t280">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t280"><data value='dispatch'>PermissionMixin.dispatch</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e434bc180f346938___init___py.html">apps\permissions\management\__init__.py</a></td>
                <td class="name left"><a href="z_e434bc180f346938___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69___init___py.html">apps\permissions\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t11">apps\permissions\management\commands\init_permissions.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t11"><data value='handle'>Command.handle</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t22">apps\permissions\management\commands\init_permissions.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t22"><data value='create_permissions'>Command.create_permissions</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t268">apps\permissions\management\commands\init_permissions.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t268"><data value='create_roles'>Command.create_roles</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html">apps\permissions\management\commands\init_permissions.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0001_initial_py.html">apps\permissions\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33_0001_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0002_initial_py.html">apps\permissions\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33_0002_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33___init___py.html">apps\permissions\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t41">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t41"><data value='str__'>Role.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t85">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t85"><data value='str__'>Permission.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t106">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t106"><data value='str__'>UserRole.__str__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t32">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t32"><data value='get_children_count'>PermissionSerializer.get_children_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t36">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t36"><data value='get_permission_path'>PermissionSerializer.get_permission_path</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t45">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t45"><data value='validate_code'>PermissionSerializer.validate_code</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t57">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t57"><data value='validate_parent'>PermissionSerializer.validate_parent</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t81">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t81"><data value='get_children'>PermissionTreeSerializer.get_children</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t111">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t111"><data value='get_permissions_info'>RoleSerializer.get_permissions_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t116">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t116"><data value='get_user_count'>RoleSerializer.get_user_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t120">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t120"><data value='validate_code'>RoleSerializer.validate_code</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t132">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t132"><data value='create'>RoleSerializer.create</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t144">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t144"><data value='update'>RoleSerializer.update</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t179">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t179"><data value='get_user_info'>UserRoleSerializer.get_user_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t189">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t189"><data value='get_role_info'>UserRoleSerializer.get_role_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t199">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t199"><data value='get_department_info'>UserRoleSerializer.get_department_info</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t210">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t210"><data value='validate'>UserRoleSerializer.validate</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t245">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t245"><data value='validate_role_id'>RoleAssignmentSerializer.validate_role_id</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t253">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t253"><data value='validate_department_id'>RoleAssignmentSerializer.validate_department_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t262">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t262"><data value='validate_user_ids'>RoleAssignmentSerializer.validate_user_ids</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t281">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t281"><data value='get_permissions'>UserPermissionSerializer.get_permissions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t286">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t286"><data value='get_roles'>UserPermissionSerializer.get_roles</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t18">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t18"><data value='get_user_permissions'>PermissionService.get_user_permissions</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t51">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t51"><data value='get_user_roles'>PermissionService.get_user_roles</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t87">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t87"><data value='get_user_data_scope'>PermissionService.get_user_data_scope</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t123">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t123"><data value='check_user_permission'>PermissionService.check_user_permission</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t144">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t144"><data value='get_user_menu_permissions'>PermissionService.get_user_menu_permissions</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t166">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t166"><data value='get_user_button_permissions'>PermissionService.get_user_button_permissions</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t185">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t185"><data value='get_user_api_permissions'>PermissionService.get_user_api_permissions</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t211">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t211"><data value='build_permission_tree'>PermissionService._build_permission_tree</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t237">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t237"><data value='sort_permissions'>PermissionService._build_permission_tree.sort_permissions</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t247">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t247"><data value='clear_user_permission_cache'>PermissionService.clear_user_permission_cache</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t268">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t268"><data value='refresh_user_permissions'>PermissionService.refresh_user_permissions</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t289">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t289"><data value='check_role_can_delete'>RoleService.check_role_can_delete</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t298">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t298"><data value='get_role_users'>RoleService.get_role_users</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t308">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t308"><data value='assign_role_to_users'>RoleService.assign_role_to_users</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t19">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t19"><data value='setUp'>PermissionModelTest.setUp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t29">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t29"><data value='test_permission_creation'>PermissionModelTest.test_permission_creation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t37">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t37"><data value='test_permission_str'>PermissionModelTest.test_permission_str</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t41">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t41"><data value='test_permission_soft_delete'>PermissionModelTest.test_permission_soft_delete</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t51">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t51"><data value='setUp'>RoleModelTest.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t64">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t64"><data value='test_role_creation'>RoleModelTest.test_role_creation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t72">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t72"><data value='test_role_permissions'>RoleModelTest.test_role_permissions</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t77">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t77"><data value='test_role_str'>RoleModelTest.test_role_str</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t85">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t85"><data value='setUp'>UserRoleModelTest.setUp</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t101">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t101"><data value='test_user_role_creation'>UserRoleModelTest.test_user_role_creation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t112">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t112"><data value='test_user_role_str'>UserRoleModelTest.test_user_role_str</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t126">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t126"><data value='setUp'>PermissionServiceTest.setUp</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t174">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t174"><data value='test_get_user_permissions'>PermissionServiceTest.test_get_user_permissions</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t181">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t181"><data value='test_get_user_roles'>PermissionServiceTest.test_get_user_roles</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t187">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t187"><data value='test_get_user_data_scope'>PermissionServiceTest.test_get_user_data_scope</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t192">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t192"><data value='test_check_user_permission'>PermissionServiceTest.test_check_user_permission</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t198">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t198"><data value='test_get_user_menu_permissions'>PermissionServiceTest.test_get_user_menu_permissions</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t203">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t203"><data value='test_get_user_button_permissions'>PermissionServiceTest.test_get_user_button_permissions</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t212">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t212"><data value='setUp'>RoleServiceTest.setUp</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t224">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t224"><data value='test_check_role_can_delete_with_users'>RoleServiceTest.test_check_role_can_delete_with_users</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t231">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t231"><data value='test_check_role_can_delete_without_users'>RoleServiceTest.test_check_role_can_delete_without_users</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t237">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t237"><data value='test_get_role_users'>RoleServiceTest.test_get_role_users</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t248">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t248"><data value='setUp'>PermissionAPITest.setUp</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t286">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t286"><data value='test_permission_list'>PermissionAPITest.test_permission_list</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t292">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t292"><data value='test_permission_create'>PermissionAPITest.test_permission_create</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t304">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t304"><data value='test_permission_tree'>PermissionAPITest.test_permission_tree</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t314">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t314"><data value='setUp'>RoleAPITest.setUp</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t352">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t352"><data value='test_role_list'>RoleAPITest.test_role_list</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t358">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t358"><data value='test_role_create'>RoleAPITest.test_role_create</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t371">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t371"><data value='test_role_data_scopes'>RoleAPITest.test_role_data_scopes</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_urls_py.html">apps\permissions\urls.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t30">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t30"><data value='get_queryset'>PermissionViewSet.get_queryset</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t63">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t63"><data value='get_serializer_class'>PermissionViewSet.get_serializer_class</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t70">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t70"><data value='tree'>PermissionViewSet.tree</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t86">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t86"><data value='types'>PermissionViewSet.types</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t95">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t95"><data value='create'>PermissionViewSet.create</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t108">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t108"><data value='update'>PermissionViewSet.update</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t123">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t123"><data value='destroy'>PermissionViewSet.destroy</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t150">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t150"><data value='get_queryset'>RoleViewSet.get_queryset</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t175">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t175"><data value='create'>RoleViewSet.create</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t188">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t188"><data value='update'>RoleViewSet.update</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t204">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t204"><data value='data_scopes'>RoleViewSet.data_scopes</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t214">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t214"><data value='permissions'>RoleViewSet.permissions</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t225">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t225"><data value='assign_permissions'>RoleViewSet.assign_permissions</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t249">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t249"><data value='users'>RoleViewSet.users</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t264">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t264"><data value='assign_users'>RoleViewSet.assign_users</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t297">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t297"><data value='destroy'>RoleViewSet.destroy</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t320">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t320"><data value='get_queryset'>UserRoleViewSet.get_queryset</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t344">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t344"><data value='batch_delete'>UserRoleViewSet.batch_delete</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t359">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t359"><data value='user_permissions'>UserRoleViewSet.user_permissions</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d___init___py.html">apps\users\__init__.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_apps_py.html">apps\users\apps.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_apps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0001_initial_py.html">apps\users\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb_0001_initial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html">apps\users\migrations\0002_alter_userprofile_managers.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb___init___py.html">apps\users\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t11">apps\users\models.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t11"><data value='get_queryset'>ActiveUserManager.get_queryset</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t45">apps\users\models.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t45"><data value='str__'>UserProfile.__str__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html">apps\users\models.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t47">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t47"><data value='validate_username'>UserSerializer.validate_username</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t58">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t58"><data value='validate_email'>UserSerializer.validate_email</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t69">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t69"><data value='validate_phone'>UserSerializer.validate_phone</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t80">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t80"><data value='update'>UserSerializer.update</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t115">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t115"><data value='validate_username'>UserCreateSerializer.validate_username</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t121">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t121"><data value='validate_email'>UserCreateSerializer.validate_email</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t127">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t127"><data value='validate_phone'>UserCreateSerializer.validate_phone</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t133">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t133"><data value='validate_password'>UserCreateSerializer.validate_password</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t141">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t141"><data value='validate'>UserCreateSerializer.validate</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t153">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t153"><data value='create'>UserCreateSerializer.create</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t180">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t180"><data value='validate_email'>UserProfileSerializer.validate_email</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t191">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t191"><data value='validate_phone'>UserProfileSerializer.validate_phone</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t209">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t209"><data value='validate_new_password'>PasswordResetSerializer.validate_new_password</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t217">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t217"><data value='validate'>PasswordResetSerializer.validate</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t237">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t237"><data value='validate_new_password'>ChangePasswordSerializer.validate_new_password</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t245">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t245"><data value='validate'>ChangePasswordSerializer.validate</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t257">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t257"><data value='validate_old_password'>ChangePasswordSerializer.validate_old_password</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t19">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t19"><data value='setUp'>UserModelTestCase.setUp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t29">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t29"><data value='test_create_user'>UserModelTestCase.test_create_user</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t40">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t40"><data value='test_create_superuser'>UserModelTestCase.test_create_superuser</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t51">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t51"><data value='test_soft_delete'>UserModelTestCase.test_soft_delete</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t64">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t64"><data value='test_restore_user'>UserModelTestCase.test_restore_user</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t76">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t76"><data value='test_user_str_representation'>UserModelTestCase.test_user_str_representation</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t86">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t86"><data value='setUp'>UserAPITestCase.setUp</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t111">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t111"><data value='get_jwt_token'>UserAPITestCase._get_jwt_token</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t116">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t116"><data value='authenticate_as_admin'>UserAPITestCase._authenticate_as_admin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t120">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t120"><data value='authenticate_as_user'>UserAPITestCase._authenticate_as_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t124">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t124"><data value='test_get_user_list'>UserAPITestCase.test_get_user_list</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t148">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t148"><data value='test_get_user_list_with_search'>UserAPITestCase.test_get_user_list_with_search</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t161">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t161"><data value='test_create_user'>UserAPITestCase.test_create_user</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t182">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t182"><data value='test_create_user_duplicate_username'>UserAPITestCase.test_create_user_duplicate_username</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t197">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t197"><data value='test_create_user_password_mismatch'>UserAPITestCase.test_create_user_password_mismatch</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t212">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t212"><data value='test_get_user_detail'>UserAPITestCase.test_get_user_detail</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t222">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t222"><data value='test_update_user'>UserAPITestCase.test_update_user</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t240">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t240"><data value='test_delete_user'>UserAPITestCase.test_delete_user</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t253">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t253"><data value='test_cannot_delete_self'>UserAPITestCase.test_cannot_delete_self</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t262">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t262"><data value='test_restore_user'>UserAPITestCase.test_restore_user</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t278">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t278"><data value='test_toggle_user_active'>UserAPITestCase.test_toggle_user_active</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t291">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t291"><data value='test_reset_password'>UserAPITestCase.test_reset_password</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t308">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t308"><data value='test_get_profile'>UserAPITestCase.test_get_profile</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t318">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t318"><data value='test_update_profile'>UserAPITestCase.test_update_profile</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t335">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t335"><data value='test_change_password'>UserAPITestCase.test_change_password</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t353">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t353"><data value='test_change_password_wrong_old_password'>UserAPITestCase.test_change_password_wrong_old_password</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t366">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t366"><data value='test_unauthorized_access'>UserAPITestCase.test_unauthorized_access</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_urls_py.html">apps\users\urls.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t27">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t27"><data value='get_serializer_class'>UserViewSet.get_serializer_class</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t41">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t41"><data value='get_queryset'>UserViewSet.get_queryset</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t85">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t85"><data value='list'>UserViewSet.list</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t124">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t124"><data value='create'>UserViewSet.create</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t144">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t144"><data value='retrieve'>UserViewSet.retrieve</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t165">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t165"><data value='update'>UserViewSet.update</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t190">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t190"><data value='destroy'>UserViewSet.destroy</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t221">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t221"><data value='profile'>UserViewSet.profile</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t236">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t236"><data value='update_profile'>UserViewSet.update_profile</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t259">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t259"><data value='reset_password'>UserViewSet.reset_password</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t289">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t289"><data value='change_password'>UserViewSet.change_password</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t310">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t310"><data value='restore'>UserViewSet.restore</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t341">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t341"><data value='toggle_active'>UserViewSet.toggle_active</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8449</td>
                <td>7783</td>
                <td>0</td>
                <td class="right" data-ratio="666 8449">8%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
