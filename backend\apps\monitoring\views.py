"""
系统监控API视图
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Avg, <PERSON>, <PERSON>, Count
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import logging

from .models import SystemMetrics, ApplicationMetrics, ServiceHealthCheck, AlertRule, AlertRecord
from .services import system_monitor, health_checker, alert_service
from apps.common.response import ApiResponse
from apps.common.exceptions import BusinessException, ErrorCode
from apps.permissions.decorators import require_permissions

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['monitoring:view'])
def get_system_overview(request):
    """
    获取系统监控概览
    """
    try:
        # 获取最新的系统指标
        latest_system = SystemMetrics.objects.first()
        latest_app = ApplicationMetrics.objects.first()
        
        # 获取最近的健康检查结果
        recent_health_checks = ServiceHealthCheck.objects.filter(
            checked_at__gte=timezone.now() - timedelta(minutes=10)
        ).values('service_type').annotate(
            latest_status=Max('status'),
            latest_time=Max('checked_at')
        )
        
        # 获取活跃告警数量
        active_alerts = AlertRecord.objects.filter(
            status__in=['triggered', 'acknowledged']
        ).count()
        
        # 构建概览数据
        overview = {
            'system_status': {
                'cpu_usage': latest_system.cpu_usage if latest_system else 0,
                'memory_usage': latest_system.memory_usage if latest_system else 0,
                'disk_usage': latest_system.disk_usage if latest_system else 0,
                'last_updated': latest_system.collected_at.isoformat() if latest_system else None
            },
            'application_status': {
                'total_requests': latest_app.total_requests if latest_app else 0,
                'error_rate': latest_app.error_rate if latest_app else 0,
                'avg_response_time': latest_app.avg_response_time if latest_app else 0,
                'active_users': latest_app.active_users if latest_app else 0,
                'last_updated': latest_app.collected_at.isoformat() if latest_app else None
            },
            'service_health': {
                service['service_type']: {
                    'status': service['latest_status'],
                    'last_checked': service['latest_time'].isoformat()
                }
                for service in recent_health_checks
            },
            'alerts': {
                'active_count': active_alerts,
                'last_updated': timezone.now().isoformat()
            }
        }
        
        return ApiResponse.success(data=overview, message="获取系统概览成功")
        
    except Exception as e:
        logger.error(f"获取系统概览异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取系统概览失败")


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['monitoring:view'])
def get_system_metrics(request):
    """
    获取系统性能指标历史数据
    """
    try:
        # 获取查询参数
        hours = int(request.GET.get('hours', 24))  # 默认24小时
        metric_type = request.GET.get('metric_type', 'all')  # cpu, memory, disk, all
        
        # 计算时间范围
        end_time = timezone.now()
        start_time = end_time - timedelta(hours=hours)
        
        # 查询数据
        queryset = SystemMetrics.objects.filter(
            collected_at__range=[start_time, end_time]
        ).order_by('collected_at')
        
        # 根据时间范围决定采样间隔
        if hours <= 6:
            # 6小时内，每分钟一个点
            interval = 1
        elif hours <= 24:
            # 24小时内，每5分钟一个点
            interval = 5
        else:
            # 超过24小时，每15分钟一个点
            interval = 15
        
        # 采样数据
        sampled_data = []
        last_time = None
        
        for metric in queryset:
            if last_time is None or (metric.collected_at - last_time).total_seconds() >= interval * 60:
                data_point = {
                    'timestamp': metric.collected_at.isoformat(),
                    'cpu_usage': metric.cpu_usage,
                    'memory_usage': metric.memory_usage,
                    'disk_usage': metric.disk_usage,
                }
                
                if metric_type == 'all' or metric_type == 'cpu':
                    data_point['cpu_usage'] = metric.cpu_usage
                if metric_type == 'all' or metric_type == 'memory':
                    data_point['memory_usage'] = metric.memory_usage
                if metric_type == 'all' or metric_type == 'disk':
                    data_point['disk_usage'] = metric.disk_usage
                
                sampled_data.append(data_point)
                last_time = metric.collected_at
        
        # 计算统计信息
        stats = queryset.aggregate(
            avg_cpu=Avg('cpu_usage'),
            max_cpu=Max('cpu_usage'),
            min_cpu=Min('cpu_usage'),
            avg_memory=Avg('memory_usage'),
            max_memory=Max('memory_usage'),
            min_memory=Min('memory_usage'),
            avg_disk=Avg('disk_usage'),
            max_disk=Max('disk_usage'),
            min_disk=Min('disk_usage'),
        )
        
        return ApiResponse.success(
            data={
                'metrics': sampled_data,
                'statistics': stats,
                'time_range': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat(),
                    'hours': hours
                }
            },
            message="获取系统指标成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统指标异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取系统指标失败")


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['monitoring:view'])
def get_application_metrics(request):
    """
    获取应用性能指标历史数据
    """
    try:
        # 获取查询参数
        hours = int(request.GET.get('hours', 24))
        
        # 计算时间范围
        end_time = timezone.now()
        start_time = end_time - timedelta(hours=hours)
        
        # 查询数据
        queryset = ApplicationMetrics.objects.filter(
            collected_at__range=[start_time, end_time]
        ).order_by('collected_at')
        
        # 构建响应数据
        metrics_data = []
        for metric in queryset:
            metrics_data.append({
                'timestamp': metric.collected_at.isoformat(),
                'total_requests': metric.total_requests,
                'successful_requests': metric.successful_requests,
                'failed_requests': metric.failed_requests,
                'error_rate': metric.error_rate,
                'avg_response_time': metric.avg_response_time,
                'p95_response_time': metric.p95_response_time,
                'p99_response_time': metric.p99_response_time,
                'active_users': metric.active_users,
                'db_connections': metric.db_connections,
                'cache_hit_rate': metric.cache_hit_rate,
                'queue_pending_tasks': metric.queue_pending_tasks,
                'queue_failed_tasks': metric.queue_failed_tasks,
            })
        
        # 计算统计信息
        stats = queryset.aggregate(
            total_requests=Max('total_requests'),
            avg_error_rate=Avg('error_rate'),
            avg_response_time=Avg('avg_response_time'),
            max_response_time=Max('max_response_time'),
            avg_active_users=Avg('active_users'),
        )
        
        return ApiResponse.success(
            data={
                'metrics': metrics_data,
                'statistics': stats,
                'time_range': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat(),
                    'hours': hours
                }
            },
            message="获取应用指标成功"
        )
        
    except Exception as e:
        logger.error(f"获取应用指标异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取应用指标失败")


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['monitoring:view'])
def get_service_health(request):
    """
    获取服务健康状态
    """
    try:
        # 获取最近的健康检查结果
        recent_checks = ServiceHealthCheck.objects.filter(
            checked_at__gte=timezone.now() - timedelta(hours=1)
        ).order_by('service_name', '-checked_at')
        
        # 按服务分组，获取最新状态
        services = {}
        for check in recent_checks:
            if check.service_name not in services:
                services[check.service_name] = {
                    'service_name': check.service_name,
                    'service_type': check.service_type,
                    'status': check.status,
                    'response_time': check.response_time,
                    'error_message': check.error_message,
                    'details': check.details,
                    'last_checked': check.checked_at.isoformat(),
                }
        
        # 计算整体健康状态
        total_services = len(services)
        healthy_services = sum(1 for s in services.values() if s['status'] == 'healthy')
        overall_status = 'healthy' if healthy_services == total_services else 'warning'
        
        if any(s['status'] == 'critical' for s in services.values()):
            overall_status = 'critical'
        
        return ApiResponse.success(
            data={
                'overall_status': overall_status,
                'total_services': total_services,
                'healthy_services': healthy_services,
                'services': list(services.values()),
                'last_updated': timezone.now().isoformat()
            },
            message="获取服务健康状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取服务健康状态异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取服务健康状态失败")


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['monitoring:manage'])
def run_health_check(request):
    """
    手动执行健康检查
    """
    try:
        # 执行健康检查
        results = health_checker.check_all_services()
        
        # 构建响应数据
        check_results = []
        for result in results:
            check_results.append({
                'service_name': result.service_name,
                'service_type': result.service_type,
                'status': result.status,
                'response_time': result.response_time,
                'error_message': result.error_message,
                'details': result.details,
                'checked_at': result.checked_at.isoformat(),
            })
        
        logger.info(f"用户 {request.user.username} 执行了健康检查")
        
        return ApiResponse.success(
            data={'results': check_results},
            message="健康检查执行完成"
        )
        
    except Exception as e:
        logger.error(f"执行健康检查异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "执行健康检查失败")


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['monitoring:manage'])
def collect_metrics(request):
    """
    手动收集系统指标
    """
    try:
        # 收集系统指标
        system_metrics = system_monitor.collect_system_metrics()
        app_metrics = system_monitor.collect_application_metrics()
        
        logger.info(f"用户 {request.user.username} 手动收集了系统指标")
        
        return ApiResponse.success(
            data={
                'system_metrics': {
                    'cpu_usage': system_metrics.cpu_usage,
                    'memory_usage': system_metrics.memory_usage,
                    'disk_usage': system_metrics.disk_usage,
                    'collected_at': system_metrics.collected_at.isoformat(),
                },
                'application_metrics': {
                    'total_requests': app_metrics.total_requests,
                    'error_rate': app_metrics.error_rate,
                    'avg_response_time': app_metrics.avg_response_time,
                    'active_users': app_metrics.active_users,
                    'collected_at': app_metrics.collected_at.isoformat(),
                }
            },
            message="系统指标收集完成"
        )
        
    except Exception as e:
        logger.error(f"收集系统指标异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "收集系统指标失败")


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['monitoring:view'])
def get_alerts(request):
    """
    获取告警记录
    """
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        status_filter = request.GET.get('status')
        severity_filter = request.GET.get('severity')
        
        # 构建查询条件
        queryset = AlertRecord.objects.select_related('rule', 'acknowledged_by', 'resolved_by')
        
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if severity_filter:
            queryset = queryset.filter(rule__severity=severity_filter)
        
        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        alerts_data = []
        for alert in page_obj:
            alerts_data.append({
                'id': alert.id,
                'rule_name': alert.rule.name,
                'rule_description': alert.rule.description,
                'metric_type': alert.rule.metric_type,
                'severity': alert.rule.severity,
                'status': alert.status,
                'metric_value': alert.metric_value,
                'threshold': alert.rule.threshold,
                'message': alert.message,
                'details': alert.details,
                'triggered_at': alert.triggered_at.isoformat(),
                'acknowledged_at': alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
                'resolved_at': alert.resolved_at.isoformat() if alert.resolved_at else None,
                'acknowledged_by': {
                    'id': alert.acknowledged_by.id,
                    'username': alert.acknowledged_by.username,
                    'nickname': alert.acknowledged_by.nickname,
                } if alert.acknowledged_by else None,
                'resolved_by': {
                    'id': alert.resolved_by.id,
                    'username': alert.resolved_by.username,
                    'nickname': alert.resolved_by.nickname,
                } if alert.resolved_by else None,
                'resolution_notes': alert.resolution_notes,
            })
        
        return ApiResponse.success(
            data={
                'alerts': alerts_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': paginator.count,
                    'total_pages': paginator.num_pages,
                }
            },
            message="获取告警记录成功"
        )
        
    except Exception as e:
        logger.error(f"获取告警记录异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取告警记录失败")
