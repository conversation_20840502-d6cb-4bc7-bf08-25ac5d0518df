<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    :title="title"
    class="department-form-dialog"
    style="width: 600px"
    @update:show="$emit('update:visible', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="部门名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="请输入部门名称"
          @blur="checkDepartmentName"
        />
      </n-form-item>
      
      <n-form-item label="部门编码" path="code">
        <n-input
          v-model:value="formData.code"
          placeholder="请输入部门编码"
          @blur="checkDepartmentCode"
        />
      </n-form-item>
      
      <n-form-item label="上级部门" path="parent">
        <n-tree-select
          v-model:value="formData.parent"
          :options="parentOptions"
          placeholder="请选择上级部门（不选择则为根部门）"
          clearable
          key-field="id"
          label-field="name"
          children-field="children"
          :disabled="!!parentId"
        />
      </n-form-item>
      
      <n-form-item label="排序权重" path="sort_order">
        <n-input-number
          v-model:value="formData.sort_order"
          placeholder="请输入排序权重"
          :min="0"
          :max="9999"
          class="w-full"
        />
      </n-form-item>
      
      <n-form-item label="部门描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入部门描述"
          :rows="3"
        />
      </n-form-item>
      
      <n-form-item label="部门状态" path="is_active">
        <n-switch
          v-model:value="formData.is_active"
          :checked-value="true"
          :unchecked-value="false"
        >
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ mode === 'create' ? '创建' : '更新' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules, type TreeSelectOption } from 'naive-ui'
import { useDepartmentStore } from '@/stores/department'
import { departmentApi } from '@/api/department'
import type { DepartmentTree, DepartmentCreateForm, DepartmentEditForm } from '@/types/department'

// Props
interface Props {
  visible: boolean
  department?: DepartmentTree | null
  mode: 'create' | 'edit'
  parentId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  department: null,
  parentId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const departmentStore = useDepartmentStore()
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)
const availableParents = ref<DepartmentTree[]>([])

// 表单数据
const formData = ref<DepartmentCreateForm>({
  name: '',
  code: '',
  parent: null,
  description: '',
  sort_order: 0,
  is_active: true
})

// 计算属性
const title = computed(() => {
  return props.mode === 'create' ? '新增部门' : '编辑部门'
})

// 转换部门树为树选择器选项
const convertToTreeSelectOptions = (departments: DepartmentTree[]): TreeSelectOption[] => {
  return departments.map(dept => ({
    id: dept.id,
    name: dept.name,
    label: dept.name,
    value: dept.id,
    children: dept.children ? convertToTreeSelectOptions(dept.children) : undefined,
    disabled: props.mode === 'edit' && props.department?.id === dept.id // 编辑时不能选择自己
  }))
}

const parentOptions = computed(() => {
  return convertToTreeSelectOptions(departmentStore.departmentTree)
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度为2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入部门编码', trigger: 'blur' },
    { min: 2, max: 20, message: '部门编码长度为2-20个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '部门编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序权重范围为0-9999', trigger: 'blur' }
  ]
}

// 方法
const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    parent: props.parentId || null,
    description: '',
    sort_order: 0,
    is_active: true
  }
}

const loadDepartmentData = () => {
  if (props.department && props.mode === 'edit') {
    formData.value = {
      name: props.department.name,
      code: props.department.code,
      parent: props.department.parent || null,
      description: props.department.description,
      sort_order: props.department.sort_order,
      is_active: props.department.is_active
    }
  }
}

const checkDepartmentName = async () => {
  if (!formData.value.name) return
  
  try {
    const response = await departmentApi.checkDepartmentNameAvailable(
      formData.value.name,
      formData.value.parent,
      props.mode === 'edit' ? props.department?.id : undefined
    )
    if (!response.data.available) {
      message.error('部门名称已存在')
    }
  } catch (error) {
    console.error('检查部门名称失败:', error)
  }
}

const checkDepartmentCode = async () => {
  if (!formData.value.code) return
  
  try {
    const response = await departmentApi.checkDepartmentCodeAvailable(
      formData.value.code,
      props.mode === 'edit' ? props.department?.id : undefined
    )
    if (!response.data.available) {
      message.error('部门编码已存在')
    }
  } catch (error) {
    console.error('检查部门编码失败:', error)
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    if (props.mode === 'create') {
      await departmentStore.createDepartment(formData.value)
    } else if (props.department) {
      const editData: DepartmentEditForm = {
        name: formData.value.name,
        code: formData.value.code,
        parent: formData.value.parent,
        description: formData.value.description,
        sort_order: formData.value.sort_order,
        is_active: formData.value.is_active
      }
      await departmentStore.updateDepartment(props.department.id, editData)
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (props.mode === 'create') {
        resetForm()
      } else {
        loadDepartmentData()
      }
    })
  }
})

watch(() => props.parentId, (newParentId) => {
  if (props.mode === 'create' && newParentId !== null) {
    formData.value.parent = newParentId
  }
})
</script>

<style scoped>
.department-form-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.w-full {
  width: 100%;
}
</style>
