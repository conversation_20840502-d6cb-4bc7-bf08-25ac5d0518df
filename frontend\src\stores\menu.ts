/**
 * 菜单状态管理
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useAuthStore } from './auth'
import { hasPermission, canAccessRoute } from '@/utils/auth'
import type { RouteRecordRaw } from 'vue-router'

// 菜单项类型定义
export interface MenuItem {
  id: string
  name: string
  title: string
  path: string
  icon?: string
  component?: string
  redirect?: string
  hidden?: boolean
  children?: MenuItem[]
  meta?: {
    requiresAuth?: boolean
    permissions?: string[]
    roles?: string[]
    title?: string
    icon?: string
    hidden?: boolean
    noCache?: boolean
  }
}

export const useMenuStore = defineStore('menu', () => {
  // 状态
  const menuList = ref<MenuItem[]>([])
  const collapsed = ref(false)
  const activeMenu = ref('')
  const openKeys = ref<string[]>([])

  // 计算属性
  const visibleMenus = computed(() => {
    return filterMenusByPermission(menuList.value)
  })

  const breadcrumbs = computed(() => {
    return generateBreadcrumbs(activeMenu.value, menuList.value)
  })

  // 根据权限过滤菜单
  function filterMenusByPermission(menus: MenuItem[]): MenuItem[] {
    const authStore = useAuthStore()
    
    if (!authStore.isLoggedIn) {
      return []
    }

    return menus.filter(menu => {
      // 检查菜单是否隐藏
      if (menu.hidden || menu.meta?.hidden) {
        return false
      }

      // 检查权限
      if (menu.meta?.permissions) {
        if (!hasPermission(menu.meta.permissions)) {
          return false
        }
      }

      // 递归过滤子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = filterMenusByPermission(menu.children)
        // 如果所有子菜单都被过滤掉，则隐藏父菜单
        return menu.children.length > 0
      }

      return true
    })
  }

  // 生成面包屑导航
  function generateBreadcrumbs(path: string, menus: MenuItem[]): MenuItem[] {
    const breadcrumbs: MenuItem[] = []
    
    function findPath(menus: MenuItem[], targetPath: string, parents: MenuItem[] = []): boolean {
      for (const menu of menus) {
        const currentPath = [...parents, menu]
        
        if (menu.path === targetPath) {
          breadcrumbs.push(...currentPath)
          return true
        }
        
        if (menu.children && menu.children.length > 0) {
          if (findPath(menu.children, targetPath, currentPath)) {
            return true
          }
        }
      }
      return false
    }
    
    findPath(menus, path)
    return breadcrumbs
  }

  // 从路由配置生成菜单
  function generateMenuFromRoutes(routes: RouteRecordRaw[]): MenuItem[] {
    const menus: MenuItem[] = []

    routes.forEach(route => {
      // 跳过不需要显示在菜单中的路由
      if (route.meta?.hidden || route.path.includes('error') || route.path === '/login') {
        return
      }

      const menuItem: MenuItem = {
        id: route.name as string || route.path,
        name: route.name as string || '',
        title: route.meta?.title || route.path,
        path: route.path,
        icon: route.meta?.icon,
        component: route.component?.toString(),
        redirect: route.redirect as string,
        hidden: route.meta?.hidden,
        meta: route.meta,
        children: []
      }

      // 处理子路由
      if (route.children && route.children.length > 0) {
        menuItem.children = generateMenuFromRoutes(route.children)
      }

      menus.push(menuItem)
    })

    return menus
  }

  // 设置菜单列表
  function setMenuList(menus: MenuItem[]) {
    menuList.value = menus
  }

  // 切换菜单折叠状态
  function toggleCollapsed() {
    collapsed.value = !collapsed.value
  }

  // 设置折叠状态
  function setCollapsed(value: boolean) {
    collapsed.value = value
  }

  // 设置当前激活菜单
  function setActiveMenu(path: string) {
    activeMenu.value = path
    
    // 自动展开父级菜单
    const keys = generateOpenKeys(path, menuList.value)
    openKeys.value = keys
  }

  // 生成需要展开的菜单键
  function generateOpenKeys(path: string, menus: MenuItem[], parentKeys: string[] = []): string[] {
    for (const menu of menus) {
      const currentKeys = [...parentKeys, menu.id]
      
      if (menu.path === path) {
        return parentKeys
      }
      
      if (menu.children && menu.children.length > 0) {
        const result = generateOpenKeys(path, menu.children, currentKeys)
        if (result.length > 0) {
          return result
        }
      }
    }
    return []
  }

  // 设置展开的菜单键
  function setOpenKeys(keys: string[]) {
    openKeys.value = keys
  }

  // 查找菜单项
  function findMenuItem(path: string, menus: MenuItem[] = menuList.value): MenuItem | null {
    for (const menu of menus) {
      if (menu.path === path) {
        return menu
      }
      
      if (menu.children && menu.children.length > 0) {
        const found = findMenuItem(path, menu.children)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  // 获取所有菜单路径
  function getAllMenuPaths(menus: MenuItem[] = menuList.value): string[] {
    const paths: string[] = []
    
    function collectPaths(menus: MenuItem[]) {
      menus.forEach(menu => {
        if (menu.path && !menu.redirect) {
          paths.push(menu.path)
        }
        
        if (menu.children && menu.children.length > 0) {
          collectPaths(menu.children)
        }
      })
    }
    
    collectPaths(menus)
    return paths
  }

  // 重置菜单状态
  function resetMenuState() {
    menuList.value = []
    activeMenu.value = ''
    openKeys.value = []
  }

  return {
    // 状态
    menuList,
    collapsed,
    activeMenu,
    openKeys,
    
    // 计算属性
    visibleMenus,
    breadcrumbs,
    
    // 方法
    setMenuList,
    toggleCollapsed,
    setCollapsed,
    setActiveMenu,
    setOpenKeys,
    findMenuItem,
    getAllMenuPaths,
    generateMenuFromRoutes,
    resetMenuState
  }
})
