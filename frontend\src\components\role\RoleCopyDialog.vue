<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="复制角色"
    class="role-copy-dialog"
    style="width: 600px"
    @update:show="$emit('update:visible', $event)"
  >
    <div class="role-copy-content">
      <!-- 源角色信息 -->
      <div v-if="sourceRole" class="source-role-info">
        <h4>源角色信息</h4>
        <div class="source-role-card">
          <div class="role-basic">
            <div class="role-name">{{ sourceRole.name }}</div>
            <n-tag type="info" size="small">{{ sourceRole.code }}</n-tag>
            <n-tag :type="getDataScopeColor(sourceRole.data_scope)" size="small">
              {{ getDataScopeLabel(sourceRole.data_scope) }}
            </n-tag>
          </div>
          <div class="role-stats">
            <span class="stat-item">关联用户: {{ sourceRole.user_count }}人</span>
            <span class="stat-item">状态: {{ sourceRole.is_active ? '正常' : '禁用' }}</span>
          </div>
          <div v-if="sourceRole.description" class="role-description">
            {{ sourceRole.description }}
          </div>
        </div>
      </div>
      
      <!-- 新角色表单 -->
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="角色名称" path="name">
          <n-input
            v-model:value="formData.name"
            placeholder="请输入新角色名称"
            @blur="checkRoleName"
          />
        </n-form-item>
        
        <n-form-item label="角色编码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入新角色编码"
            @blur="checkRoleCode"
          />
        </n-form-item>
        
        <n-form-item label="角色描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入角色描述"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item label="复制选项" path="copy_permissions">
          <n-space vertical>
            <n-checkbox
              v-model:checked="formData.copy_permissions"
              label="复制权限配置"
            />
            <n-text depth="3" style="font-size: 12px">
              选中后将复制源角色的所有权限配置到新角色
            </n-text>
          </n-space>
        </n-form-item>
      </n-form>
      
      <!-- 权限预览 -->
      <div v-if="formData.copy_permissions && sourceRolePermissions.length > 0" class="permissions-preview">
        <h4>将要复制的权限 ({{ sourceRolePermissions.length }})</h4>
        <div class="permission-stats">
          <div class="stat-item">
            <span class="stat-label">菜单权限:</span>
            <span class="stat-value">{{ getPermissionCountByType('MENU') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">按钮权限:</span>
            <span class="stat-value">{{ getPermissionCountByType('BUTTON') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">API权限:</span>
            <span class="stat-value">{{ getPermissionCountByType('API') }}</span>
          </div>
        </div>
        
        <div class="permission-list">
          <n-scrollbar style="max-height: 200px">
            <div class="permission-items">
              <div
                v-for="permission in sourceRolePermissions"
                :key="permission.id"
                class="permission-item"
              >
                <n-icon
                  v-if="permission.icon"
                  :component="getPermissionIcon(permission.icon)"
                  class="permission-icon"
                />
                <span class="permission-name">{{ permission.name }}</span>
                <n-tag
                  :type="getPermissionTypeColor(permission.permission_type)"
                  size="small"
                  class="permission-type"
                >
                  {{ getPermissionTypeLabel(permission.permission_type) }}
                </n-tag>
                <span v-if="permission.code" class="permission-code">{{ permission.code }}</span>
              </div>
            </div>
          </n-scrollbar>
        </div>
      </div>
    </div>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          创建角色
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { SearchIcon } from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { roleApi } from '@/api/role'
import type { Role, RoleCopyForm, Permission, PermissionType, DataScope } from '@/types/role'

// Props
interface Props {
  visible: boolean
  sourceRole?: Role | null
}

const props = withDefaults(defineProps<Props>(), {
  sourceRole: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)
const sourceRolePermissions = ref<Permission[]>([])

// 表单数据
const formData = ref<RoleCopyForm>({
  source_role_id: 0,
  name: '',
  code: '',
  description: '',
  copy_permissions: true
})

// 数据范围标签映射
const dataScopeLabels: Record<DataScope, string> = {
  'ALL': '全部数据',
  'DEPT_AND_SUB': '本部门及子部门',
  'DEPT_ONLY': '仅本部门',
  'SELF_ONLY': '仅本人',
  'CUSTOM': '自定义'
}

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度为2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 20, message: '角色编码长度为2-20个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '角色编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ]
}

// 方法
const getDataScopeLabel = (dataScope: DataScope) => {
  return dataScopeLabels[dataScope] || dataScope
}

const getDataScopeColor = (dataScope: DataScope) => {
  switch (dataScope) {
    case 'ALL':
      return 'error'
    case 'DEPT_AND_SUB':
      return 'warning'
    case 'DEPT_ONLY':
      return 'info'
    case 'SELF_ONLY':
      return 'success'
    case 'CUSTOM':
      return 'default'
    default:
      return 'default'
  }
}

const getPermissionCountByType = (type: PermissionType) => {
  return sourceRolePermissions.value.filter(p => p.permission_type === type).length
}

const getPermissionIcon = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  return SearchIcon // 临时使用搜索图标
}

const getPermissionTypeColor = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return 'info'
    case 'BUTTON':
      return 'warning'
    case 'API':
      return 'success'
    default:
      return 'default'
  }
}

const getPermissionTypeLabel = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return '菜单'
    case 'BUTTON':
      return '按钮'
    case 'API':
      return 'API'
    default:
      return type
  }
}

const resetForm = () => {
  formData.value = {
    source_role_id: props.sourceRole?.id || 0,
    name: '',
    code: '',
    description: '',
    copy_permissions: true
  }
  sourceRolePermissions.value = []
}

const loadSourceRoleData = async () => {
  if (!props.sourceRole) return
  
  // 设置表单默认值
  formData.value.source_role_id = props.sourceRole.id
  formData.value.name = `${props.sourceRole.name}_副本`
  formData.value.code = `${props.sourceRole.code}_COPY`
  formData.value.description = props.sourceRole.description
  
  // 获取源角色权限
  try {
    const response = await roleApi.getRolePermissions(props.sourceRole.id)
    if (response.code === 200) {
      sourceRolePermissions.value = response.data
    }
  } catch (error) {
    console.error('获取源角色权限失败:', error)
  }
}

const checkRoleName = async () => {
  if (!formData.value.name) return
  
  try {
    const response = await roleApi.checkRoleNameAvailable(formData.value.name)
    if (!response.data.available) {
      message.error('角色名称已存在')
    }
  } catch (error) {
    console.error('检查角色名称失败:', error)
  }
}

const checkRoleCode = async () => {
  if (!formData.value.code) return
  
  try {
    const response = await roleApi.checkRoleCodeAvailable(formData.value.code)
    if (!response.data.available) {
      message.error('角色编码已存在')
    }
  } catch (error) {
    console.error('检查角色编码失败:', error)
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    await roleApi.copyRole(formData.value)
    
    emit('success')
  } catch (error) {
    console.error('复制角色失败:', error)
    message.error('复制角色失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (props.sourceRole) {
        loadSourceRoleData()
      } else {
        resetForm()
      }
    })
  }
})

watch(() => props.sourceRole, (newSourceRole) => {
  if (props.visible && newSourceRole) {
    loadSourceRoleData()
  }
})
</script>

<style scoped>
.role-copy-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.role-copy-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.source-role-info h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.source-role-card {
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.role-basic {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.role-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.role-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.stat-item {
  font-size: 14px;
  color: #6b7280;
}

.role-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.permissions-preview h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.permission-stats {
  display: flex;
  gap: 24px;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
  margin-bottom: 12px;
}

.permission-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.permission-list {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.permission-items {
  padding: 8px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.permission-item:hover {
  background: #f3f4f6;
}

.permission-icon {
  flex-shrink: 0;
  color: #6b7280;
}

.permission-name {
  flex: 1;
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.permission-type {
  flex-shrink: 0;
}

.permission-code {
  flex-shrink: 0;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
