"""
会话管理相关视图
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Q, Count
from django.core.paginator import Paginator
import logging

from .models import UserSession, SecurityAlert, DeviceFingerprint
from .anomaly_detector import anomaly_detector
from apps.common.response import ApiResponse
from apps.common.exceptions import BusinessException, ErrorCode
from apps.permissions.decorators import require_permissions

logger = logging.getLogger(__name__)
User = get_user_model()

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_sessions(request):
    """
    获取用户会话列表
    """
    try:
        user_id = request.GET.get('user_id')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        # 如果不是管理员，只能查看自己的会话
        if not request.user.has_perm('authentication.view_all_sessions'):
            user_id = request.user.id
        
        # 构建查询条件
        queryset = UserSession.objects.select_related('user')
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # 按最后活动时间排序
        queryset = queryset.order_by('-last_activity')
        
        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        sessions_data = []
        for session in page_obj:
            sessions_data.append({
                'id': session.id,
                'session_key': session.session_key,
                'user': {
                    'id': session.user.id,
                    'username': session.user.username,
                    'nickname': session.user.nickname,
                },
                'ip_address': session.ip_address,
                'device_type': session.device_type,
                'browser': session.browser,
                'os': session.os,
                'location': {
                    'country': session.country,
                    'region': session.region,
                    'city': session.city,
                },
                'is_active': session.is_active,
                'last_activity': session.last_activity.isoformat(),
                'expires_at': session.expires_at.isoformat(),
                'created_at': session.created_at.isoformat(),
            })
        
        return ApiResponse.success(
            data={
                'sessions': sessions_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': paginator.count,
                    'total_pages': paginator.num_pages,
                }
            },
            message="获取会话列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取会话列表异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取会话列表失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['authentication.manage_sessions'])
def terminate_session(request):
    """
    强制终止会话
    """
    try:
        session_id = request.data.get('session_id')
        if not session_id:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请提供会话ID")
        
        session = UserSession.objects.get(id=session_id)
        
        # 检查权限：只能终止自己的会话或管理员可以终止任何会话
        if session.user_id != request.user.id and not request.user.has_perm('authentication.manage_all_sessions'):
            raise BusinessException(ErrorCode.PERMISSION_DENIED, "无权终止此会话")
        
        # 终止会话
        session.is_active = False
        session.save(update_fields=['is_active'])
        
        # 记录操作日志
        logger.info(f"用户 {request.user.username} 终止了会话: {session.session_key}")
        
        return ApiResponse.success(message="会话已终止")
        
    except UserSession.DoesNotExist:
        raise BusinessException(ErrorCode.VALIDATION_ERROR, "会话不存在")
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"终止会话异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "终止会话失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['authentication.manage_sessions'])
def terminate_user_sessions(request):
    """
    终止用户的所有会话
    """
    try:
        user_id = request.data.get('user_id')
        exclude_current = request.data.get('exclude_current', True)
        
        if not user_id:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请提供用户ID")
        
        # 检查权限
        if user_id != request.user.id and not request.user.has_perm('authentication.manage_all_sessions'):
            raise BusinessException(ErrorCode.PERMISSION_DENIED, "无权管理此用户的会话")
        
        # 构建查询条件
        queryset = UserSession.objects.filter(user_id=user_id, is_active=True)
        
        # 是否排除当前会话
        if exclude_current and hasattr(request, 'session'):
            queryset = queryset.exclude(session_key=request.session.session_key)
        
        # 批量终止会话
        terminated_count = queryset.update(is_active=False)
        
        logger.info(f"用户 {request.user.username} 终止了用户 {user_id} 的 {terminated_count} 个会话")
        
        return ApiResponse.success(
            data={'terminated_count': terminated_count},
            message=f"已终止 {terminated_count} 个会话"
        )
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"批量终止会话异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "批量终止会话失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_session_statistics(request):
    """
    获取会话统计信息
    """
    try:
        # 总会话数
        total_sessions = UserSession.objects.count()
        
        # 活跃会话数
        active_sessions = UserSession.objects.filter(is_active=True).count()
        
        # 今日新增会话
        today_sessions = UserSession.objects.filter(
            created_at__date=timezone.now().date()
        ).count()
        
        # 在线用户数
        online_users = UserSession.objects.filter(
            is_active=True,
            last_activity__gte=timezone.now() - timezone.timedelta(minutes=30)
        ).values('user').distinct().count()
        
        # 设备类型分布
        device_stats = UserSession.objects.filter(is_active=True).values('device_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 浏览器分布
        browser_stats = UserSession.objects.filter(is_active=True).values('browser').annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        # 地理位置分布
        location_stats = UserSession.objects.filter(
            is_active=True,
            country__isnull=False
        ).values('country').annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        return ApiResponse.success(
            data={
                'overview': {
                    'total_sessions': total_sessions,
                    'active_sessions': active_sessions,
                    'today_sessions': today_sessions,
                    'online_users': online_users,
                },
                'device_distribution': list(device_stats),
                'browser_distribution': list(browser_stats),
                'location_distribution': list(location_stats),
            },
            message="获取会话统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取会话统计异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取会话统计失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cleanup_expired_sessions(request):
    """
    清理过期会话
    """
    try:
        # 检查权限
        if not request.user.has_perm('authentication.manage_sessions'):
            raise BusinessException(ErrorCode.PERMISSION_DENIED, "无权执行此操作")
        
        # 清理过期会话
        expired_count = UserSession.objects.filter(
            Q(expires_at__lt=timezone.now()) | Q(is_active=False)
        ).delete()[0]
        
        logger.info(f"用户 {request.user.username} 清理了 {expired_count} 个过期会话")
        
        return ApiResponse.success(
            data={'cleaned_count': expired_count},
            message=f"已清理 {expired_count} 个过期会话"
        )
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"清理过期会话异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "清理过期会话失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@require_permissions(['authentication.view_security_alerts'])
def get_security_alerts(request):
    """
    获取安全告警列表
    """
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        status_filter = request.GET.get('status')
        severity_filter = request.GET.get('severity')
        alert_type_filter = request.GET.get('alert_type')
        
        # 构建查询条件
        queryset = SecurityAlert.objects.select_related('user', 'assigned_to', 'resolved_by')
        
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if severity_filter:
            queryset = queryset.filter(severity=severity_filter)
        if alert_type_filter:
            queryset = queryset.filter(alert_type=alert_type_filter)
        
        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        alerts_data = []
        for alert in page_obj:
            alerts_data.append({
                'id': alert.id,
                'alert_id': str(alert.alert_id),
                'alert_type': alert.alert_type,
                'alert_type_display': alert.get_alert_type_display(),
                'severity': alert.severity,
                'severity_display': alert.get_severity_display(),
                'status': alert.status,
                'status_display': alert.get_status_display(),
                'title': alert.title,
                'description': alert.description,
                'risk_score': alert.risk_score,
                'user': {
                    'id': alert.user.id,
                    'username': alert.user.username,
                    'nickname': alert.user.nickname,
                } if alert.user else None,
                'ip_address': alert.ip_address,
                'detection_details': alert.detection_details,
                'recommendations': alert.recommendations,
                'assigned_to': {
                    'id': alert.assigned_to.id,
                    'username': alert.assigned_to.username,
                    'nickname': alert.assigned_to.nickname,
                } if alert.assigned_to else None,
                'created_at': alert.created_at.isoformat(),
                'resolved_at': alert.resolved_at.isoformat() if alert.resolved_at else None,
            })
        
        return ApiResponse.success(
            data={
                'alerts': alerts_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': paginator.count,
                    'total_pages': paginator.num_pages,
                }
            },
            message="获取安全告警成功"
        )
        
    except Exception as e:
        logger.error(f"获取安全告警异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取安全告警失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@require_permissions(['authentication.manage_security_alerts'])
def update_alert_status(request):
    """
    更新告警状态
    """
    try:
        alert_id = request.data.get('alert_id')
        new_status = request.data.get('status')
        resolution_notes = request.data.get('resolution_notes', '')
        
        if not alert_id or not new_status:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请提供告警ID和状态")
        
        alert = SecurityAlert.objects.get(alert_id=alert_id)
        
        # 更新状态
        alert.status = new_status
        if new_status == 'resolved':
            alert.resolved_by = request.user
            alert.resolved_at = timezone.now()
            alert.resolution_notes = resolution_notes
        
        alert.save()
        
        logger.info(f"用户 {request.user.username} 更新告警状态: {alert_id} -> {new_status}")
        
        return ApiResponse.success(message="告警状态更新成功")
        
    except SecurityAlert.DoesNotExist:
        raise BusinessException(ErrorCode.VALIDATION_ERROR, "告警不存在")
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"更新告警状态异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "更新告警状态失败")
