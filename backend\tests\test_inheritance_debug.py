"""
权限继承调试测试
"""
import pytest
from django.test import TestCase
from apps.permissions.models import Permission, Role, UserRole
from apps.permissions.services import PermissionService
from apps.users.models import UserDepartment
from tests.factories import UserProfileFactory, DepartmentFactory, RoleFactory, PermissionFactory


@pytest.mark.django_db
class TestInheritanceDebug(TestCase):
    """权限继承调试"""
    
    def test_inheritance_debug(self):
        """调试权限继承问题"""
        # 创建用户和基础数据
        user = UserProfileFactory()
        department = DepartmentFactory()
        role = RoleFactory()
        
        # 建立用户部门关系
        UserDepartment.objects.create(
            user=user, department=department, is_primary=True, is_active=True
        )
        
        # 建立用户角色关系
        UserRole.objects.create(user=user, role=role, department=department)
        
        # 创建父子权限
        parent = PermissionFactory(code='system:admin', name='系统管理')
        child = PermissionFactory(code='system:user', name='用户管理')
        child.parent = parent
        child.save()
        
        print(f"父权限: {parent.code} (ID: {parent.id})")
        print(f"子权限: {child.code} (ID: {child.id})")
        print(f"子权限的父权限: {child.parent} (ID: {child.parent.id if child.parent else None})")
        
        # 角色分配父权限
        role.permissions.add(parent)
        
        # 获取用户权限
        user_permissions = PermissionService.get_user_permissions(user)
        print(f"用户权限: {[p.code for p in user_permissions]}")
        
        # 测试权限继承逻辑
        target_permission = Permission.objects.filter(code='system:user').first()
        print(f"目标权限: {target_permission}")
        
        if target_permission:
            ancestors = target_permission.get_ancestors()
            print(f"目标权限的祖先: {[a.code for a in ancestors]}")
            
            for perm in user_permissions:
                print(f"检查用户权限 {perm.code} 是否在祖先中")
                if perm in ancestors:
                    print(f"  找到匹配: {perm.code}")
                else:
                    print(f"  未匹配: {perm.code}")
        
        # 清除缓存
        from django.core.cache import cache
        cache.clear()

        # 测试权限检查
        print("\n=== 测试父权限检查 ===")
        has_parent = PermissionService.check_user_permission(user, 'system:admin')
        print(f"父权限检查结果: {has_parent}")

        print("\n=== 测试子权限检查 ===")

        # 手动复制权限检查逻辑进行调试
        permission_code = 'system:user'
        print(f"手动检查权限: {permission_code}")

        permissions = PermissionService.get_user_permissions(user)
        permission_codes = [p.code if hasattr(p, 'code') else p for p in permissions]
        print(f"用户拥有的权限: {permission_codes}")

        # 检查直接权限
        if permission_code in permission_codes:
            print(f"找到直接权限: {permission_code}")
            manual_result = True
        else:
            # 检查权限继承
            target_permission = Permission.objects.filter(code=permission_code).first()
            print(f"目标权限对象: {target_permission}")

            manual_result = False
            if target_permission:
                ancestors = target_permission.get_ancestors()
                ancestor_ids = [a.id for a in ancestors]
                print(f"祖先权限ID: {ancestor_ids}")

                for perm in permissions:
                    print(f"检查权限 {perm.code} (ID: {perm.id}) 是否在祖先中")
                    if perm.id in ancestor_ids:
                        print(f"找到继承权限: {perm.code} -> {permission_code}")
                        manual_result = True
                        break

        print(f"手动检查结果: {manual_result}")

        # 重新导入模块以确保使用最新版本
        import importlib
        from apps.permissions import services
        importlib.reload(services)
        from apps.permissions.services import PermissionService as ReloadedPermissionService

        has_child = ReloadedPermissionService.check_user_permission(user, 'system:user')
        print(f"重新加载后的服务方法检查结果: {has_child}")

        # 也测试原始导入
        has_child_original = PermissionService.check_user_permission(user, 'system:user')
        print(f"原始服务方法检查结果: {has_child_original}")
        
        print(f"有父权限: {has_parent}")
        print(f"有子权限: {has_child_original}")

        self.assertTrue(has_parent)
        self.assertTrue(has_child_original or has_child)
