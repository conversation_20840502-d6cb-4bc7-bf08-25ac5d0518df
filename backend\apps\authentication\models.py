"""
认证模块 - 会话管理模型
"""
from django.db import models
from django.utils import timezone
from apps.common.models import BaseModel
import random
import string
import uuid
import hashlib


class UserSession(BaseModel):
    """用户会话管理 - 控制并发登录"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    session_key = models.CharField(max_length=40, unique=True, verbose_name="会话密钥")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(verbose_name="用户代理")
    
    # 会话状态
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    last_activity = models.DateTimeField(auto_now=True, verbose_name="最后活动时间")
    expires_at = models.DateTimeField(verbose_name="过期时间")
    
    # 设备信息
    device_type = models.CharField(max_length=50, blank=True, verbose_name="设备类型")
    browser = models.CharField(max_length=100, blank=True, verbose_name="浏览器")
    os = models.CharField(max_length=100, blank=True, verbose_name="操作系统")
    device_fingerprint = models.CharField(max_length=64, blank=True, verbose_name="设备指纹")

    # 地理位置信息
    country = models.CharField(max_length=100, blank=True, verbose_name="国家")
    region = models.CharField(max_length=100, blank=True, verbose_name="地区")
    city = models.CharField(max_length=100, blank=True, verbose_name="城市")
    latitude = models.FloatField(null=True, blank=True, verbose_name="纬度")
    longitude = models.FloatField(null=True, blank=True, verbose_name="经度")
    
    class Meta:
        db_table = 'auth_user_session'
        verbose_name = "用户会话"
        verbose_name_plural = "用户会话"
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.nickname} - {self.ip_address}"


class SimpleCaptcha(BaseModel):
    """简单数学验证码"""
    key = models.CharField(max_length=40, unique=True, verbose_name="验证码密钥")
    question = models.CharField(max_length=50, verbose_name="验证码问题")
    answer = models.CharField(max_length=10, verbose_name="验证码答案")
    expires_at = models.DateTimeField(verbose_name="过期时间")

    class Meta:
        db_table = 'auth_simple_captcha'
        verbose_name = "验证码"
        verbose_name_plural = "验证码"
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.question} = ?"

    @classmethod
    def generate_captcha(cls):
        """生成数学验证码"""
        # 生成简单的加法题
        num1 = random.randint(1, 20)
        num2 = random.randint(1, 20)
        question = f"{num1} + {num2}"
        answer = str(num1 + num2)

        # 生成唯一密钥
        key = ''.join(random.choices(string.ascii_lowercase + string.digits, k=40))

        # 设置5分钟过期
        expires_at = timezone.now() + timezone.timedelta(minutes=5)

        # 创建验证码记录
        captcha = cls.objects.create(
            key=key,
            question=question,
            answer=answer,
            expires_at=expires_at
        )

        return captcha

    @classmethod
    def verify_captcha(cls, key, answer):
        """验证验证码"""
        try:
            captcha = cls.objects.get(
                key=key,
                expires_at__gt=timezone.now()
            )
            if captcha.answer == str(answer).strip():
                # 验证成功后删除验证码
                captcha.delete()
                return True
            return False
        except cls.DoesNotExist:
            return False

    @classmethod
    def cleanup_expired(cls):
        """清理过期验证码"""
        return cls.objects.filter(expires_at__lt=timezone.now()).delete()


class IPWhitelist(BaseModel):
    """IP白名单"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    description = models.CharField(max_length=200, blank=True, verbose_name="描述")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name="过期时间")

    class Meta:
        db_table = 'auth_ip_whitelist'
        verbose_name = "IP白名单"
        verbose_name_plural = "IP白名单"
        unique_together = ['user', 'ip_address']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['ip_address']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.ip_address}"

    def is_valid(self):
        """检查IP白名单是否有效"""
        if not self.is_active:
            return False
        if self.expires_at and self.expires_at < timezone.now():
            return False
        return True


class LoginAttempt(BaseModel):
    """登录尝试记录"""
    username = models.CharField(max_length=150, verbose_name="用户名")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(verbose_name="用户代理")
    is_successful = models.BooleanField(verbose_name="是否成功")
    failure_reason = models.CharField(max_length=100, blank=True, verbose_name="失败原因")

    # 设备指纹信息
    device_fingerprint = models.CharField(max_length=64, blank=True, verbose_name="设备指纹")
    browser_fingerprint = models.TextField(blank=True, verbose_name="浏览器指纹")

    class Meta:
        db_table = 'auth_login_attempt'
        verbose_name = "登录尝试"
        verbose_name_plural = "登录尝试"
        indexes = [
            models.Index(fields=['username', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['is_successful', 'created_at']),
            models.Index(fields=['device_fingerprint']),
            models.Index(fields=['username', 'is_successful']),
            models.Index(fields=['ip_address', 'is_successful']),
        ]

    def __str__(self):
        status = "成功" if self.is_successful else "失败"
        return f"{self.username} - {self.ip_address} - {status}"


class SecurityAlert(BaseModel):
    """安全告警记录"""
    ALERT_TYPES = [
        ('failed_attempts', '失败登录尝试'),
        ('new_device', '新设备登录'),
        ('new_ip', '新IP地址登录'),
        ('unusual_time', '异常时间登录'),
        ('location_anomaly', '地理位置异常'),
        ('concurrent_sessions', '并发会话异常'),
        ('login_frequency', '登录频率异常'),
        ('brute_force', '暴力破解攻击'),
        ('account_takeover', '账户接管'),
    ]

    SEVERITY_LEVELS = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]

    STATUS_CHOICES = [
        ('open', '待处理'),
        ('investigating', '调查中'),
        ('resolved', '已解决'),
        ('false_positive', '误报'),
    ]

    alert_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name="告警ID")
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, null=True, blank=True, verbose_name="相关用户")
    alert_type = models.CharField(max_length=50, choices=ALERT_TYPES, verbose_name="告警类型")
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, verbose_name="严重程度")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open', verbose_name="处理状态")

    title = models.CharField(max_length=200, verbose_name="告警标题")
    description = models.TextField(verbose_name="告警描述")
    risk_score = models.IntegerField(default=0, verbose_name="风险评分")

    # 相关信息
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="IP地址")
    user_agent = models.TextField(blank=True, verbose_name="用户代理")
    device_fingerprint = models.CharField(max_length=64, blank=True, verbose_name="设备指纹")

    # 检测详情
    detection_details = models.JSONField(default=dict, verbose_name="检测详情")
    recommendations = models.JSONField(default=list, verbose_name="处理建议")

    # 处理信息
    assigned_to = models.ForeignKey('users.UserProfile', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='assigned_alerts', verbose_name="分配给")
    resolved_by = models.ForeignKey('users.UserProfile', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='resolved_alerts', verbose_name="解决人")
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name="解决时间")
    resolution_notes = models.TextField(blank=True, verbose_name="解决备注")

    class Meta:
        db_table = 'auth_security_alert'
        verbose_name = "安全告警"
        verbose_name_plural = "安全告警"
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['alert_type', 'created_at']),
            models.Index(fields=['severity', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_alert_type_display()} - {self.title}"


class DeviceFingerprint(BaseModel):
    """设备指纹记录"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    fingerprint_hash = models.CharField(max_length=64, unique=True, verbose_name="指纹哈希")

    # 设备信息
    user_agent = models.TextField(verbose_name="用户代理")
    screen_resolution = models.CharField(max_length=20, blank=True, verbose_name="屏幕分辨率")
    timezone_offset = models.IntegerField(null=True, blank=True, verbose_name="时区偏移")
    language = models.CharField(max_length=10, blank=True, verbose_name="语言")
    platform = models.CharField(max_length=50, blank=True, verbose_name="平台")

    # 浏览器特征
    browser_name = models.CharField(max_length=50, blank=True, verbose_name="浏览器名称")
    browser_version = models.CharField(max_length=20, blank=True, verbose_name="浏览器版本")
    engine_name = models.CharField(max_length=50, blank=True, verbose_name="引擎名称")
    engine_version = models.CharField(max_length=20, blank=True, verbose_name="引擎版本")

    # 系统信息
    os_name = models.CharField(max_length=50, blank=True, verbose_name="操作系统")
    os_version = models.CharField(max_length=20, blank=True, verbose_name="系统版本")
    device_type = models.CharField(max_length=20, blank=True, verbose_name="设备类型")

    # 网络信息
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    isp = models.CharField(max_length=100, blank=True, verbose_name="网络服务商")

    # 状态信息
    is_trusted = models.BooleanField(default=False, verbose_name="是否可信")
    trust_score = models.IntegerField(default=0, verbose_name="信任评分")
    last_seen = models.DateTimeField(auto_now=True, verbose_name="最后见到时间")
    login_count = models.IntegerField(default=1, verbose_name="登录次数")

    class Meta:
        db_table = 'auth_device_fingerprint'
        verbose_name = "设备指纹"
        verbose_name_plural = "设备指纹"
        indexes = [
            models.Index(fields=['user', 'last_seen']),
            models.Index(fields=['fingerprint_hash']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['is_trusted', 'user']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.browser_name} on {self.os_name}"

    @classmethod
    def generate_fingerprint(cls, user_agent, additional_data=None):
        """生成设备指纹"""
        fingerprint_data = user_agent
        if additional_data:
            fingerprint_data += str(additional_data)

        return hashlib.sha256(fingerprint_data.encode()).hexdigest()

    def update_trust_score(self):
        """更新信任评分"""
        # 基于登录次数、时间间隔等因素计算信任评分
        base_score = min(self.login_count * 10, 100)

        # 考虑时间因素
        days_since_first_seen = (timezone.now() - self.created_at).days
        time_factor = min(days_since_first_seen * 2, 50)

        self.trust_score = min(base_score + time_factor, 100)

        # 如果评分超过80，标记为可信
        if self.trust_score >= 80:
            self.is_trusted = True

        self.save(update_fields=['trust_score', 'is_trusted'])