<template>
  <div class="user-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">用户管理</h1>
          <p class="page-description">管理系统用户账户和权限</p>
        </div>

        <!-- 统计卡片 -->
        <div v-if="userStats" class="stats-cards">
          <div class="stat-card">
            <div class="stat-value">{{ userStats.total_users }}</div>
            <div class="stat-label">总用户数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ userStats.active_users }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ userStats.online_users }}</div>
            <div class="stat-label">在线用户</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 操作栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <!-- 搜索框 -->
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索用户名、昵称、邮箱..."
            class="search-input"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
          </n-input>

          <!-- 筛选器 -->
          <n-select
            v-model:value="filterDepartment"
            placeholder="选择部门"
            class="filter-select"
            clearable
            @update:value="handleFilter"
          >
            <n-option
              v-for="dept in departments"
              :key="dept.id"
              :value="dept.id"
              :label="dept.name"
            />
          </n-select>

          <n-select
            v-model:value="filterStatus"
            placeholder="用户状态"
            class="filter-select"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="true" label="已激活" />
            <n-option value="false" label="已禁用" />
          </n-select>
        </div>

        <div class="toolbar-right">
          <!-- 批量操作 -->
          <n-dropdown
            v-if="hasSelectedUsers"
            :options="batchOptions"
            @select="handleBatchOperation"
          >
            <n-button>
              批量操作 ({{ selectedUsersCount }})
              <template #icon>
                <n-icon><ChevronDownIcon /></n-icon>
              </template>
            </n-button>
          </n-dropdown>

          <!-- 导出按钮 -->
          <n-button
            v-permission="'user:export'"
            @click="handleExport"
            :loading="exporting"
          >
            <template #icon>
              <n-icon><DownloadIcon /></n-icon>
            </template>
            导出
          </n-button>

          <!-- 导入按钮 -->
          <n-upload
            v-permission="'user:import'"
            :show-file-list="false"
            accept=".xlsx,.xls,.csv"
            @before-upload="handleImport"
          >
            <n-button>
              <template #icon>
                <n-icon><UploadIcon /></n-icon>
              </template>
              导入
            </n-button>
          </n-upload>

          <!-- 新增用户按钮 -->
          <n-button
            v-permission="'user:create'"
            type="primary"
            @click="handleCreate"
          >
            <template #icon>
              <n-icon><PlusIcon /></n-icon>
            </template>
            新增用户
          </n-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <n-data-table
          :columns="tableColumns"
          :data="userList"
          :loading="loading"
          :pagination="paginationConfig"
          :row-key="getRowKey"
          :checked-row-keys="selectedUserIds"
          @update:checked-row-keys="handleSelectionChange"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
          @update:sorter="handleSort"
          striped
          flex-height
          class="user-table"
        />
      </div>
    </div>

    <!-- 用户表单对话框 -->
    <UserFormDialog
      v-model:visible="formDialogVisible"
      :user="editingUser"
      :mode="formMode"
      @success="handleFormSuccess"
    />

    <!-- 用户详情对话框 -->
    <UserDetailDialog
      v-model:visible="detailDialogVisible"
      :user-id="viewingUserId"
    />

    <!-- 密码重置对话框 -->
    <PasswordResetDialog
      v-model:visible="passwordDialogVisible"
      :user-id="resetPasswordUserId"
      @success="handlePasswordResetSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage, useDialog, type DataTableColumns, type DropdownOption } from 'naive-ui'
import {
  SearchIcon,
  PlusIcon,
  DownloadIcon,
  UploadIcon,
  ChevronDownIcon,
  EditIcon,
  DeleteIcon,
  EyeIcon,
  KeyIcon,
  CheckIcon,
  CloseIcon
} from '@vicons/tabler'
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import { hasPermission } from '@/utils/auth'
import type { UserListItem, UserSearchParams } from '@/types/user'
import UserFormDialog from '@/components/user/UserFormDialog.vue'
import UserDetailDialog from '@/components/user/UserDetailDialog.vue'
import PasswordResetDialog from '@/components/user/PasswordResetDialog.vue'

// 状态管理
const userStore = useUserStore()
const authStore = useAuthStore()
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const searchKeyword = ref('')
const filterDepartment = ref<number | null>(null)
const filterStatus = ref<string | null>(null)
const exporting = ref(false)

// 对话框状态
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const passwordDialogVisible = ref(false)
const editingUser = ref<UserListItem | null>(null)
const formMode = ref<'create' | 'edit'>('create')
const viewingUserId = ref<number | null>(null)
const resetPasswordUserId = ref<number | null>(null)

// 计算属性
const userList = computed(() => userStore.userList)
const loading = computed(() => userStore.loading)
const selectedUserIds = computed(() => userStore.selectedUserIds)
const hasSelectedUsers = computed(() => userStore.hasSelectedUsers)
const selectedUsersCount = computed(() => userStore.selectedUsersCount)
const userStats = computed(() => userStore.userStats)
const departments = computed(() => authStore.departments)

// 分页配置
const paginationConfig = computed(() => ({
  page: userStore.pagination.page,
  pageSize: userStore.pagination.page_size,
  itemCount: userStore.pagination.total,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}))

// 表格行键
const getRowKey = (row: UserListItem) => row.id

// 批量操作选项
const batchOptions: DropdownOption[] = [
  {
    label: '批量激活',
    key: 'activate',
    disabled: !hasPermission('user:update')
  },
  {
    label: '批量禁用',
    key: 'deactivate',
    disabled: !hasPermission('user:update')
  },
  {
    label: '批量删除',
    key: 'delete',
    disabled: !hasPermission('user:delete')
  }
]

// 表格列配置
const tableColumns: DataTableColumns<UserListItem> = [
  {
    type: 'selection',
    disabled: (row: UserListItem) => row.id === authStore.userInfo?.id
  },
  {
    title: '用户名',
    key: 'username',
    width: 120,
    sorter: true,
    render: (row: UserListItem) => h('span', { class: 'font-medium' }, row.username)
  },
  {
    title: '昵称',
    key: 'nickname',
    width: 120,
    sorter: true
  },
  {
    title: '邮箱',
    key: 'email',
    width: 200,
    render: (row: UserListItem) => row.email || '-'
  },
  {
    title: '手机号',
    key: 'phone',
    width: 120,
    render: (row: UserListItem) => row.phone || '-'
  },
  {
    title: '主部门',
    key: 'primary_department',
    width: 150,
    render: (row: UserListItem) => row.primary_department?.name || '-'
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row: UserListItem) => h(
      'n-tag',
      {
        type: row.is_active ? 'success' : 'error',
        size: 'small'
      },
      row.is_active ? '正常' : '禁用'
    )
  },
  {
    title: '最后登录',
    key: 'last_login_time',
    width: 160,
    sorter: true,
    render: (row: UserListItem) => {
      if (!row.last_login_time) return '-'
      return new Date(row.last_login_time).toLocaleString('zh-CN')
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    sorter: true,
    render: (row: UserListItem) => new Date(row.created_at).toLocaleString('zh-CN')
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: UserListItem) => {
      const actions = []

      // 查看详情
      if (hasPermission('user:view')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleView(row.id)
          }, { default: () => '查看', icon: () => h(EyeIcon) })
        )
      }

      // 编辑
      if (hasPermission('user:update')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleEdit(row)
          }, { default: () => '编辑', icon: () => h(EditIcon) })
        )
      }

      // 重置密码
      if (hasPermission('user:reset_password') && row.id !== authStore.userInfo?.id) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'warning',
            text: true,
            onClick: () => handleResetPassword(row.id)
          }, { default: () => '重置密码', icon: () => h(KeyIcon) })
        )
      }

      // 切换状态
      if (hasPermission('user:update') && row.id !== authStore.userInfo?.id) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: row.is_active ? 'error' : 'success',
            text: true,
            onClick: () => handleToggleStatus(row)
          }, {
            default: () => row.is_active ? '禁用' : '启用',
            icon: () => h(row.is_active ? CloseIcon : CheckIcon)
          })
        )
      }

      // 删除
      if (hasPermission('user:delete') && row.id !== authStore.userInfo?.id) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'error',
            text: true,
            onClick: () => handleDelete(row)
          }, { default: () => '删除', icon: () => h(DeleteIcon) })
        )
      }

      return h('div', { class: 'flex gap-2' }, actions)
    }
  }
]

// 事件处理方法
const handleSearch = () => {
  userStore.searchUsers(searchKeyword.value)
}

const handleFilter = () => {
  const filters: Partial<UserSearchParams> = {}

  if (filterDepartment.value) {
    filters.department_id = filterDepartment.value
  }

  if (filterStatus.value !== null) {
    filters.is_active = filterStatus.value === 'true'
  }

  userStore.filterUsers(filters)
}

const handleCreate = () => {
  editingUser.value = null
  formMode.value = 'create'
  formDialogVisible.value = true
}

const handleEdit = (user: UserListItem) => {
  editingUser.value = user
  formMode.value = 'edit'
  formDialogVisible.value = true
}

const handleView = (userId: number) => {
  viewingUserId.value = userId
  detailDialogVisible.value = true
}

const handleResetPassword = (userId: number) => {
  resetPasswordUserId.value = userId
  passwordDialogVisible.value = true
}

const handleToggleStatus = async (user: UserListItem) => {
  const action = user.is_active ? '禁用' : '启用'

  dialog.warning({
    title: `确认${action}用户`,
    content: `确定要${action}用户 "${user.nickname}" 吗？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await userStore.toggleUserActive(user.id)
        message.success(`用户${action}成功`)
      } catch (error) {
        message.error(`用户${action}失败`)
      }
    }
  })
}

const handleDelete = (user: UserListItem) => {
  dialog.error({
    title: '确认删除用户',
    content: `确定要删除用户 "${user.nickname}" 吗？此操作不可恢复！`,
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await userStore.deleteUser(user.id)
        message.success('用户删除成功')
      } catch (error) {
        message.error('用户删除失败')
      }
    }
  })
}

const handleSelectionChange = (keys: Array<string | number>) => {
  userStore.selectedUserIds = keys as number[]
}

const handlePageChange = (page: number) => {
  userStore.changePage(page)
}

const handlePageSizeChange = (pageSize: number) => {
  userStore.changePageSize(pageSize)
}

const handleSort = (sorter: any) => {
  if (sorter) {
    const ordering = sorter.order === 'descend' ? `-${sorter.columnKey}` : sorter.columnKey
    userStore.sortUsers(ordering)
  }
}

const handleBatchOperation = async (key: string) => {
  const selectedIds = userStore.selectedUserIds

  if (selectedIds.length === 0) {
    message.warning('请先选择要操作的用户')
    return
  }

  let title = ''
  let content = ''
  let operation = key

  switch (key) {
    case 'activate':
      title = '批量激活用户'
      content = `确定要激活选中的 ${selectedIds.length} 个用户吗？`
      break
    case 'deactivate':
      title = '批量禁用用户'
      content = `确定要禁用选中的 ${selectedIds.length} 个用户吗？`
      break
    case 'delete':
      title = '批量删除用户'
      content = `确定要删除选中的 ${selectedIds.length} 个用户吗？此操作不可恢复！`
      break
  }

  dialog.warning({
    title,
    content,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await userStore.batchOperateUsers({
          user_ids: selectedIds,
          operation: operation as any
        })
        message.success('批量操作成功')
      } catch (error) {
        message.error('批量操作失败')
      }
    }
  })
}

const handleExport = async () => {
  try {
    exporting.value = true
    const blob = await userStore.exportUsers(userStore.searchParams)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `用户列表_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleImport = async (file: File) => {
  try {
    const response = await userStore.importUsers(file)

    if (response.code === 200) {
      message.success(`导入成功：成功 ${response.data.success_count} 条，失败 ${response.data.failed_count} 条`)

      if (response.data.errors.length > 0) {
        dialog.info({
          title: '导入详情',
          content: response.data.errors.join('\n'),
          positiveText: '确定'
        })
      }

      // 刷新列表
      await userStore.fetchUserList()
    }
  } catch (error) {
    message.error('导入失败')
  }

  return false // 阻止默认上传行为
}

const handleFormSuccess = () => {
  formDialogVisible.value = false
  message.success(formMode.value === 'create' ? '用户创建成功' : '用户更新成功')
}

const handlePasswordResetSuccess = () => {
  passwordDialogVisible.value = false
  message.success('密码重置成功')
}

// 生命周期
onMounted(async () => {
  // 获取用户列表
  await userStore.fetchUserList()

  // 获取用户统计
  await userStore.fetchUserStats()
})
</script>

<style scoped>
.user-manage-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px 20px;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 150px;
}

.table-container {
  flex: 1;
  padding: 0 24px 24px;
}

.user-table {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-right {
    justify-content: flex-end;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }

  .stats-cards {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-right {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
