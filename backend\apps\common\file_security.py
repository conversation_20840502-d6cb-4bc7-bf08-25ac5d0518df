"""
文件上传安全验证模块
提供文件类型验证、大小限制、内容扫描等安全功能
"""
import os
import mimetypes
import hashlib
import magic
from typing import List, Dict, Optional, Tuple
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)

class FileSecurityValidator:
    """文件安全验证器"""
    
    # 允许的文件类型配置
    ALLOWED_FILE_TYPES = {
        'image': {
            'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
            'mime_types': ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'],
            'max_size': 10 * 1024 * 1024,  # 10MB
        },
        'document': {
            'extensions': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
            'mime_types': [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain',
            ],
            'max_size': 50 * 1024 * 1024,  # 50MB
        },
        'archive': {
            'extensions': ['.zip', '.rar', '.7z', '.tar', '.gz'],
            'mime_types': [
                'application/zip',
                'application/x-rar-compressed',
                'application/x-7z-compressed',
                'application/x-tar',
                'application/gzip',
            ],
            'max_size': 100 * 1024 * 1024,  # 100MB
        }
    }
    
    # 危险文件扩展名黑名单
    DANGEROUS_EXTENSIONS = [
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
        '.php', '.asp', '.aspx', '.jsp', '.py', '.pl', '.sh', '.ps1'
    ]
    
    # 危险MIME类型黑名单
    DANGEROUS_MIME_TYPES = [
        'application/x-executable',
        'application/x-msdownload',
        'application/x-msdos-program',
        'text/x-php',
        'application/x-httpd-php',
        'text/x-python',
        'application/x-sh',
    ]
    
    def __init__(self, file_type: str = 'image'):
        """
        初始化文件安全验证器
        
        Args:
            file_type: 文件类型 ('image', 'document', 'archive')
        """
        self.file_type = file_type
        self.config = self.ALLOWED_FILE_TYPES.get(file_type, self.ALLOWED_FILE_TYPES['image'])
        
    def validate_file(self, uploaded_file: UploadedFile) -> Dict[str, any]:
        """
        验证上传的文件
        
        Args:
            uploaded_file: Django上传文件对象
            
        Returns:
            验证结果字典
            
        Raises:
            ValidationError: 文件验证失败
        """
        result = {
            'is_valid': True,
            'file_info': {},
            'security_info': {},
            'errors': []
        }
        
        try:
            # 基础信息提取
            file_info = self._extract_file_info(uploaded_file)
            result['file_info'] = file_info
            
            # 文件名安全检查
            self._validate_filename(file_info['name'])
            
            # 文件大小检查
            self._validate_file_size(file_info['size'])
            
            # 文件扩展名检查
            self._validate_file_extension(file_info['extension'])
            
            # MIME类型检查
            self._validate_mime_type(uploaded_file, file_info['mime_type'])
            
            # 文件内容安全扫描
            security_info = self._scan_file_content(uploaded_file)
            result['security_info'] = security_info
            
            # 文件头验证
            self._validate_file_header(uploaded_file, file_info['extension'])
            
            logger.info(f"文件验证成功: {file_info['name']}")
            
        except ValidationError as e:
            result['is_valid'] = False
            result['errors'].append(str(e))
            logger.warning(f"文件验证失败: {uploaded_file.name}, 错误: {e}")
            
        except Exception as e:
            result['is_valid'] = False
            result['errors'].append(f"文件验证异常: {str(e)}")
            logger.error(f"文件验证异常: {uploaded_file.name}, 错误: {e}")
            
        return result
    
    def _extract_file_info(self, uploaded_file: UploadedFile) -> Dict[str, any]:
        """提取文件基础信息"""
        name = uploaded_file.name
        size = uploaded_file.size
        extension = os.path.splitext(name)[1].lower()
        mime_type = uploaded_file.content_type or mimetypes.guess_type(name)[0]
        
        # 计算文件哈希
        uploaded_file.seek(0)
        file_hash = hashlib.md5(uploaded_file.read()).hexdigest()
        uploaded_file.seek(0)
        
        return {
            'name': name,
            'size': size,
            'extension': extension,
            'mime_type': mime_type,
            'hash': file_hash,
        }
    
    def _validate_filename(self, filename: str):
        """验证文件名安全性"""
        if not filename:
            raise ValidationError(_("文件名不能为空"))
            
        # 检查文件名长度
        if len(filename) > 255:
            raise ValidationError(_("文件名过长"))
            
        # 检查危险字符
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        if any(char in filename for char in dangerous_chars):
            raise ValidationError(_("文件名包含非法字符"))
            
        # 检查路径遍历攻击
        if '..' in filename or filename.startswith('/'):
            raise ValidationError(_("文件名包含非法路径"))
    
    def _validate_file_size(self, file_size: int):
        """验证文件大小"""
        max_size = self.config['max_size']
        if file_size > max_size:
            max_size_mb = max_size / (1024 * 1024)
            raise ValidationError(_(f"文件大小超过限制 ({max_size_mb}MB)"))
            
        if file_size == 0:
            raise ValidationError(_("文件不能为空"))
    
    def _validate_file_extension(self, extension: str):
        """验证文件扩展名"""
        # 检查是否在危险扩展名黑名单中
        if extension in self.DANGEROUS_EXTENSIONS:
            raise ValidationError(_(f"不允许的文件类型: {extension}"))
            
        # 检查是否在允许的扩展名白名单中
        if extension not in self.config['extensions']:
            allowed = ', '.join(self.config['extensions'])
            raise ValidationError(_(f"不支持的文件类型。允许的类型: {allowed}"))
    
    def _validate_mime_type(self, uploaded_file: UploadedFile, mime_type: str):
        """验证MIME类型"""
        # 检查是否在危险MIME类型黑名单中
        if mime_type in self.DANGEROUS_MIME_TYPES:
            raise ValidationError(_(f"不允许的文件类型: {mime_type}"))
            
        # 使用python-magic检测真实MIME类型
        try:
            uploaded_file.seek(0)
            real_mime_type = magic.from_buffer(uploaded_file.read(1024), mime=True)
            uploaded_file.seek(0)
            
            # 检查真实MIME类型是否与声明的一致
            if real_mime_type not in self.config['mime_types']:
                allowed = ', '.join(self.config['mime_types'])
                raise ValidationError(_(f"文件类型不匹配。检测到: {real_mime_type}, 允许: {allowed}"))
                
        except Exception as e:
            logger.warning(f"MIME类型检测失败: {e}")
            # 如果检测失败，使用声明的MIME类型进行验证
            if mime_type not in self.config['mime_types']:
                allowed = ', '.join(self.config['mime_types'])
                raise ValidationError(_(f"不支持的文件类型: {mime_type}。允许: {allowed}"))
    
    def _validate_file_header(self, uploaded_file: UploadedFile, extension: str):
        """验证文件头魔数"""
        # 常见文件类型的魔数
        file_signatures = {
            '.jpg': [b'\xff\xd8\xff'],
            '.jpeg': [b'\xff\xd8\xff'],
            '.png': [b'\x89\x50\x4e\x47'],
            '.gif': [b'\x47\x49\x46\x38'],
            '.pdf': [b'\x25\x50\x44\x46'],
            '.zip': [b'\x50\x4b\x03\x04', b'\x50\x4b\x05\x06', b'\x50\x4b\x07\x08'],
            '.doc': [b'\xd0\xcf\x11\xe0'],
            '.docx': [b'\x50\x4b\x03\x04'],
            '.xls': [b'\xd0\xcf\x11\xe0'],
            '.xlsx': [b'\x50\x4b\x03\x04'],
        }
        
        if extension in file_signatures:
            uploaded_file.seek(0)
            file_header = uploaded_file.read(8)
            uploaded_file.seek(0)
            
            signatures = file_signatures[extension]
            if not any(file_header.startswith(sig) for sig in signatures):
                raise ValidationError(_(f"文件头验证失败，文件可能已损坏或类型不匹配"))
    
    def _scan_file_content(self, uploaded_file: UploadedFile) -> Dict[str, any]:
        """扫描文件内容安全性"""
        security_info = {
            'has_malicious_content': False,
            'suspicious_patterns': [],
            'scan_timestamp': None
        }
        
        try:
            uploaded_file.seek(0)
            content = uploaded_file.read()
            uploaded_file.seek(0)
            
            # 检查恶意脚本模式
            malicious_patterns = [
                b'<script',
                b'javascript:',
                b'vbscript:',
                b'onload=',
                b'onerror=',
                b'eval(',
                b'exec(',
                b'system(',
                b'shell_exec(',
            ]
            
            for pattern in malicious_patterns:
                if pattern in content.lower():
                    security_info['has_malicious_content'] = True
                    security_info['suspicious_patterns'].append(pattern.decode('utf-8', errors='ignore'))
            
            if security_info['has_malicious_content']:
                raise ValidationError(_("文件包含可疑内容，可能存在安全风险"))
                
            from django.utils import timezone
            security_info['scan_timestamp'] = timezone.now().isoformat()
            
        except UnicodeDecodeError:
            # 二进制文件，跳过内容扫描
            pass
        except Exception as e:
            logger.warning(f"文件内容扫描失败: {e}")
            
        return security_info

class SecureFileUploadHandler:
    """安全文件上传处理器"""
    
    def __init__(self, upload_path: str = 'uploads'):
        """
        初始化上传处理器
        
        Args:
            upload_path: 上传路径
        """
        self.upload_path = upload_path
        
    def handle_upload(self, uploaded_file: UploadedFile, file_type: str = 'image') -> Dict[str, any]:
        """
        处理文件上传
        
        Args:
            uploaded_file: 上传的文件
            file_type: 文件类型
            
        Returns:
            处理结果
        """
        # 文件安全验证
        validator = FileSecurityValidator(file_type)
        validation_result = validator.validate_file(uploaded_file)
        
        if not validation_result['is_valid']:
            return validation_result
            
        # 生成安全的文件名
        safe_filename = self._generate_safe_filename(uploaded_file.name)
        
        # 创建上传目录
        upload_dir = self._create_upload_directory()
        
        # 保存文件
        file_path = os.path.join(upload_dir, safe_filename)
        saved_path = self._save_file(uploaded_file, file_path)
        
        validation_result['file_info']['saved_path'] = saved_path
        validation_result['file_info']['safe_filename'] = safe_filename
        
        return validation_result
    
    def _generate_safe_filename(self, original_name: str) -> str:
        """生成安全的文件名"""
        import uuid
        from django.utils import timezone
        
        # 提取文件扩展名
        name, ext = os.path.splitext(original_name)
        
        # 生成唯一文件名
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        safe_name = f"{timestamp}_{unique_id}{ext}"
        
        return safe_name
    
    def _create_upload_directory(self) -> str:
        """创建上传目录"""
        from django.utils import timezone
        
        # 按日期组织目录结构
        date_path = timezone.now().strftime('%Y/%m/%d')
        upload_dir = os.path.join(settings.MEDIA_ROOT, self.upload_path, date_path)
        
        os.makedirs(upload_dir, exist_ok=True)
        return upload_dir
    
    def _save_file(self, uploaded_file: UploadedFile, file_path: str) -> str:
        """保存文件到指定路径"""
        with open(file_path, 'wb') as destination:
            for chunk in uploaded_file.chunks():
                destination.write(chunk)
        
        # 返回相对于MEDIA_ROOT的路径
        relative_path = os.path.relpath(file_path, settings.MEDIA_ROOT)
        return relative_path.replace('\\', '/')  # 统一使用正斜杠
