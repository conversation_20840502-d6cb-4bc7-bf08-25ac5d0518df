# HEIM项目开发规范

## 📋 开发前检查清单

### ✅ 环境配置检查
- [ ] 确认使用项目虚拟环境（uv管理）
- [ ] 确认Python版本 >= 3.12
- [ ] 确认uv包管理工具已安装
- [ ] 确认项目依赖通过pyproject.toml管理

### ✅ 包管理规范
- [ ] **严禁使用pip命令**，必须使用uv
- [ ] 添加新依赖：`uv add <package>`
- [ ] 移除依赖：`uv remove <package>`
- [ ] 同步依赖：`uv sync`
- [ ] 运行命令：`uv run <command>`
- [ ] 查看包列表：`uv pip list`

### ✅ 技术栈规范
- [ ] Python 3.12+ (运行时环境)
- [ ] Django 4.2+ (Web框架 LTS版本)
- [ ] Django REST Framework 3.16+ (API框架)
- [ ] uv (Python包管理工具，替代pip)
- [ ] pyproject.toml (项目配置文件)

### ✅ 开发流程规范
- [ ] 所有开发沟通使用中文
- [ ] 代码注释使用中文
- [ ] 文档内容使用中文
- [ ] 错误信息和调试过程使用中文

## 🚀 快速开始命令

### 环境设置
```bash
# 检查uv版本
uv --version

# 同步项目依赖
uv sync

# 验证Django
uv run python manage.py check

# 验证Celery
uv run python -c "from config.celery import app; print('✅ Celery应用导入成功:', app.main)"
```

### 常用开发命令
```bash
# 运行开发服务器
uv run python manage.py runserver

# 创建数据库迁移
uv run python manage.py makemigrations

# 执行数据库迁移
uv run python manage.py migrate

# 创建超级用户
uv run python manage.py createsuperuser

# 运行测试
uv run python manage.py test

# 启动Celery worker
uv run celery -A config worker -l info
```

### 依赖管理命令
```bash
# 添加新依赖
uv add <package_name>

# 添加开发依赖
uv add --dev <package_name>

# 移除依赖
uv remove <package_name>

# 更新依赖
uv sync --upgrade

# 查看依赖树
uv pip show <package_name>
```

## ⚠️ 严禁事项

### 🚫 包管理禁令
- **严禁使用pip install**
- **严禁使用pip uninstall**
- **严禁直接编辑requirements.txt**
- **严禁在全局环境安装项目依赖**

### 🚫 环境管理禁令
- **严禁在全局Python环境开发**
- **严禁混用pip和uv**
- **严禁手动管理虚拟环境**

### 🚫 配置管理禁令
- **严禁直接修改pyproject.toml的dependencies**
- **严禁绕过uv直接安装包**

## 🔍 自动化检查脚本

### 环境验证脚本
```bash
# 运行环境验证
uv run python scripts/check_environment.py
```

### 依赖检查脚本
```bash
# 检查依赖一致性
uv run python scripts/check_dependencies.py
```

## 📚 规范说明

### 为什么使用uv？
1. **性能优势**: uv比pip快10-100倍
2. **依赖解析**: 更好的依赖冲突解决
3. **项目隔离**: 更好的项目环境隔离
4. **现代化**: 符合Python包管理发展趋势
5. **技术规范**: 项目明确要求使用uv

### pyproject.toml vs requirements.txt
- **pyproject.toml**: 现代Python项目标准配置文件
- **requirements.txt**: 传统依赖列表文件（已废弃）
- **优势**: 更好的元数据管理、版本约束、可选依赖

### 虚拟环境管理
- **uv自动管理**: uv会自动创建和管理虚拟环境
- **项目隔离**: 每个项目独立的依赖环境
- **版本控制**: 精确的依赖版本控制

## 🛠️ 故障排除

### 常见问题

#### 1. uv命令未找到
```bash
# 安装uv
pip install uv
# 或
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### 2. 依赖冲突
```bash
# 清理并重新同步
uv sync --reinstall
```

#### 3. 虚拟环境问题
```bash
# 重新创建虚拟环境
rm -rf .venv
uv sync
```

#### 4. 包版本问题
```bash
# 查看包信息
uv pip show <package_name>

# 更新特定包
uv add <package_name>@latest
```

### 紧急恢复

如果环境出现严重问题：

1. **备份当前状态**
   ```bash
   cp pyproject.toml pyproject.toml.backup
   ```

2. **重置环境**
   ```bash
   rm -rf .venv
   uv sync
   ```

3. **验证功能**
   ```bash
   uv run python manage.py check
   ```

## 📞 技术支持

如果遇到技术问题：
1. 首先查看本文档的故障排除部分
2. 检查项目的技术规范文档
3. 确保严格遵循uv包管理规范
4. 所有问题讨论使用中文

## 🔄 规范更新

本规范文档会根据项目发展需要进行更新，请定期查看最新版本。

---

**重要提醒**: 严格遵循本规范是确保项目稳定性和团队协作效率的关键！
