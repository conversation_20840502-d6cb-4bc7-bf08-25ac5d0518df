<template>
  <n-modal
    :show="visible"
    preset="dialog"
    title="会话详情"
    class="session-detail-dialog"
    style="width: 800px"
    @update:show="$emit('update:visible', $event)"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else-if="sessionDetail" class="session-detail-content">
      <!-- 基础信息 -->
      <n-card title="基础信息" class="detail-card">
        <n-descriptions :column="2" size="medium">
          <n-descriptions-item label="会话ID">
            {{ sessionDetail.session_key }}
          </n-descriptions-item>
          <n-descriptions-item label="用户">
            <div class="user-info">
              <span class="username">{{ sessionDetail.user?.username }}</span>
              <span class="nickname">({{ sessionDetail.user?.nickname }})</span>
            </div>
          </n-descriptions-item>
          <n-descriptions-item label="IP地址">
            <span class="ip-address">{{ sessionDetail.ip_address }}</span>
          </n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="sessionDetail.is_active ? 'success' : 'default'" size="small">
              {{ sessionDetail.is_active ? '活跃' : '非活跃' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">
            {{ formatTime(sessionDetail.created_at) }}
          </n-descriptions-item>
          <n-descriptions-item label="最后活动">
            {{ formatTime(sessionDetail.last_activity) }}
          </n-descriptions-item>
          <n-descriptions-item label="过期时间">
            {{ formatTime(sessionDetail.expires_at) }}
          </n-descriptions-item>
          <n-descriptions-item label="会话时长">
            {{ formatDuration(sessionDetail.created_at, sessionDetail.last_activity) }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 设备信息 -->
      <n-card title="设备信息" class="detail-card">
        <n-descriptions :column="2" size="medium">
          <n-descriptions-item label="设备类型">
            <div class="device-type">
              <n-icon size="16" class="device-icon">
                <DeviceDesktopIcon v-if="sessionDetail.device_type === 'desktop'" />
                <DeviceMobileIcon v-else-if="sessionDetail.device_type === 'mobile'" />
                <DeviceTabletIcon v-else-if="sessionDetail.device_type === 'tablet'" />
                <DeviceDesktopIcon v-else />
              </n-icon>
              <span>{{ getDeviceTypeName(sessionDetail.device_type) }}</span>
            </div>
          </n-descriptions-item>
          <n-descriptions-item label="浏览器">
            {{ sessionDetail.browser || '未知' }}
          </n-descriptions-item>
          <n-descriptions-item label="操作系统">
            {{ sessionDetail.os || '未知' }}
          </n-descriptions-item>
          <n-descriptions-item label="设备指纹">
            <span class="fingerprint">{{ sessionDetail.device_fingerprint || '未知' }}</span>
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 地理位置信息 -->
      <n-card v-if="sessionDetail.location" title="地理位置" class="detail-card">
        <n-descriptions :column="2" size="medium">
          <n-descriptions-item label="国家">
            {{ sessionDetail.location.country || '未知' }}
          </n-descriptions-item>
          <n-descriptions-item label="地区">
            {{ sessionDetail.location.region || '未知' }}
          </n-descriptions-item>
          <n-descriptions-item label="城市">
            {{ sessionDetail.location.city || '未知' }}
          </n-descriptions-item>
          <n-descriptions-item label="坐标">
            <span v-if="sessionDetail.latitude && sessionDetail.longitude">
              {{ sessionDetail.latitude.toFixed(4) }}, {{ sessionDetail.longitude.toFixed(4) }}
            </span>
            <span v-else>未知</span>
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 安全信息 -->
      <n-card title="安全信息" class="detail-card">
        <div class="security-info">
          <div class="security-item">
            <span class="label">登录风险评估:</span>
            <n-tag :type="getRiskLevel(sessionDetail.risk_score).type" size="small">
              {{ getRiskLevel(sessionDetail.risk_score).text }}
            </n-tag>
          </div>
          
          <div class="security-item">
            <span class="label">异常检测:</span>
            <n-tag :type="sessionDetail.has_anomaly ? 'warning' : 'success'" size="small">
              {{ sessionDetail.has_anomaly ? '发现异常' : '正常' }}
            </n-tag>
          </div>
          
          <div v-if="sessionDetail.security_alerts" class="security-item">
            <span class="label">安全告警:</span>
            <div class="alert-list">
              <n-tag
                v-for="alert in sessionDetail.security_alerts"
                :key="alert.id"
                type="warning"
                size="small"
                class="alert-tag"
              >
                {{ alert.type }}
              </n-tag>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 活动历史 -->
      <n-card title="活动历史" class="detail-card">
        <div v-if="activityHistory.length > 0" class="activity-timeline">
          <div
            v-for="activity in activityHistory"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
            <div class="activity-content">
              <div class="activity-type">{{ activity.type }}</div>
              <div class="activity-description">{{ activity.description }}</div>
            </div>
          </div>
        </div>
        <div v-else class="no-activity">
          <n-empty description="暂无活动记录" />
        </div>
      </n-card>
    </div>
    
    <div v-else class="empty-state">
      <n-empty description="未找到会话信息" />
    </div>
    
    <template #action>
      <n-space>
        <n-button @click="handleClose">关闭</n-button>
        <n-button
          v-if="sessionDetail?.is_active"
          type="error"
          @click="handleTerminateSession"
        >
          强制下线
        </n-button>
        <n-button
          v-if="sessionDetail"
          type="primary"
          @click="handleExportDetail"
        >
          导出详情
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import {
  DeviceDesktopIcon,
  DeviceMobileIcon,
  DeviceTabletIcon
} from '@vicons/tabler'
import { request } from '@/api'

// Props
interface Props {
  visible: boolean
  sessionId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  sessionId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'session-terminated': []
}>()

// 状态管理
const message = useMessage()

// 响应式数据
const loading = ref(false)
const sessionDetail = ref<any>(null)
const activityHistory = ref<any[]>([])

// 方法
const fetchSessionDetail = async () => {
  if (!props.sessionId) return
  
  try {
    loading.value = true
    
    // 获取会话详情
    const response = await request.get(`/api/auth/sessions/${props.sessionId}/`)
    
    if (response.data.code === 200) {
      sessionDetail.value = response.data.data
      
      // 获取活动历史（模拟数据）
      activityHistory.value = [
        {
          id: 1,
          timestamp: sessionDetail.value.created_at,
          type: '登录',
          description: '用户登录系统'
        },
        {
          id: 2,
          timestamp: sessionDetail.value.last_activity,
          type: '活动',
          description: '最后一次活动'
        }
      ]
    }
    
  } catch (error: any) {
    message.error('获取会话详情失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const handleTerminateSession = async () => {
  if (!sessionDetail.value) return
  
  try {
    await request.post('/api/auth/sessions/terminate/', {
      session_id: sessionDetail.value.id
    })
    
    message.success('会话已强制下线')
    emit('session-terminated')
    
  } catch (error: any) {
    message.error('强制下线失败: ' + (error.response?.data?.message || error.message))
  }
}

const handleExportDetail = () => {
  if (!sessionDetail.value) return
  
  // 导出会话详情
  const exportData = {
    session_id: sessionDetail.value.session_key,
    user: sessionDetail.value.user,
    device_info: {
      type: sessionDetail.value.device_type,
      browser: sessionDetail.value.browser,
      os: sessionDetail.value.os,
      fingerprint: sessionDetail.value.device_fingerprint
    },
    location: sessionDetail.value.location,
    timestamps: {
      created_at: sessionDetail.value.created_at,
      last_activity: sessionDetail.value.last_activity,
      expires_at: sessionDetail.value.expires_at
    },
    security: {
      risk_score: sessionDetail.value.risk_score,
      has_anomaly: sessionDetail.value.has_anomaly,
      alerts: sessionDetail.value.security_alerts
    },
    activity_history: activityHistory.value
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `session_${sessionDetail.value.session_key}_detail.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  message.success('会话详情已导出')
}

const handleClose = () => {
  emit('update:visible', false)
}

const getDeviceTypeName = (type: string) => {
  const typeNames: Record<string, string> = {
    'desktop': '桌面端',
    'mobile': '移动端',
    'tablet': '平板',
    'unknown': '未知'
  }
  return typeNames[type] || '未知'
}

const getRiskLevel = (score: number) => {
  if (score >= 80) {
    return { type: 'error', text: '高风险' }
  } else if (score >= 60) {
    return { type: 'warning', text: '中风险' }
  } else if (score >= 30) {
    return { type: 'info', text: '低风险' }
  } else {
    return { type: 'success', text: '安全' }
  }
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

const formatDuration = (startTime: string, endTime: string) => {
  if (!startTime || !endTime) return '-'
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  const duration = end.getTime() - start.getTime()
  
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.sessionId) {
    fetchSessionDetail()
  }
})

watch(() => props.sessionId, (newSessionId) => {
  if (props.visible && newSessionId) {
    fetchSessionDetail()
  }
})
</script>

<style scoped>
.session-detail-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.session-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin-bottom: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-weight: 500;
}

.nickname {
  color: #6b7280;
  font-size: 14px;
}

.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.device-type {
  display: flex;
  align-items: center;
  gap: 6px;
}

.device-icon {
  color: #6b7280;
}

.fingerprint {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6b7280;
}

.security-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.security-item .label {
  font-weight: 500;
  min-width: 100px;
}

.alert-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.alert-tag {
  margin: 0;
}

.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 16px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.activity-time {
  font-size: 12px;
  color: #6b7280;
  min-width: 140px;
}

.activity-content {
  flex: 1;
}

.activity-type {
  font-weight: 500;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 14px;
  color: #6b7280;
}

.no-activity {
  text-align: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
