<template>
  <n-modal
    :show="visible"
    preset="dialog"
    title="角色详情"
    class="role-detail-dialog"
    style="width: 900px"
    @update:show="$emit('update:visible', $event)"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else-if="roleDetail" class="role-detail-content">
      <!-- 基础信息 -->
      <n-card title="基础信息" class="detail-card">
        <n-grid :cols="3" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <div class="detail-item">
              <label>角色名称</label>
              <span>{{ roleDetail.name }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>角色编码</label>
              <span>{{ roleDetail.code }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>数据范围</label>
              <n-tag type="info" size="small">
                {{ getDataScopeLabel(roleDetail.data_scope) }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>角色状态</label>
              <n-tag :type="roleDetail.is_active ? 'success' : 'error'" size="small">
                {{ roleDetail.is_active ? '正常' : '禁用' }}
              </n-tag>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>排序权重</label>
              <span>{{ roleDetail.sort_order }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>关联用户数</label>
              <span class="user-count">{{ roleDetail.user_count }}人</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>创建时间</label>
              <span>{{ formatDateTime(roleDetail.created_at) }}</span>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="detail-item">
              <label>更新时间</label>
              <span>{{ formatDateTime(roleDetail.updated_at) }}</span>
            </div>
          </n-grid-item>
        </n-grid>
        
        <div v-if="roleDetail.description" class="description">
          <label>角色描述</label>
          <p>{{ roleDetail.description }}</p>
        </div>
      </n-card>

      <!-- 权限信息 -->
      <n-card title="权限信息" class="detail-card">
        <template #header-extra>
          <n-space>
            <n-button size="small" @click="handleExpandAllPermissions">
              全部展开
            </n-button>
            <n-button size="small" @click="handleCollapseAllPermissions">
              全部折叠
            </n-button>
            <n-input
              v-model:value="permissionSearchKeyword"
              placeholder="搜索权限..."
              size="small"
              clearable
              style="width: 200px"
            >
              <template #prefix>
                <n-icon><SearchIcon /></n-icon>
              </template>
            </n-input>
          </n-space>
        </template>
        
        <div v-if="roleDetail.permissions_info.length > 0" class="permissions-container">
          <!-- 权限统计 -->
          <div class="permission-stats">
            <div class="stat-item">
              <span class="stat-label">总权限数:</span>
              <span class="stat-value">{{ roleDetail.permissions_info.length }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">菜单权限:</span>
              <span class="stat-value">{{ getPermissionCountByType('MENU') }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">按钮权限:</span>
              <span class="stat-value">{{ getPermissionCountByType('BUTTON') }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">API权限:</span>
              <span class="stat-value">{{ getPermissionCountByType('API') }}</span>
            </div>
          </div>
          
          <!-- 权限树 -->
          <n-tree
            :data="filteredPermissionTree"
            :expanded-keys="permissionExpandedKeys"
            key-field="id"
            label-field="name"
            children-field="children"
            block-line
            @update:expanded-keys="handlePermissionExpandedKeysChange"
            class="permission-tree"
          >
            <template #default="{ option }">
              <div class="permission-node">
                <n-icon
                  v-if="option.icon"
                  :component="getPermissionIcon(option.icon)"
                  class="permission-icon"
                />
                <span class="permission-name">{{ option.name }}</span>
                <n-tag
                  :type="getPermissionTypeColor(option.permission_type)"
                  size="small"
                  class="permission-type"
                >
                  {{ getPermissionTypeLabel(option.permission_type) }}
                </n-tag>
                <span v-if="option.code" class="permission-code">{{ option.code }}</span>
                <span v-if="option.path" class="permission-path">{{ option.path }}</span>
              </div>
            </template>
          </n-tree>
        </div>
        
        <div v-else class="empty-state">
          <n-empty description="暂无权限信息" />
        </div>
      </n-card>

      <!-- 关联用户 -->
      <n-card title="关联用户" class="detail-card">
        <template #header-extra>
          <n-button
            v-permission="'role:assign_users'"
            size="small"
            type="primary"
            @click="handleManageUsers"
          >
            管理用户
          </n-button>
        </template>
        
        <div v-if="roleUsers.length > 0" class="users-container">
          <div class="user-list">
            <div
              v-for="userRole in roleUsers"
              :key="userRole.id"
              class="user-item"
            >
              <n-avatar
                :size="32"
                :src="userRole.user_info.avatar"
                class="user-avatar"
              >
                {{ userRole.user_info.nickname?.charAt(0) || userRole.user_info.username?.charAt(0) }}
              </n-avatar>
              
              <div class="user-info">
                <div class="user-name">{{ userRole.user_info.nickname || userRole.user_info.username }}</div>
                <div class="user-email">{{ userRole.user_info.email || '-' }}</div>
              </div>
              
              <div class="user-meta">
                <n-tag v-if="userRole.department_info" size="small">
                  {{ userRole.department_info.name }}
                </n-tag>
                <span class="assign-time">{{ formatDateTime(userRole.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-state">
          <n-empty description="暂无关联用户">
            <template #extra>
              <n-button
                v-permission="'role:assign_users'"
                size="small"
                type="primary"
                @click="handleManageUsers"
              >
                分配用户
              </n-button>
            </template>
          </n-empty>
        </div>
      </n-card>
    </div>
    
    <div v-else class="empty-state">
      <n-empty description="未找到角色信息" />
    </div>
    
    <template #action>
      <n-button @click="handleClose">关闭</n-button>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { SearchIcon } from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { roleApi } from '@/api/role'
import type { RoleDetail, UserRole, Permission, PermissionType, DataScope } from '@/types/role'

// Props
interface Props {
  visible: boolean
  roleId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  roleId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'manage-users': [roleId: number]
}>()

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const roleUsers = ref<UserRole[]>([])
const permissionSearchKeyword = ref('')
const permissionExpandedKeys = ref<number[]>([])

// 计算属性
const roleDetail = computed(() => roleStore.currentRole)

// 数据范围标签映射
const dataScopeLabels: Record<DataScope, string> = {
  'ALL': '全部数据',
  'DEPT_AND_SUB': '本部门及子部门',
  'DEPT_ONLY': '仅本部门',
  'SELF_ONLY': '仅本人',
  'CUSTOM': '自定义'
}

// 过滤后的权限树
const filteredPermissionTree = computed(() => {
  if (!roleDetail.value?.permissions_info) return []
  
  const buildPermissionTree = (permissions: Permission[]): Permission[] => {
    const permissionMap = new Map<number, Permission & { children: Permission[] }>()
    const rootPermissions: (Permission & { children: Permission[] })[] = []
    
    // 创建权限映射
    permissions.forEach(permission => {
      permissionMap.set(permission.id, { ...permission, children: [] })
    })
    
    // 构建树形结构
    permissions.forEach(permission => {
      const permissionNode = permissionMap.get(permission.id)!
      if (permission.parent && permissionMap.has(permission.parent)) {
        const parent = permissionMap.get(permission.parent)!
        parent.children.push(permissionNode)
      } else {
        rootPermissions.push(permissionNode)
      }
    })
    
    return rootPermissions
  }
  
  const tree = buildPermissionTree(roleDetail.value.permissions_info)
  
  if (!permissionSearchKeyword.value) {
    return tree
  }
  
  const filterTree = (nodes: any[]): any[] => {
    return nodes.filter(node => {
      const matchesKeyword = node.name.toLowerCase().includes(permissionSearchKeyword.value.toLowerCase()) ||
                           node.code.toLowerCase().includes(permissionSearchKeyword.value.toLowerCase())
      
      if (matchesKeyword) {
        return true
      }
      
      if (node.children && node.children.length > 0) {
        const filteredChildren = filterTree(node.children)
        if (filteredChildren.length > 0) {
          node.children = filteredChildren
          return true
        }
      }
      
      return false
    })
  }
  
  return filterTree(tree)
})

// 方法
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getDataScopeLabel = (dataScope: DataScope) => {
  return dataScopeLabels[dataScope] || dataScope
}

const getPermissionCountByType = (type: PermissionType) => {
  if (!roleDetail.value?.permissions_info) return 0
  return roleDetail.value.permissions_info.filter(p => p.permission_type === type).length
}

const getPermissionIcon = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  return SearchIcon // 临时使用搜索图标
}

const getPermissionTypeColor = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return 'info'
    case 'BUTTON':
      return 'warning'
    case 'API':
      return 'success'
    default:
      return 'default'
  }
}

const getPermissionTypeLabel = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return '菜单'
    case 'BUTTON':
      return '按钮'
    case 'API':
      return 'API'
    default:
      return type
  }
}

const handlePermissionExpandedKeysChange = (keys: number[]) => {
  permissionExpandedKeys.value = keys
}

const handleExpandAllPermissions = () => {
  const getAllKeys = (nodes: any[]): number[] => {
    let keys: number[] = []
    nodes.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  
  permissionExpandedKeys.value = getAllKeys(filteredPermissionTree.value)
}

const handleCollapseAllPermissions = () => {
  permissionExpandedKeys.value = []
}

const handleManageUsers = () => {
  if (props.roleId) {
    emit('manage-users', props.roleId)
  }
}

const fetchRoleDetail = async () => {
  if (!props.roleId) return
  
  try {
    loading.value = true
    await roleStore.fetchRoleDetail(props.roleId)
  } catch (error) {
    message.error('获取角色详情失败')
  } finally {
    loading.value = false
  }
}

const fetchRoleUsers = async () => {
  if (!props.roleId) return
  
  try {
    const response = await roleApi.getRoleUsers(props.roleId)
    if (response.code === 200) {
      roleUsers.value = response.data
    }
  } catch (error) {
    console.error('获取角色用户失败:', error)
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.roleId) {
    fetchRoleDetail()
    fetchRoleUsers()
  }
})

watch(() => props.roleId, (newRoleId) => {
  if (props.visible && newRoleId) {
    fetchRoleDetail()
    fetchRoleUsers()
  }
})
</script>

<style scoped>
.role-detail-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.role-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #1f2937;
}

.user-count {
  color: #3b82f6;
  font-weight: 500;
}

.description {
  margin-top: 16px;
}

.description label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  display: block;
  margin-bottom: 8px;
}

.description p {
  font-size: 14px;
  color: #1f2937;
  margin: 0;
  line-height: 1.5;
}

.permissions-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.permission-stats {
  display: flex;
  gap: 24px;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.permission-tree {
  max-height: 400px;
  overflow-y: auto;
}

.permission-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.permission-icon {
  flex-shrink: 0;
  color: #6b7280;
}

.permission-name {
  flex: 1;
  font-weight: 500;
  color: #1f2937;
}

.permission-type {
  flex-shrink: 0;
}

.permission-code {
  flex-shrink: 0;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.permission-path {
  flex-shrink: 0;
  font-size: 11px;
  color: #9ca3af;
}

.users-container {
  max-height: 300px;
  overflow-y: auto;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.user-email {
  font-size: 12px;
  color: #6b7280;
}

.user-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.assign-time {
  font-size: 11px;
  color: #9ca3af;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
