"""
系统健康检查API视图
"""
import psutil
import time
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from celery import current_app as celery_app
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])  # 健康检查接口允许匿名访问
def health_check(request):
    """
    系统健康检查接口
    返回系统各组件的健康状态
    """
    start_time = time.time()
    
    health_status = {
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'checks': {},
        'system_info': {},
        'response_time_ms': 0
    }
    
    # 检查数据库连接
    db_status = check_database()
    health_status['checks']['database'] = db_status
    
    # 检查缓存服务
    cache_status = check_cache()
    health_status['checks']['cache'] = cache_status
    
    # 检查消息队列
    queue_status = check_message_queue()
    health_status['checks']['queue'] = queue_status
    
    # 检查系统资源
    system_status = check_system_resources()
    health_status['checks']['system'] = system_status
    health_status['system_info'] = system_status.get('details', {})
    
    # 检查磁盘空间
    disk_status = check_disk_space()
    health_status['checks']['disk'] = disk_status
    
    # 计算总体状态
    all_checks = [db_status, cache_status, queue_status, system_status, disk_status]
    if any(check['status'] == 'critical' for check in all_checks):
        health_status['status'] = 'critical'
    elif any(check['status'] == 'warning' for check in all_checks):
        health_status['status'] = 'warning'
    
    # 计算响应时间
    health_status['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
    
    # 根据状态返回相应的HTTP状态码
    if health_status['status'] == 'critical':
        status_code = 503  # Service Unavailable
    elif health_status['status'] == 'warning':
        status_code = 200  # OK but with warnings
    else:
        status_code = 200  # OK
    
    return JsonResponse(health_status, status=status_code)


@api_view(['GET'])
@permission_classes([AllowAny])
def liveness_probe(request):
    """
    存活性探针 - 用于Kubernetes等容器编排系统
    只检查应用是否能响应请求
    """
    return JsonResponse({
        'status': 'alive',
        'timestamp': timezone.now().isoformat()
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def readiness_probe(request):
    """
    就绪性探针 - 检查应用是否准备好接收流量
    """
    # 检查关键依赖
    db_ok = check_database()['status'] != 'critical'
    cache_ok = check_cache()['status'] != 'critical'
    
    if db_ok and cache_ok:
        return JsonResponse({
            'status': 'ready',
            'timestamp': timezone.now().isoformat()
        })
    else:
        return JsonResponse({
            'status': 'not_ready',
            'timestamp': timezone.now().isoformat(),
            'reason': 'Critical dependencies unavailable'
        }, status=503)


def check_database():
    """检查数据库连接"""
    try:
        start_time = time.time()
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        response_time = (time.time() - start_time) * 1000
        
        return {
            'status': 'healthy',
            'response_time_ms': round(response_time, 2),
            'message': '数据库连接正常'
        }
    except Exception as e:
        return {
            'status': 'critical',
            'message': f'数据库连接失败: {str(e)}'
        }


def check_cache():
    """检查缓存服务"""
    try:
        start_time = time.time()
        
        # 测试缓存读写
        test_key = 'health_check_test'
        test_value = 'test_value'
        
        cache.set(test_key, test_value, timeout=60)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            cache.delete(test_key)
            response_time = (time.time() - start_time) * 1000
            
            return {
                'status': 'healthy',
                'response_time_ms': round(response_time, 2),
                'message': '缓存服务正常'
            }
        else:
            return {
                'status': 'critical',
                'message': '缓存读写测试失败'
            }
    except Exception as e:
        return {
            'status': 'critical',
            'message': f'缓存服务异常: {str(e)}'
        }


def check_message_queue():
    """检查消息队列"""
    try:
        start_time = time.time()
        
        # 检查Celery连接
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        response_time = (time.time() - start_time) * 1000
        
        if stats:
            worker_count = len(stats)
            return {
                'status': 'healthy',
                'response_time_ms': round(response_time, 2),
                'message': f'消息队列正常，{worker_count}个工作进程',
                'details': {
                    'worker_count': worker_count,
                    'broker_url': celery_app.conf.broker_url
                }
            }
        else:
            return {
                'status': 'warning',
                'response_time_ms': round(response_time, 2),
                'message': '消息队列连接正常但无工作进程'
            }
    except Exception as e:
        return {
            'status': 'critical',
            'message': f'消息队列异常: {str(e)}'
        }


def check_system_resources():
    """检查系统资源使用情况"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 系统负载（仅Unix系统）
        load_avg = None
        try:
            load_avg = psutil.getloadavg()
        except (AttributeError, OSError):
            pass
        
        # 进程数量
        process_count = len(psutil.pids())
        
        # 判断状态
        status = 'healthy'
        messages = []
        
        if cpu_percent > 90:
            status = 'critical'
            messages.append(f'CPU使用率过高: {cpu_percent}%')
        elif cpu_percent > 80:
            status = 'warning'
            messages.append(f'CPU使用率较高: {cpu_percent}%')
        
        if memory_percent > 90:
            status = 'critical'
            messages.append(f'内存使用率过高: {memory_percent}%')
        elif memory_percent > 80:
            status = 'warning'
            messages.append(f'内存使用率较高: {memory_percent}%')
        
        return {
            'status': status,
            'message': '; '.join(messages) if messages else '系统资源正常',
            'details': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_total_gb': round(memory.total / (1024**3), 2),
                'memory_used_gb': round(memory.used / (1024**3), 2),
                'load_average': load_avg,
                'process_count': process_count
            }
        }
    except Exception as e:
        return {
            'status': 'critical',
            'message': f'系统资源检查异常: {str(e)}'
        }


def check_disk_space():
    """检查磁盘空间"""
    try:
        # 检查根目录磁盘使用情况
        disk_usage = psutil.disk_usage('/')
        usage_percent = (disk_usage.used / disk_usage.total) * 100
        
        # 判断状态
        if usage_percent > 95:
            status = 'critical'
            message = f'磁盘空间严重不足: {usage_percent:.1f}%'
        elif usage_percent > 85:
            status = 'warning'
            message = f'磁盘空间不足: {usage_percent:.1f}%'
        else:
            status = 'healthy'
            message = f'磁盘空间正常: {usage_percent:.1f}%'
        
        return {
            'status': status,
            'message': message,
            'details': {
                'usage_percent': round(usage_percent, 1),
                'total_gb': round(disk_usage.total / (1024**3), 2),
                'used_gb': round(disk_usage.used / (1024**3), 2),
                'free_gb': round(disk_usage.free / (1024**3), 2)
            }
        }
    except Exception as e:
        return {
            'status': 'critical',
            'message': f'磁盘空间检查异常: {str(e)}'
        }


@api_view(['GET'])
@permission_classes([AllowAny])
def system_info(request):
    """
    获取系统信息
    """
    try:
        import platform
        
        # 系统基本信息
        system_info = {
            'platform': platform.platform(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'hostname': platform.node(),
        }
        
        # CPU信息
        cpu_info = {
            'cpu_count': psutil.cpu_count(),
            'cpu_count_logical': psutil.cpu_count(logical=True),
            'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            'total': memory.total,
            'available': memory.available,
            'used': memory.used,
            'free': memory.free,
            'percent': memory.percent
        }
        
        # 磁盘信息
        disk_usage = psutil.disk_usage('/')
        disk_info = {
            'total': disk_usage.total,
            'used': disk_usage.used,
            'free': disk_usage.free,
            'percent': (disk_usage.used / disk_usage.total) * 100
        }
        
        # 网络信息
        network = psutil.net_io_counters()
        network_info = {
            'bytes_sent': network.bytes_sent,
            'bytes_recv': network.bytes_recv,
            'packets_sent': network.packets_sent,
            'packets_recv': network.packets_recv
        }
        
        # 启动时间
        boot_time = psutil.boot_time()
        
        return JsonResponse({
            'system': system_info,
            'cpu': cpu_info,
            'memory': memory_info,
            'disk': disk_info,
            'network': network_info,
            'boot_time': boot_time,
            'uptime_seconds': time.time() - boot_time,
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        return JsonResponse({
            'error': f'获取系统信息失败: {str(e)}'
        }, status=500)
