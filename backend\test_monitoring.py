#!/usr/bin/env python
"""
测试系统监控功能
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
django.setup()

from apps.monitoring.services import system_monitor, health_checker

def test_system_monitoring():
    """测试系统监控功能"""
    print("🔍 测试系统监控功能")
    
    try:
        # 测试系统指标收集
        print("\n📊 收集系统指标...")
        system_metrics = system_monitor.collect_system_metrics()
        print(f"✅ 系统指标收集成功:")
        print(f"   CPU使用率: {system_metrics.cpu_usage}%")
        print(f"   内存使用率: {system_metrics.memory_usage}%")
        print(f"   磁盘使用率: {system_metrics.disk_usage:.1f}%")
        print(f"   进程数量: {system_metrics.process_count}")
        
        # 测试应用指标收集
        print("\n📈 收集应用指标...")
        app_metrics = system_monitor.collect_application_metrics()
        print(f"✅ 应用指标收集成功:")
        print(f"   总请求数: {app_metrics.total_requests}")
        print(f"   错误率: {app_metrics.error_rate:.2f}%")
        print(f"   平均响应时间: {app_metrics.avg_response_time:.2f}ms")
        print(f"   活跃用户数: {app_metrics.active_users}")
        
        # 测试健康检查
        print("\n🏥 执行健康检查...")
        health_results = health_checker.check_all_services()
        print(f"✅ 健康检查完成，检查了 {len(health_results)} 个服务:")
        for result in health_results:
            status_emoji = "✅" if result.status == 'healthy' else "❌"
            print(f"   {status_emoji} {result.service_name}: {result.get_status_display()}")
            if result.response_time:
                print(f"      响应时间: {result.response_time:.2f}ms")
        
        print("\n🎉 系统监控功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_system_monitoring()
    sys.exit(0 if success else 1)
