"""
数据范围权限集成测试
测试不同权限级别的数据访问控制、跨部门访问控制、权限过滤机制、缓存机制
"""
import pytest
from django.test import TestCase
from django.urls import reverse
from django.core.cache import cache
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock

from apps.permissions.models import Role, Permission, UserRole
from apps.permissions.services import PermissionService
from apps.users.models import UserProfile
from apps.departments.models import Department
from apps.audit.models import OperationLog
from tests.factories import (
    UserProfileFactory, DepartmentFactory, RoleFactory, 
    PermissionFactory, OperationLogFactory
)


@pytest.mark.django_db
@pytest.mark.integration
@pytest.mark.permissions
class TestDataScopePermissions(APITestCase):
    """数据范围权限集成测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建部门层级结构
        self.root_dept = DepartmentFactory(name='总公司', code='ROOT')
        self.branch_dept = DepartmentFactory(name='分公司', code='BRANCH', parent=self.root_dept)
        self.sub_dept = DepartmentFactory(name='子部门', code='SUB', parent=self.branch_dept)
        
        # 创建不同数据范围的角色
        self.all_data_role = RoleFactory(name='全部数据角色', data_scope='ALL')
        self.dept_and_sub_role = RoleFactory(name='本部门及下级角色', data_scope='DEPT_AND_SUB')
        self.dept_only_role = RoleFactory(name='仅本部门角色', data_scope='DEPT_ONLY')
        self.self_only_role = RoleFactory(name='仅本人角色', data_scope='SELF_ONLY')
        
        # 创建权限
        self.view_user_permission = PermissionFactory(code='user:view', name='查看用户')
        self.view_log_permission = PermissionFactory(code='log:view', name='查看日志')
        
        # 为角色分配权限
        for role in [self.all_data_role, self.dept_and_sub_role, self.dept_only_role, self.self_only_role]:
            role.permissions.add(self.view_user_permission, self.view_log_permission)
        
        # 创建不同部门的用户
        self.root_user = UserProfileFactory(nickname='总公司用户')
        self.branch_user = UserProfileFactory(nickname='分公司用户')
        self.sub_user = UserProfileFactory(nickname='子部门用户')
        self.other_user = UserProfileFactory(nickname='其他用户')
        
        # 创建独立部门和用户（用于测试跨部门访问）
        self.other_dept = DepartmentFactory(name='其他部门', code='OTHER')
        self.other_dept_user = UserProfileFactory(nickname='其他部门用户')
        
        # 设置密码
        for user in [self.root_user, self.branch_user, self.sub_user, self.other_user, self.other_dept_user]:
            user.set_password('test123')
            user.save()
    
    def _assign_user_role(self, user, role, department):
        """分配用户角色"""
        UserRole.objects.create(user=user, role=role, department=department)
    
    def _authenticate_user(self, user):
        """认证用户"""
        refresh = RefreshToken.for_user(user)
        token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        return token
    
    def test_all_data_scope_access(self):
        """测试全部数据权限级别"""
        # 分配全部数据权限给根部门用户
        self._assign_user_role(self.root_user, self.all_data_role, self.root_dept)
        self._authenticate_user(self.root_user)
        
        # 查询用户列表
        url = reverse('users:user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该能看到所有用户
        user_ids = [user['id'] for user in response.data['results']]
        self.assertIn(self.root_user.id, user_ids)
        self.assertIn(self.branch_user.id, user_ids)
        self.assertIn(self.sub_user.id, user_ids)
        self.assertIn(self.other_dept_user.id, user_ids)
    
    def test_dept_and_sub_scope_access(self):
        """测试本部门及下级权限级别"""
        # 分配本部门及下级权限给分公司用户
        self._assign_user_role(self.branch_user, self.dept_and_sub_role, self.branch_dept)
        self._authenticate_user(self.branch_user)
        
        # 查询用户列表
        url = reverse('users:user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该能看到本部门及下级部门的用户
        user_ids = [user['id'] for user in response.data['results']]
        self.assertIn(self.branch_user.id, user_ids)  # 本部门
        self.assertIn(self.sub_user.id, user_ids)     # 下级部门
        
        # 不应该看到上级部门和其他部门的用户
        self.assertNotIn(self.root_user.id, user_ids)
        self.assertNotIn(self.other_dept_user.id, user_ids)
    
    def test_dept_only_scope_access(self):
        """测试仅本部门权限级别"""
        # 分配仅本部门权限给子部门用户
        self._assign_user_role(self.sub_user, self.dept_only_role, self.sub_dept)
        self._authenticate_user(self.sub_user)
        
        # 查询用户列表
        url = reverse('users:user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该只能看到本部门的用户
        user_ids = [user['id'] for user in response.data['results']]
        self.assertIn(self.sub_user.id, user_ids)  # 本部门
        
        # 不应该看到其他部门的用户
        self.assertNotIn(self.root_user.id, user_ids)
        self.assertNotIn(self.branch_user.id, user_ids)
        self.assertNotIn(self.other_dept_user.id, user_ids)
    
    def test_self_only_scope_access(self):
        """测试仅本人权限级别"""
        # 分配仅本人权限给其他用户
        self._assign_user_role(self.other_user, self.self_only_role, self.other_dept)
        self._authenticate_user(self.other_user)
        
        # 查询用户列表
        url = reverse('users:user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该只能看到自己
        user_ids = [user['id'] for user in response.data['results']]
        self.assertEqual(len(user_ids), 1)
        self.assertIn(self.other_user.id, user_ids)
    
    def test_cross_department_access_denied(self):
        """测试跨部门数据访问控制"""
        # 分配仅本部门权限给分公司用户
        self._assign_user_role(self.branch_user, self.dept_only_role, self.branch_dept)
        self._authenticate_user(self.branch_user)
        
        # 尝试访问其他部门用户的详情
        url = reverse('users:user-detail', kwargs={'pk': self.other_dept_user.id})
        response = self.client.get(url)
        
        # 应该返回403或404（根据实现方式）
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND])
    
    def test_operation_log_data_scope_filtering(self):
        """测试操作日志的数据范围过滤"""
        # 创建不同用户的操作日志
        root_log = OperationLogFactory(user=self.root_user, operation_desc='根部门操作')
        branch_log = OperationLogFactory(user=self.branch_user, operation_desc='分公司操作')
        sub_log = OperationLogFactory(user=self.sub_user, operation_desc='子部门操作')
        other_log = OperationLogFactory(user=self.other_dept_user, operation_desc='其他部门操作')
        
        # 分配本部门及下级权限给分公司用户
        self._assign_user_role(self.branch_user, self.dept_and_sub_role, self.branch_dept)
        self._authenticate_user(self.branch_user)
        
        # 查询操作日志
        url = reverse('audit:operationlog-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该只能看到本部门及下级部门用户的日志
        log_descriptions = [log['operation_desc'] for log in response.data['results']]
        self.assertIn('分公司操作', log_descriptions)
        self.assertIn('子部门操作', log_descriptions)
        self.assertNotIn('根部门操作', log_descriptions)
        self.assertNotIn('其他部门操作', log_descriptions)
    
    def test_data_scope_boundary_conditions(self):
        """测试数据范围权限边界条件"""
        # 测试用户没有角色的情况
        no_role_user = UserProfileFactory(nickname='无角色用户')
        no_role_user.set_password('test123')
        no_role_user.save()
        
        self._authenticate_user(no_role_user)
        
        url = reverse('users:user-list')
        response = self.client.get(url)
        
        # 没有角色的用户应该无法访问数据
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_multiple_roles_data_scope(self):
        """测试用户拥有多个角色时的数据范围"""
        # 给用户分配多个不同数据范围的角色
        self._assign_user_role(self.branch_user, self.dept_only_role, self.branch_dept)
        self._assign_user_role(self.branch_user, self.dept_and_sub_role, self.sub_dept)
        
        self._authenticate_user(self.branch_user)
        
        url = reverse('users:user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该取最大的数据范围权限
        user_ids = [user['id'] for user in response.data['results']]
        self.assertIn(self.branch_user.id, user_ids)
        self.assertIn(self.sub_user.id, user_ids)
    
    @patch('django.core.cache.cache.get')
    @patch('django.core.cache.cache.set')
    def test_data_scope_permission_caching(self, mock_cache_set, mock_cache_get):
        """测试数据范围权限缓存机制"""
        # 模拟缓存未命中
        mock_cache_get.return_value = None
        
        # 分配权限
        self._assign_user_role(self.branch_user, self.dept_and_sub_role, self.branch_dept)
        
        # 检查用户权限（应该触发缓存设置）
        permissions = PermissionService.get_user_permissions(self.branch_user)
        
        # 验证缓存被调用
        mock_cache_get.assert_called()
        mock_cache_set.assert_called()
        
        # 模拟缓存命中
        cached_permissions = ['user:view', 'log:view']
        mock_cache_get.return_value = cached_permissions
        
        # 再次检查权限（应该从缓存获取）
        permissions = PermissionService.get_user_permissions(self.branch_user)
        
        self.assertEqual(permissions, cached_permissions)
    
    def test_data_scope_permission_cache_invalidation(self):
        """测试数据范围权限缓存失效"""
        # 分配权限
        self._assign_user_role(self.branch_user, self.dept_only_role, self.branch_dept)
        
        # 获取权限（触发缓存）
        permissions_before = PermissionService.get_user_permissions(self.branch_user)
        
        # 修改用户角色
        self._assign_user_role(self.branch_user, self.all_data_role, self.root_dept)
        
        # 清除缓存
        cache.clear()
        
        # 再次获取权限
        permissions_after = PermissionService.get_user_permissions(self.branch_user)
        
        # 权限应该发生变化
        self.assertNotEqual(permissions_before, permissions_after)


@pytest.mark.django_db
@pytest.mark.unit
class TestDataScopeService(TestCase):
    """数据范围服务单元测试"""
    
    def setUp(self):
        """测试前置设置"""
        # 创建部门层级
        self.parent_dept = DepartmentFactory(name='父部门')
        self.child_dept = DepartmentFactory(name='子部门', parent=self.parent_dept)
        self.grandchild_dept = DepartmentFactory(name='孙子部门', parent=self.child_dept)
        
        # 创建用户和角色
        self.user = UserProfileFactory()
        self.role = RoleFactory(data_scope='DEPT_AND_SUB')
        
        # 建立用户部门关系
        from apps.users.models import UserDepartment
        UserDepartment.objects.create(
            user=self.user, department=self.parent_dept, is_primary=True, is_active=True
        )

        # 建立用户角色关系
        UserRole.objects.create(user=self.user, role=self.role, department=self.parent_dept)
    
    def test_get_user_data_scope_departments(self):
        """测试获取用户数据范围内的部门"""
        from apps.permissions.services import PermissionService
        
        departments = PermissionService.get_user_data_scope_departments(self.user)
        
        # 应该包含本部门及下级部门
        dept_ids = [dept.id for dept in departments]
        self.assertIn(self.parent_dept.id, dept_ids)
        self.assertIn(self.child_dept.id, dept_ids)
        self.assertIn(self.grandchild_dept.id, dept_ids)
    
    def test_filter_queryset_by_data_scope(self):
        """测试根据数据范围过滤查询集"""
        from apps.permissions.services import PermissionService
        
        # 创建不同部门的用户
        parent_user = UserProfileFactory()
        child_user = UserProfileFactory()
        other_user = UserProfileFactory()
        
        # 模拟用户部门关系（这里简化处理）
        queryset = UserProfile.objects.all()
        
        # 根据数据范围过滤
        filtered_queryset = PermissionService.filter_queryset_by_data_scope(
            queryset, self.user, 'user'
        )
        
        # 验证过滤结果
        self.assertIsNotNone(filtered_queryset)
    
    def test_check_data_scope_permission(self):
        """测试检查数据范围权限"""
        from apps.permissions.services import PermissionService
        
        # 测试访问本部门数据
        has_permission = PermissionService.check_data_scope_permission(
            self.user, self.parent_dept.id, 'department'
        )
        self.assertTrue(has_permission)
        
        # 测试访问下级部门数据
        has_permission = PermissionService.check_data_scope_permission(
            self.user, self.child_dept.id, 'department'
        )
        self.assertTrue(has_permission)
        
        # 测试访问无权限部门数据
        other_dept = DepartmentFactory(name='无权限部门')
        has_permission = PermissionService.check_data_scope_permission(
            self.user, other_dept.id, 'department'
        )
        self.assertFalse(has_permission)


@pytest.mark.django_db
@pytest.mark.integration
class TestDataScopeIntegrationScenarios(TestCase):
    """数据范围权限集成场景测试"""
    
    def setUp(self):
        """测试前置设置"""
        # 创建复杂的组织架构
        self.headquarters = DepartmentFactory(name='总部', code='HQ')
        self.sales_dept = DepartmentFactory(name='销售部', code='SALES', parent=self.headquarters)
        self.tech_dept = DepartmentFactory(name='技术部', code='TECH', parent=self.headquarters)
        self.sales_team1 = DepartmentFactory(name='销售一组', code='SALES1', parent=self.sales_dept)
        self.sales_team2 = DepartmentFactory(name='销售二组', code='SALES2', parent=self.sales_dept)
        
        # 创建角色
        self.ceo_role = RoleFactory(name='CEO', data_scope='ALL')
        self.dept_manager_role = RoleFactory(name='部门经理', data_scope='DEPT_AND_SUB')
        self.team_leader_role = RoleFactory(name='组长', data_scope='DEPT_ONLY')
        self.employee_role = RoleFactory(name='员工', data_scope='SELF_ONLY')
        
        # 创建用户
        self.ceo = UserProfileFactory(nickname='CEO')
        self.sales_manager = UserProfileFactory(nickname='销售经理')
        self.team1_leader = UserProfileFactory(nickname='一组组长')
        self.employee1 = UserProfileFactory(nickname='员工1')
        self.employee2 = UserProfileFactory(nickname='员工2')
        
        # 分配角色
        UserRole.objects.create(user=self.ceo, role=self.ceo_role, department=self.headquarters)
        UserRole.objects.create(user=self.sales_manager, role=self.dept_manager_role, department=self.sales_dept)
        UserRole.objects.create(user=self.team1_leader, role=self.team_leader_role, department=self.sales_team1)
        UserRole.objects.create(user=self.employee1, role=self.employee_role, department=self.sales_team1)
        UserRole.objects.create(user=self.employee2, role=self.employee_role, department=self.sales_team2)
    
    def test_hierarchical_data_access_scenario(self):
        """测试层级化数据访问场景"""
        from apps.permissions.services import PermissionService
        
        # CEO应该能访问所有数据
        ceo_depts = PermissionService.get_user_data_scope_departments(self.ceo)
        self.assertEqual(len(ceo_depts), 5)  # 所有部门
        
        # 销售经理应该能访问销售部及下级
        manager_depts = PermissionService.get_user_data_scope_departments(self.sales_manager)
        dept_codes = [dept.code for dept in manager_depts]
        self.assertIn('SALES', dept_codes)
        self.assertIn('SALES1', dept_codes)
        self.assertIn('SALES2', dept_codes)
        self.assertNotIn('TECH', dept_codes)
        self.assertNotIn('HQ', dept_codes)
        
        # 组长应该只能访问本组
        leader_depts = PermissionService.get_user_data_scope_departments(self.team1_leader)
        dept_codes = [dept.code for dept in leader_depts]
        self.assertIn('SALES1', dept_codes)
        self.assertNotIn('SALES2', dept_codes)
        self.assertNotIn('SALES', dept_codes)
    
    def test_cross_department_collaboration_scenario(self):
        """测试跨部门协作场景"""
        # 给销售经理分配技术部的临时权限
        UserRole.objects.create(
            user=self.sales_manager, 
            role=self.team_leader_role, 
            department=self.tech_dept
        )
        
        from apps.permissions.services import PermissionService
        
        # 销售经理现在应该能访问技术部数据
        manager_depts = PermissionService.get_user_data_scope_departments(self.sales_manager)
        dept_codes = [dept.code for dept in manager_depts]
        self.assertIn('TECH', dept_codes)
        self.assertIn('SALES', dept_codes)
        self.assertIn('SALES1', dept_codes)
        self.assertIn('SALES2', dept_codes)
    
    def test_temporary_permission_elevation_scenario(self):
        """测试临时权限提升场景"""
        # 员工临时获得组长权限
        temp_role = UserRole.objects.create(
            user=self.employee1,
            role=self.team_leader_role,
            department=self.sales_team1
        )
        
        from apps.permissions.services import PermissionService
        
        # 员工现在应该有组长级别的数据访问权限
        employee_depts = PermissionService.get_user_data_scope_departments(self.employee1)
        dept_codes = [dept.code for dept in employee_depts]
        self.assertIn('SALES1', dept_codes)
        
        # 撤销临时权限
        temp_role.delete()
        
        # 权限应该回到原来的级别
        employee_depts = PermissionService.get_user_data_scope_departments(self.employee1)
        # 仅本人权限，应该没有部门访问权限或只有自己
        self.assertLessEqual(len(employee_depts), 1)
