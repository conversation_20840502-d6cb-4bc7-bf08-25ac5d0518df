<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full bg-white shadow rounded-lg p-8 text-center">
      <!-- 404图标 -->
      <div class="mb-6">
        <svg class="mx-auto h-24 w-24 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-1.01-6-2.709M15 11.291A7.962 7.962 0 0112 9c-2.34 0-4.5 1.01-6 2.709M12 3v6" />
        </svg>
      </div>
      
      <!-- 错误信息 -->
      <h1 class="text-4xl font-bold text-gray-900 mb-4">404</h1>
      <h2 class="text-xl font-semibold text-gray-700 mb-4">页面未找到</h2>
      <p class="text-gray-600 mb-8">
        抱歉，您访问的页面不存在。可能是页面已被删除或URL地址错误。
      </p>
      
      <!-- 搜索建议 -->
      <div class="mb-6">
        <n-input 
          v-model:value="searchQuery" 
          placeholder="搜索页面或功能..." 
          @keyup.enter="handleSearch"
          class="mb-4"
        >
          <template #suffix>
            <n-button text @click="handleSearch">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </n-button>
          </template>
        </n-input>
      </div>
      
      <!-- 操作按钮 -->
      <div class="space-y-4">
        <n-button type="primary" @click="goHome" class="w-full">
          返回首页
        </n-button>
        <n-button @click="goBack" class="w-full">
          返回上一页
        </n-button>
      </div>
      
      <!-- 常用链接 -->
      <div class="mt-8">
        <h3 class="text-sm font-medium text-gray-700 mb-4">常用页面</h3>
        <div class="grid grid-cols-2 gap-2">
          <n-button text @click="router.push('/')" class="text-sm">
            首页
          </n-button>
          <n-button text @click="router.push('/about')" class="text-sm">
            关于
          </n-button>
        </div>
      </div>
      
      <!-- 额外信息 -->
      <div class="mt-8 text-sm text-gray-500">
        <p>错误代码: 404 Not Found</p>
        <p>请求路径: {{ route.fullPath }}</p>
        <p>时间: {{ currentTime }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'

const router = useRouter()
const route = useRoute()
const message = useMessage()

// 响应式数据
const currentTime = ref('')
const searchQuery = ref('')

// 获取当前时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
  message.info('已返回首页')
}

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    message.info(`搜索功能暂未实现: ${searchQuery.value}`)
    // TODO: 实现搜索功能
  }
}

// 组件挂载时
onMounted(() => {
  updateTime()
  
  // 每秒更新时间
  setInterval(updateTime, 1000)
  
  // 记录404错误（可选）
  console.warn(`404 Error: Page not found - ${route.fullPath}`)
})
</script>

<style scoped>
/* 自定义样式 */
.bounce-animation {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
