<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">8%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_9e189ee61a034fc9___init___py.html">apps\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250___init___py.html">apps\audit\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_apps_py.html">apps\audit\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f81546b0b2653676___init___py.html">apps\audit\management\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6___init___py.html">apps\audit\management\commands\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0001_initial_py.html">apps\audit\migrations\0001_initial.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0002_initial_py.html">apps\audit\migrations\0002_initial.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html">apps\audit\migrations\0003_add_error_operation_type.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd___init___py.html">apps\audit\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html">apps\audit\models.py</a></td>
                <td>26</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="21 26">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html">apps\audit\serializers.py</a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html">apps\audit\services.py</a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html">apps\audit\tasks.py</a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html">apps\audit\test_tasks.py</a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html">apps\audit\tests.py</a></td>
                <td>211</td>
                <td>211</td>
                <td>0</td>
                <td class="right" data-ratio="0 211">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_urls_py.html">apps\audit\urls.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html">apps\audit\views.py</a></td>
                <td>209</td>
                <td>209</td>
                <td>0</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa___init___py.html">apps\authentication\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html">apps\authentication\anomaly_detector.py</a></td>
                <td>222</td>
                <td>222</td>
                <td>0</td>
                <td class="right" data-ratio="0 222">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_apps_py.html">apps\authentication\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91a8e3ad6c12af09___init___py.html">apps\authentication\management\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458___init___py.html">apps\authentication\management\commands\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html">apps\authentication\management\commands\cleanup_auth.py</a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0001_initial_py.html">apps\authentication\migrations\0001_initial.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0002_initial_py.html">apps\authentication\migrations\0002_initial.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html">apps\authentication\migrations\0003_simplecaptcha.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html">apps\authentication\migrations\0004_loginattempt_ipwhitelist.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html">apps\authentication\migrations\0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9___init___py.html">apps\authentication\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html">apps\authentication\models.py</a></td>
                <td>173</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="133 173">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html">apps\authentication\security.py</a></td>
                <td>111</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html">apps\authentication\serializers.py</a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html">apps\authentication\services.py</a></td>
                <td>126</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="0 126">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html">apps\authentication\session_views.py</a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html">apps\authentication\tasks.py</a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html">apps\authentication\tests.py</a></td>
                <td>102</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_urls_py.html">apps\authentication\urls.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html">apps\authentication\views.py</a></td>
                <td>169</td>
                <td>169</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0___init___py.html">apps\backup\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_apps_py.html">apps\backup\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac143cddc957a9a8_0001_initial_py.html">apps\backup\migrations\0001_initial.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac143cddc957a9a8___init___py.html">apps\backup\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html">apps\backup\models.py</a></td>
                <td>138</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="117 138">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html">apps\backup\services.py</a></td>
                <td>266</td>
                <td>266</td>
                <td>0</td>
                <td class="right" data-ratio="0 266">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5___init___py.html">apps\common\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_apps_py.html">apps\common\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html">apps\common\config_manager.py</a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html">apps\common\config_views.py</a></td>
                <td>189</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html">apps\common\decorators.py</a></td>
                <td>93</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="0 93">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html">apps\common\department_service.py</a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html">apps\common\example_views.py</a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html">apps\common\examples.py</a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html">apps\common\exceptions.py</a></td>
                <td>128</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="0 128">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html">apps\common\file_security.py</a></td>
                <td>151</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="0 151">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html">apps\common\file_views.py</a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_789b0fc39a74c14f___init___py.html">apps\common\management\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8___init___py.html">apps\common\management\commands\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html">apps\common\management\commands\test_data_scope.py</a></td>
                <td>125</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="0 125">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html">apps\common\middleware.py</a></td>
                <td>212</td>
                <td>212</td>
                <td>0</td>
                <td class="right" data-ratio="0 212">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html">apps\common\models.py</a></td>
                <td>20</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="14 20">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html">apps\common\permissions.py</a></td>
                <td>343</td>
                <td>343</td>
                <td>0</td>
                <td class="right" data-ratio="0 343">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html">apps\common\response.py</a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html">apps\common\test_exceptions.py</a></td>
                <td>198</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="0 198">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html">apps\common\test_file_security.py</a></td>
                <td>144</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="0 144">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html">apps\common\test_views.py</a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html">apps\common\tests.py</a></td>
                <td>246</td>
                <td>246</td>
                <td>0</td>
                <td class="right" data-ratio="0 246">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6___init___py.html">apps\common\tests\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html">apps\common\tests\test_data_scope.py</a></td>
                <td>205</td>
                <td>205</td>
                <td>0</td>
                <td class="right" data-ratio="0 205">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_urls_py.html">apps\common\urls.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html">apps\common\utils.py</a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c___init___py.html">apps\departments\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_apps_py.html">apps\departments\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0001_initial_py.html">apps\departments\migrations\0001_initial.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0002_initial_py.html">apps\departments\migrations\0002_initial.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7___init___py.html">apps\departments\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html">apps\departments\models.py</a></td>
                <td>69</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="49 69">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html">apps\departments\serializers.py</a></td>
                <td>148</td>
                <td>148</td>
                <td>0</td>
                <td class="right" data-ratio="0 148">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html">apps\departments\tests.py</a></td>
                <td>254</td>
                <td>254</td>
                <td>0</td>
                <td class="right" data-ratio="0 254">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_urls_py.html">apps\departments\urls.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html">apps\departments\views.py</a></td>
                <td>235</td>
                <td>235</td>
                <td>0</td>
                <td class="right" data-ratio="0 235">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358___init___py.html">apps\health\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_apps_py.html">apps\health\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_urls_py.html">apps\health\urls.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html">apps\health\views.py</a></td>
                <td>148</td>
                <td>148</td>
                <td>0</td>
                <td class="right" data-ratio="0 148">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc___init___py.html">apps\monitoring\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html">apps\monitoring\apps.py</a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_62e9cac5df6ba8e3_0001_initial_py.html">apps\monitoring\migrations\0001_initial.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_62e9cac5df6ba8e3___init___py.html">apps\monitoring\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html">apps\monitoring\models.py</a></td>
                <td>113</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="108 113">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html">apps\monitoring\services.py</a></td>
                <td>255</td>
                <td>255</td>
                <td>0</td>
                <td class="right" data-ratio="0 255">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html">apps\monitoring\tasks.py</a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_urls_py.html">apps\monitoring\urls.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html">apps\monitoring\views.py</a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f___init___py.html">apps\permissions\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_apps_py.html">apps\permissions\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html">apps\permissions\decorators.py</a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e434bc180f346938___init___py.html">apps\permissions\management\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69___init___py.html">apps\permissions\management\commands\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html">apps\permissions\management\commands\init_permissions.py</a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0001_initial_py.html">apps\permissions\migrations\0001_initial.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0002_initial_py.html">apps\permissions\migrations\0002_initial.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33___init___py.html">apps\permissions\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html">apps\permissions\models.py</a></td>
                <td>55</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="51 55">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html">apps\permissions\serializers.py</a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html">apps\permissions\services.py</a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html">apps\permissions\tests.py</a></td>
                <td>153</td>
                <td>153</td>
                <td>0</td>
                <td class="right" data-ratio="0 153">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_urls_py.html">apps\permissions\urls.py</a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html">apps\permissions\views.py</a></td>
                <td>225</td>
                <td>225</td>
                <td>0</td>
                <td class="right" data-ratio="0 225">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d___init___py.html">apps\users\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_apps_py.html">apps\users\apps.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0001_initial_py.html">apps\users\migrations\0001_initial.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html">apps\users\migrations\0002_alter_userprofile_managers.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb___init___py.html">apps\users\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html">apps\users\models.py</a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html">apps\users\serializers.py</a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html">apps\users\tests.py</a></td>
                <td>196</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="0 196">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_urls_py.html">apps\users\urls.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html">apps\users\views.py</a></td>
                <td>175</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="0 175">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>8449</td>
                <td>7783</td>
                <td>0</td>
                <td class="right" data-ratio="666 8449">8%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_e7a2fb5ce97bac5d_views_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_9e189ee61a034fc9___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
