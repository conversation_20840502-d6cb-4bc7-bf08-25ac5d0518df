<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    :title="title"
    class="permission-form-dialog"
    style="width: 600px"
    @update:show="$emit('update:visible', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="24">
        <!-- 基础信息 -->
        <n-form-item-gi label="权限名称" path="name">
          <n-input
            v-model:value="formData.name"
            placeholder="请输入权限名称"
            @blur="checkPermissionName"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="权限编码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入权限编码"
            @blur="checkPermissionCode"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="权限类型" path="permission_type">
          <n-select
            v-model:value="formData.permission_type"
            :options="permissionTypeOptions"
            placeholder="请选择权限类型"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="父权限" path="parent">
          <n-tree-select
            v-model:value="formData.parent"
            :options="parentPermissionOptions"
            placeholder="请选择父权限（可选）"
            clearable
            key-field="id"
            label-field="name"
            children-field="children"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="排序权重" path="sort_order">
          <n-input-number
            v-model:value="formData.sort_order"
            placeholder="请输入排序权重"
            :min="0"
            :max="9999"
            class="w-full"
          />
        </n-form-item-gi>
        
        <n-form-item-gi label="权限状态" path="is_active">
          <n-switch
            v-model:value="formData.is_active"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item-gi>
      </n-grid>
      
      <!-- 路径信息 -->
      <n-form-item
        v-if="formData.permission_type === 'MENU'"
        label="路由路径"
        path="path"
      >
        <n-input
          v-model:value="formData.path"
          placeholder="请输入路由路径，如：/system/users"
        />
      </n-form-item>
      
      <n-form-item
        v-if="formData.permission_type === 'MENU'"
        label="组件路径"
        path="component"
      >
        <n-input
          v-model:value="formData.component"
          placeholder="请输入组件路径，如：views/system/UserManage.vue"
        />
      </n-form-item>
      
      <n-form-item
        v-if="formData.permission_type === 'API'"
        label="HTTP方法"
        path="http_method"
      >
        <n-select
          v-model:value="formData.http_method"
          :options="httpMethodOptions"
          placeholder="请选择HTTP方法"
        />
      </n-form-item>
      
      <!-- 图标选择 -->
      <n-form-item
        v-if="formData.permission_type === 'MENU'"
        label="菜单图标"
        path="icon"
      >
        <n-input
          v-model:value="formData.icon"
          placeholder="请输入图标名称"
        >
          <template #suffix>
            <n-button size="small" @click="handleSelectIcon">
              选择图标
            </n-button>
          </template>
        </n-input>
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ mode === 'create' ? '创建' : '更新' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { useRoleStore } from '@/stores/role'
import { roleApi } from '@/api/role'
import type { Permission, PermissionCreateForm, PermissionEditForm, PermissionType } from '@/types/role'

// Props
interface Props {
  visible: boolean
  permission?: Permission | null
  mode: 'create' | 'edit'
  parentId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  permission: null,
  parentId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)

// 表单数据
const formData = ref<PermissionCreateForm>({
  name: '',
  code: '',
  permission_type: 'MENU' as PermissionType,
  parent: null,
  path: '',
  component: '',
  icon: '',
  http_method: '',
  sort_order: 0,
  is_active: true
})

// 权限类型选项
const permissionTypeOptions = [
  { label: '菜单权限', value: 'MENU' },
  { label: '按钮权限', value: 'BUTTON' },
  { label: 'API权限', value: 'API' }
]

// HTTP方法选项
const httpMethodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'DELETE', value: 'DELETE' }
]

// 计算属性
const title = computed(() => {
  return props.mode === 'create' ? '新增权限' : '编辑权限'
})

const parentPermissionOptions = computed(() => {
  // 过滤掉当前编辑的权限，避免循环引用
  const filterPermissions = (permissions: any[], excludeId?: number): any[] => {
    return permissions.filter(p => p.id !== excludeId).map(p => ({
      ...p,
      children: p.children ? filterPermissions(p.children, excludeId) : []
    }))
  }
  
  return filterPermissions(roleStore.permissionTree, props.permission?.id)
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 50, message: '权限名称长度为2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { min: 2, max: 50, message: '权限编码长度为2-50个字符', trigger: 'blur' },
    { pattern: /^[a-z0-9_:]+$/, message: '权限编码只能包含小写字母、数字、下划线和冒号', trigger: 'blur' }
  ],
  permission_type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ],
  path: [
    {
      validator: (rule, value) => {
        if (formData.value.permission_type === 'MENU' && !value) {
          return false
        }
        return true
      },
      message: '菜单权限必须设置路由路径',
      trigger: 'blur'
    }
  ],
  component: [
    {
      validator: (rule, value) => {
        if (formData.value.permission_type === 'MENU' && !value) {
          return false
        }
        return true
      },
      message: '菜单权限必须设置组件路径',
      trigger: 'blur'
    }
  ],
  http_method: [
    {
      validator: (rule, value) => {
        if (formData.value.permission_type === 'API' && !value) {
          return false
        }
        return true
      },
      message: 'API权限必须设置HTTP方法',
      trigger: 'change'
    }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序权重范围为0-9999', trigger: 'blur' }
  ]
}

// 方法
const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    permission_type: 'MENU' as PermissionType,
    parent: props.parentId || null,
    path: '',
    component: '',
    icon: '',
    http_method: '',
    sort_order: 0,
    is_active: true
  }
}

const loadPermissionData = () => {
  if (props.permission && props.mode === 'edit') {
    formData.value = {
      name: props.permission.name,
      code: props.permission.code,
      permission_type: props.permission.permission_type,
      parent: props.permission.parent,
      path: props.permission.path,
      component: props.permission.component,
      icon: props.permission.icon,
      http_method: props.permission.http_method,
      sort_order: props.permission.sort_order,
      is_active: props.permission.is_active
    }
  }
}

const checkPermissionName = async () => {
  if (!formData.value.name) return
  
  try {
    const response = await roleApi.checkPermissionNameAvailable(
      formData.value.name,
      props.mode === 'edit' ? props.permission?.id : undefined
    )
    if (!response.data.available) {
      message.error('权限名称已存在')
    }
  } catch (error) {
    console.error('检查权限名称失败:', error)
  }
}

const checkPermissionCode = async () => {
  if (!formData.value.code) return
  
  try {
    const response = await roleApi.checkPermissionCodeAvailable(
      formData.value.code,
      props.mode === 'edit' ? props.permission?.id : undefined
    )
    if (!response.data.available) {
      message.error('权限编码已存在')
    }
  } catch (error) {
    console.error('检查权限编码失败:', error)
  }
}

const handleSelectIcon = () => {
  // 这里可以打开图标选择器
  message.info('图标选择器功能待实现')
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    if (props.mode === 'create') {
      await roleStore.createPermission(formData.value)
    } else if (props.permission) {
      const editData: PermissionEditForm = {
        name: formData.value.name,
        code: formData.value.code,
        permission_type: formData.value.permission_type,
        parent: formData.value.parent,
        path: formData.value.path,
        component: formData.value.component,
        icon: formData.value.icon,
        http_method: formData.value.http_method,
        sort_order: formData.value.sort_order,
        is_active: formData.value.is_active
      }
      await roleStore.updatePermission(props.permission.id, editData)
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (props.mode === 'create') {
        resetForm()
      } else {
        loadPermissionData()
      }
    })
  }
})

// 生命周期
onMounted(async () => {
  // 获取权限树（用于父权限选择）
  await roleStore.fetchPermissionTree()
})
</script>

<style scoped>
.permission-form-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.w-full {
  width: 100%;
}
</style>
