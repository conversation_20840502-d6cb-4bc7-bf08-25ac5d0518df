<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full bg-white shadow rounded-lg p-8 text-center">
      <!-- 403图标 -->
      <div class="mb-6">
        <svg class="mx-auto h-24 w-24 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      
      <!-- 错误信息 -->
      <h1 class="text-4xl font-bold text-gray-900 mb-4">403</h1>
      <h2 class="text-xl font-semibold text-gray-700 mb-4">访问被拒绝</h2>
      <p class="text-gray-600 mb-8">
        抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
      </p>
      
      <!-- 操作按钮 -->
      <div class="space-y-4">
        <n-button type="primary" @click="goBack" class="w-full">
          返回上一页
        </n-button>
        <n-button @click="goHome" class="w-full">
          返回首页
        </n-button>
      </div>
      
      <!-- 额外信息 -->
      <div class="mt-8 text-sm text-gray-500">
        <p>错误代码: 403 Forbidden</p>
        <p v-if="requiredPermission">需要权限: {{ requiredPermission }}</p>
        <p>时间: {{ currentTime }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'

const router = useRouter()
const route = useRoute()
const message = useMessage()

// 响应式数据
const currentTime = ref('')
const requiredPermission = ref('')

// 获取当前时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
  message.info('已返回首页')
}

// 组件挂载时
onMounted(() => {
  updateTime()
  
  // 从路由参数获取所需权限信息
  if (route.query.permission) {
    requiredPermission.value = route.query.permission as string
  }
  
  // 每秒更新时间
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
/* 自定义样式 */
.error-animation {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
</style>
