/**
 * 审计日志 API 接口
 */
import { request } from './index'
import type { ApiResponse } from '@/types/api'
import type {
  OperationLog,
  OperationLogDetail,
  OperationLogListItem,
  OperationLogSearchParams,
  OperationLogExportParams,
  OperationLogStats,
  UserOperationStats,
  SystemHealthStats,
  LogCleanupParams,
  LogCleanupResult,
  OperationLogListResponse,
  ExportTask,
  LogCleanupPolicy,
  LogAlertRule,
  LogAlertEvent,
  LogAnalysisResult,
  LogExportTemplate,
  LogArchiveConfig,
  LogMonitorConfig
} from '@/types/audit'

/**
 * 操作日志管理 API
 */

// 获取操作日志列表
export function getOperationLogList(params?: OperationLogSearchParams): Promise<ApiResponse<OperationLogListResponse>> {
  return request.get('/api/audit/logs/', { params })
}

// 获取操作日志详情
export function getOperationLogDetail(id: number): Promise<ApiResponse<OperationLogDetail>> {
  return request.get(`/api/audit/logs/${id}/`)
}

// 导出操作日志
export function exportOperationLogs(data: OperationLogExportParams): Promise<ApiResponse<{ task_id: string }>> {
  return request.post('/api/audit/logs/export/', data)
}

// 获取操作日志统计
export function getOperationLogStats(): Promise<ApiResponse<OperationLogStats>> {
  return request.get('/api/audit/logs/stats/')
}

// 获取用户操作统计
export function getUserOperationStats(days?: number): Promise<ApiResponse<UserOperationStats>> {
  return request.get('/api/audit/logs/user_stats/', { 
    params: { days: days || 30 } 
  })
}

// 获取系统健康状态
export function getSystemHealthStats(): Promise<ApiResponse<SystemHealthStats>> {
  return request.get('/api/audit/logs/health/')
}

// 清理过期日志
export function cleanupLogs(data: LogCleanupParams): Promise<ApiResponse<LogCleanupResult>> {
  return request.post('/api/audit/logs/cleanup/', data)
}

// 获取日志分析结果
export function getLogAnalysis(params?: OperationLogSearchParams): Promise<ApiResponse<LogAnalysisResult>> {
  return request.get('/api/audit/logs/analysis/', { params })
}

/**
 * 导出任务管理 API
 */

// 获取导出任务列表
export function getExportTaskList(): Promise<ApiResponse<ExportTask[]>> {
  return request.get('/api/audit/export-tasks/')
}

// 获取导出任务详情
export function getExportTaskDetail(taskId: string): Promise<ApiResponse<ExportTask>> {
  return request.get(`/api/audit/export-tasks/${taskId}/`)
}

// 取消导出任务
export function cancelExportTask(taskId: string): Promise<ApiResponse<void>> {
  return request.post(`/api/audit/export-tasks/${taskId}/cancel/`)
}

// 删除导出任务
export function deleteExportTask(taskId: string): Promise<ApiResponse<void>> {
  return request.delete(`/api/audit/export-tasks/${taskId}/`)
}

// 下载导出文件
export function downloadExportFile(taskId: string): Promise<Blob> {
  return request.get(`/api/audit/export-tasks/${taskId}/download/`, {
    responseType: 'blob'
  })
}

/**
 * 日志清理策略管理 API
 */

// 获取清理策略列表
export function getCleanupPolicyList(): Promise<ApiResponse<LogCleanupPolicy[]>> {
  return request.get('/api/audit/cleanup-policies/')
}

// 获取清理策略详情
export function getCleanupPolicyDetail(id: string): Promise<ApiResponse<LogCleanupPolicy>> {
  return request.get(`/api/audit/cleanup-policies/${id}/`)
}

// 创建清理策略
export function createCleanupPolicy(data: Partial<LogCleanupPolicy>): Promise<ApiResponse<LogCleanupPolicy>> {
  return request.post('/api/audit/cleanup-policies/', data)
}

// 更新清理策略
export function updateCleanupPolicy(id: string, data: Partial<LogCleanupPolicy>): Promise<ApiResponse<LogCleanupPolicy>> {
  return request.put(`/api/audit/cleanup-policies/${id}/`, data)
}

// 删除清理策略
export function deleteCleanupPolicy(id: string): Promise<ApiResponse<void>> {
  return request.delete(`/api/audit/cleanup-policies/${id}/`)
}

// 启用/禁用清理策略
export function toggleCleanupPolicy(id: string): Promise<ApiResponse<{ enabled: boolean }>> {
  return request.post(`/api/audit/cleanup-policies/${id}/toggle/`)
}

/**
 * 日志告警管理 API
 */

// 获取告警规则列表
export function getAlertRuleList(): Promise<ApiResponse<LogAlertRule[]>> {
  return request.get('/api/audit/alert-rules/')
}

// 获取告警规则详情
export function getAlertRuleDetail(id: string): Promise<ApiResponse<LogAlertRule>> {
  return request.get(`/api/audit/alert-rules/${id}/`)
}

// 创建告警规则
export function createAlertRule(data: Partial<LogAlertRule>): Promise<ApiResponse<LogAlertRule>> {
  return request.post('/api/audit/alert-rules/', data)
}

// 更新告警规则
export function updateAlertRule(id: string, data: Partial<LogAlertRule>): Promise<ApiResponse<LogAlertRule>> {
  return request.put(`/api/audit/alert-rules/${id}/`, data)
}

// 删除告警规则
export function deleteAlertRule(id: string): Promise<ApiResponse<void>> {
  return request.delete(`/api/audit/alert-rules/${id}/`)
}

// 启用/禁用告警规则
export function toggleAlertRule(id: string): Promise<ApiResponse<{ enabled: boolean }>> {
  return request.post(`/api/audit/alert-rules/${id}/toggle/`)
}

// 获取告警事件列表
export function getAlertEventList(): Promise<ApiResponse<LogAlertEvent[]>> {
  return request.get('/api/audit/alert-events/')
}

// 确认告警事件
export function acknowledgeAlertEvent(id: string): Promise<ApiResponse<void>> {
  return request.post(`/api/audit/alert-events/${id}/acknowledge/`)
}

/**
 * 导出模板管理 API
 */

// 获取导出模板列表
export function getExportTemplateList(): Promise<ApiResponse<LogExportTemplate[]>> {
  return request.get('/api/audit/export-templates/')
}

// 获取导出模板详情
export function getExportTemplateDetail(id: string): Promise<ApiResponse<LogExportTemplate>> {
  return request.get(`/api/audit/export-templates/${id}/`)
}

// 创建导出模板
export function createExportTemplate(data: Partial<LogExportTemplate>): Promise<ApiResponse<LogExportTemplate>> {
  return request.post('/api/audit/export-templates/', data)
}

// 更新导出模板
export function updateExportTemplate(id: string, data: Partial<LogExportTemplate>): Promise<ApiResponse<LogExportTemplate>> {
  return request.put(`/api/audit/export-templates/${id}/`, data)
}

// 删除导出模板
export function deleteExportTemplate(id: string): Promise<ApiResponse<void>> {
  return request.delete(`/api/audit/export-templates/${id}/`)
}

/**
 * 系统配置管理 API
 */

// 获取日志归档配置
export function getArchiveConfig(): Promise<ApiResponse<LogArchiveConfig>> {
  return request.get('/api/audit/config/archive/')
}

// 更新日志归档配置
export function updateArchiveConfig(data: LogArchiveConfig): Promise<ApiResponse<LogArchiveConfig>> {
  return request.put('/api/audit/config/archive/', data)
}

// 获取日志监控配置
export function getMonitorConfig(): Promise<ApiResponse<LogMonitorConfig>> {
  return request.get('/api/audit/config/monitor/')
}

// 更新日志监控配置
export function updateMonitorConfig(data: LogMonitorConfig): Promise<ApiResponse<LogMonitorConfig>> {
  return request.put('/api/audit/config/monitor/', data)
}

/**
 * 实时数据 API
 */

// 获取实时日志流
export function getRealTimeLogs(limit?: number): Promise<ApiResponse<OperationLogListItem[]>> {
  return request.get('/api/audit/logs/realtime/', { 
    params: { limit: limit || 50 } 
  })
}

// 获取实时统计数据
export function getRealTimeStats(): Promise<ApiResponse<any>> {
  return request.get('/api/audit/stats/realtime/')
}

/**
 * 搜索和筛选 API
 */

// 全文搜索日志
export function searchLogs(query: string, params?: OperationLogSearchParams): Promise<ApiResponse<OperationLogListResponse>> {
  return request.get('/api/audit/logs/search/', { 
    params: { q: query, ...params } 
  })
}

// 获取搜索建议
export function getSearchSuggestions(query: string): Promise<ApiResponse<string[]>> {
  return request.get('/api/audit/logs/search/suggestions/', { 
    params: { q: query } 
  })
}

// 获取筛选选项
export function getFilterOptions(): Promise<ApiResponse<{
  operation_types: Array<{ value: string; label: string }>
  methods: Array<{ value: string; label: string }>
  status_codes: Array<{ value: number; label: string }>
}>> {
  return request.get('/api/audit/logs/filter-options/')
}

/**
 * 工具函数
 */

// 格式化操作类型
export function formatOperationType(type: string): string {
  const typeMap: Record<string, string> = {
    'LOGIN': '登录',
    'LOGOUT': '登出',
    'CREATE': '创建',
    'UPDATE': '更新',
    'DELETE': '删除',
    'QUERY': '查询',
    'ERROR': '系统异常'
  }
  return typeMap[type] || type
}

// 格式化状态码
export function formatStatusCode(code: number): { text: string; type: 'success' | 'warning' | 'error' } {
  if (code >= 200 && code < 300) {
    return { text: '成功', type: 'success' }
  } else if (code >= 400 && code < 500) {
    return { text: '客户端错误', type: 'warning' }
  } else if (code >= 500) {
    return { text: '服务器错误', type: 'error' }
  } else {
    return { text: '其他', type: 'warning' }
  }
}

// 格式化响应时间
export function formatResponseTime(time: number): string {
  if (time < 100) {
    return `${time}ms`
  } else if (time < 1000) {
    return `${time}ms`
  } else {
    return `${(time / 1000).toFixed(2)}s`
  }
}

// 审计日志API对象
export const auditApi = {
  // 操作日志
  getOperationLogList,
  getOperationLogDetail,
  exportOperationLogs,
  getOperationLogStats,
  getUserOperationStats,
  getSystemHealthStats,
  cleanupLogs,
  getLogAnalysis,
  
  // 导出任务
  getExportTaskList,
  getExportTaskDetail,
  cancelExportTask,
  deleteExportTask,
  downloadExportFile,
  
  // 清理策略
  getCleanupPolicyList,
  getCleanupPolicyDetail,
  createCleanupPolicy,
  updateCleanupPolicy,
  deleteCleanupPolicy,
  toggleCleanupPolicy,
  
  // 告警管理
  getAlertRuleList,
  getAlertRuleDetail,
  createAlertRule,
  updateAlertRule,
  deleteAlertRule,
  toggleAlertRule,
  getAlertEventList,
  acknowledgeAlertEvent,
  
  // 导出模板
  getExportTemplateList,
  getExportTemplateDetail,
  createExportTemplate,
  updateExportTemplate,
  deleteExportTemplate,
  
  // 系统配置
  getArchiveConfig,
  updateArchiveConfig,
  getMonitorConfig,
  updateMonitorConfig,
  
  // 实时数据
  getRealTimeLogs,
  getRealTimeStats,
  
  // 搜索筛选
  searchLogs,
  getSearchSuggestions,
  getFilterOptions,
  
  // 工具函数
  formatOperationType,
  formatStatusCode,
  formatResponseTime
}
