<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const isLoggedIn = computed(() => authStore.isLoggedIn)

// 登出处理
const handleLogout = async () => {
  try {
    await authStore.logout()
    message.success('登出成功')
    router.push('/login')
  } catch (error) {
    message.error('登出失败')
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">HEIM企业管理平台</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span v-if="userInfo" class="text-gray-700">
              欢迎，{{ userInfo.nickname || userInfo.username }}
            </span>
            <n-button @click="handleLogout" type="primary">
              登出
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <div class="border-4 border-dashed border-gray-200 rounded-lg p-8">
          <div class="text-center">
            <h2 class="text-3xl font-extrabold text-gray-900 mb-4">
              认证成功！
            </h2>
            <p class="text-lg text-gray-600 mb-8">
              您已成功登录到HEIM企业管理平台
            </p>

            <div v-if="userInfo" class="bg-white shadow rounded-lg p-6 max-w-md mx-auto">
              <h3 class="text-lg font-medium text-gray-900 mb-4">用户信息</h3>
              <dl class="space-y-2">
                <div class="flex justify-between">
                  <dt class="text-sm font-medium text-gray-500">用户名:</dt>
                  <dd class="text-sm text-gray-900">{{ userInfo.username }}</dd>
                </div>
                <div class="flex justify-between">
                  <dt class="text-sm font-medium text-gray-500">昵称:</dt>
                  <dd class="text-sm text-gray-900">{{ userInfo.nickname }}</dd>
                </div>
                <div class="flex justify-between">
                  <dt class="text-sm font-medium text-gray-500">邮箱:</dt>
                  <dd class="text-sm text-gray-900">{{ userInfo.email || '未设置' }}</dd>
                </div>
                <div class="flex justify-between">
                  <dt class="text-sm font-medium text-gray-500">最后登录IP:</dt>
                  <dd class="text-sm text-gray-900">{{ userInfo.last_login_ip || '未知' }}</dd>
                </div>
                <div class="flex justify-between">
                  <dt class="text-sm font-medium text-gray-500">最后登录时间:</dt>
                  <dd class="text-sm text-gray-900">{{ userInfo.last_login_time || '未知' }}</dd>
                </div>
              </dl>
            </div>

            <!-- 权限测试按钮 -->
            <div class="mt-8 space-x-4">
              <n-button v-permission="'user:create'" type="success">
                创建用户 (需要权限)
              </n-button>
              <n-button v-permission-hide="'admin:manage'" type="warning">
                管理员功能 (需要权限)
              </n-button>
              <n-button v-permission-disable="'system:config'" type="error">
                系统配置 (需要权限)
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>
