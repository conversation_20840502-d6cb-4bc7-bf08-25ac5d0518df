<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">8%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_9e189ee61a034fc9___init___py.html">apps\__init__.py</a></td>
                <td class="name left"><a href="z_9e189ee61a034fc9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250___init___py.html">apps\audit\__init__.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_apps_py.html#t4">apps\audit\apps.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_apps_py.html#t4"><data value='AuditConfig'>AuditConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_apps_py.html">apps\audit\apps.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f81546b0b2653676___init___py.html">apps\audit\management\__init__.py</a></td>
                <td class="name left"><a href="z_f81546b0b2653676___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6___init___py.html">apps\audit\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t16">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html#t16"><data value='Command'>Command</data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html">apps\audit\management\commands\cleanup_audit_logs.py</a></td>
                <td class="name left"><a href="z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0001_initial_py.html#t6">apps\audit\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0001_initial_py.html#t6"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0001_initial_py.html">apps\audit\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0002_initial_py.html#t8">apps\audit\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0002_initial_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0002_initial_py.html">apps\audit\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0002_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html#t6">apps\audit\migrations\0003_add_error_operation_type.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html#t6"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html">apps\audit\migrations\0003_add_error_operation_type.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1eec6a31ccdfc5bd___init___py.html">apps\audit\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_1eec6a31ccdfc5bd___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t7">apps\audit\models.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t7"><data value='OperationLog'>OperationLog</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t37">apps\audit\models.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html#t37"><data value='Meta'>OperationLog.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html">apps\audit\models.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t11">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t11"><data value='OperationLogSerializer'>OperationLogSerializer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t19">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t19"><data value='Meta'>OperationLogSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t34">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t34"><data value='OperationLogListSerializer'>OperationLogListSerializer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t41">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t41"><data value='Meta'>OperationLogListSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t54">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t54"><data value='OperationLogDetailSerializer'>OperationLogDetailSerializer</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t62">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t62"><data value='Meta'>OperationLogDetailSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t141">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t141"><data value='OperationLogExportSerializer'>OperationLogExportSerializer</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t169">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html#t169"><data value='OperationLogStatsSerializer'>OperationLogStatsSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html">apps\audit\serializers.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_serializers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t16">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t16"><data value='ExceptionLogService'>ExceptionLogService</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t163">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html#t163"><data value='AuditLogService'>AuditLogService</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html">apps\audit\services.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html">apps\audit\tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tasks_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t25">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html#t25"><data value='AuditTasksTest'>AuditTasksTest</data></a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html">apps\audit\test_tasks.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_test_tasks_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t23">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t23"><data value='OperationLogModelTest'>OperationLogModelTest</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t106">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t106"><data value='ExceptionLogServiceTest'>ExceptionLogServiceTest</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t170">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t170"><data value='AuditLogServiceTest'>AuditLogServiceTest</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t280">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t280"><data value='AuditLogMiddlewareTest'>AuditLogMiddlewareTest</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t329">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t329"><data value='OperationLogViewSetTest'>OperationLogViewSetTest</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t446">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html#t446"><data value='AuditLogMiddlewareIntegrationTest'>AuditLogMiddlewareIntegrationTest</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html">apps\audit\tests.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_urls_py.html">apps\audit\urls.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t32">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html#t32"><data value='OperationLogViewSet'>OperationLogViewSet</data></a></td>
                <td>169</td>
                <td>169</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html">apps\audit\views.py</a></td>
                <td class="name left"><a href="z_bf7680af20f2e250_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa___init___py.html">apps\authentication\__init__.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t21">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html#t21"><data value='LoginAnomalyDetector'>LoginAnomalyDetector</data></a></td>
                <td>192</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="0 192">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html">apps\authentication\anomaly_detector.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_anomaly_detector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_apps_py.html#t4">apps\authentication\apps.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_apps_py.html#t4"><data value='AuthenticationConfig'>AuthenticationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_apps_py.html">apps\authentication\apps.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91a8e3ad6c12af09___init___py.html">apps\authentication\management\__init__.py</a></td>
                <td class="name left"><a href="z_91a8e3ad6c12af09___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458___init___py.html">apps\authentication\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_0d354c5377fd6458___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html#t10">apps\authentication\management\commands\cleanup_auth.py</a></td>
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html#t10"><data value='Command'>Command</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html">apps\authentication\management\commands\cleanup_auth.py</a></td>
                <td class="name left"><a href="z_0d354c5377fd6458_cleanup_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0001_initial_py.html#t6">apps\authentication\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0001_initial_py.html#t6"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0001_initial_py.html">apps\authentication\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0002_initial_py.html#t8">apps\authentication\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0002_initial_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0002_initial_py.html">apps\authentication\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0002_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html#t6">apps\authentication\migrations\0003_simplecaptcha.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html#t6"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html">apps\authentication\migrations\0003_simplecaptcha.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html#t8">apps\authentication\migrations\0004_loginattempt_ipwhitelist.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html">apps\authentication\migrations\0004_loginattempt_ipwhitelist.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html#t6">apps\authentication\migrations\0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html#t6"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html">apps\authentication\migrations\0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b44c3f9d3a575f9___init___py.html">apps\authentication\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_9b44c3f9d3a575f9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t13">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t13"><data value='UserSession'>UserSession</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t38">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t38"><data value='Meta'>UserSession.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t51">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t51"><data value='SimpleCaptcha'>SimpleCaptcha</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t58">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t58"><data value='Meta'>SimpleCaptcha.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t117">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t117"><data value='IPWhitelist'>IPWhitelist</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t125">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t125"><data value='Meta'>IPWhitelist.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t147">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t147"><data value='LoginAttempt'>LoginAttempt</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t159">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t159"><data value='Meta'>LoginAttempt.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t177">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t177"><data value='SecurityAlert'>SecurityAlert</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t232">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t232"><data value='Meta'>SecurityAlert.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t249">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t249"><data value='DeviceFingerprint'>DeviceFingerprint</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t282">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html#t282"><data value='Meta'>DeviceFingerprint.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html">apps\authentication\models.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>133</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="133 133">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t16">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html#t16"><data value='SecurityService'>SecurityService</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html">apps\authentication\security.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t12">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t12"><data value='CaptchaSerializer'>CaptchaSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t18">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t18"><data value='LoginSerializer'>LoginSerializer</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t89">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t89"><data value='TokenRefreshSerializer'>TokenRefreshSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t94">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t94"><data value='LogoutSerializer'>LogoutSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t99">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t99"><data value='UserSessionSerializer'>UserSessionSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t103">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html#t103"><data value='Meta'>UserSessionSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html">apps\authentication\serializers.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_serializers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t16">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t16"><data value='CaptchaService'>CaptchaService</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t34">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t34"><data value='SessionService'>SessionService</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t138">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html#t138"><data value='AuthenticationService'>AuthenticationService</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html">apps\authentication\services.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html">apps\authentication\session_views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_session_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html">apps\authentication\tasks.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tasks_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t15">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t15"><data value='CaptchaServiceTestCase'>CaptchaServiceTestCase</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t39">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t39"><data value='AuthenticationAPITestCase'>AuthenticationAPITestCase</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t140">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t140"><data value='SessionServiceTestCase'>SessionServiceTestCase</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t172">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html#t172"><data value='AccountLockTestCase'>AccountLockTestCase</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html">apps\authentication\tests.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_urls_py.html">apps\authentication\urls.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t204">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html#t204"><data value='UserSessionViewSet'>UserSessionViewSet</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html">apps\authentication\views.py</a></td>
                <td class="name left"><a href="z_4944ad59518994fa_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0___init___py.html">apps\backup\__init__.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_apps_py.html#t7">apps\backup\apps.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_apps_py.html#t7"><data value='BackupConfig'>BackupConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_apps_py.html">apps\backup\apps.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac143cddc957a9a8_0001_initial_py.html#t8">apps\backup\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_ac143cddc957a9a8_0001_initial_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac143cddc957a9a8_0001_initial_py.html">apps\backup\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_ac143cddc957a9a8_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac143cddc957a9a8___init___py.html">apps\backup\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_ac143cddc957a9a8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t11">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t11"><data value='BackupJob'>BackupJob</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t77">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t77"><data value='Meta'>BackupJob.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t120">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t120"><data value='RestoreJob'>RestoreJob</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t178">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t178"><data value='Meta'>RestoreJob.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t193">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t193"><data value='BackupSchedule'>BackupSchedule</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t233">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t233"><data value='Meta'>BackupSchedule.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t247">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t247"><data value='BackupStorage'>BackupStorage</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t278">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html#t278"><data value='Meta'>BackupStorage.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html">apps\backup\models.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>117</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="117 117">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t24">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t24"><data value='BackupService'>BackupService</data></a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t286">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html#t286"><data value='RestoreService'>RestoreService</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html">apps\backup\services.py</a></td>
                <td class="name left"><a href="z_747ca389d9c344c0_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5___init___py.html">apps\common\__init__.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_apps_py.html#t4">apps\common\apps.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_apps_py.html#t4"><data value='CommonConfig'>CommonConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_apps_py.html">apps\common\apps.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t16">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html#t16"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html">apps\common\config_manager.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html">apps\common\config_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_config_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>189</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t106">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html#t106"><data value='DataScopeMixin'>DataScopeMixin</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html">apps\common\decorators.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t16">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html#t16"><data value='UserDepartmentService'>UserDepartmentService</data></a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html">apps\common\department_service.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_department_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t16">apps\common\example_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html#t16"><data value='ExceptionDemoView'>ExceptionDemoView</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html">apps\common\example_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_example_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t25">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t25"><data value='ExampleAPIView'>ExampleAPIView</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t231">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html#t231"><data value='BestPracticeView'>BestPracticeView</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html">apps\common\examples.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_examples_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t17">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t17"><data value='ErrorCode'>ErrorCode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t91">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html#t91"><data value='BusinessException'>BusinessException</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html">apps\common\exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t18">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t18"><data value='FileSecurityValidator'>FileSecurityValidator</data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t293">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html#t293"><data value='SecureFileUploadHandler'>SecureFileUploadHandler</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html">apps\common\file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html">apps\common\file_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_file_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_789b0fc39a74c14f___init___py.html">apps\common\management\__init__.py</a></td>
                <td class="name left"><a href="z_789b0fc39a74c14f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8___init___py.html">apps\common\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t16">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html#t16"><data value='Command'>Command</data></a></td>
                <td>108</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="0 108">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html">apps\common\management\commands\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2b6b2d63c2fb0d8_test_data_scope_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t15">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t15"><data value='JWTAuthenticationMiddleware'>JWTAuthenticationMiddleware</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t79">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t79"><data value='PermissionMiddleware'>PermissionMiddleware</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t137">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html#t137"><data value='AuditLogMiddleware'>AuditLogMiddleware</data></a></td>
                <td>146</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html">apps\common\middleware.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_middleware_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t8">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t8"><data value='BaseModel'>BaseModel</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t15">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t15"><data value='Meta'>BaseModel.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t31">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html#t31"><data value='ActiveManager'>ActiveManager</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html">apps\common\models.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t15">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html#t15"><data value='DataScopeService'>DataScopeService</data></a></td>
                <td>296</td>
                <td>296</td>
                <td>0</td>
                <td class="right" data-ratio="0 296">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html">apps\common\permissions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_permissions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t10">apps\common\response.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html#t10"><data value='ApiResponse'>ApiResponse</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html">apps\common\response.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_response_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t29">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t29"><data value='ErrorCodeTestCase'>ErrorCodeTestCase</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t61">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t61"><data value='BusinessExceptionTestCase'>BusinessExceptionTestCase</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t92">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t92"><data value='CustomExceptionHandlerTestCase'>CustomExceptionHandlerTestCase</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t232">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t232"><data value='HelperFunctionsTestCase'>HelperFunctionsTestCase</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t283">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t283"><data value='TestAPIView'>TestAPIView</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t306">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t306"><data value='ExceptionHandlerIntegrationTestCase'>ExceptionHandlerIntegrationTestCase</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t376">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html#t376"><data value='ExceptionLoggingTestCase'>ExceptionLoggingTestCase</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html">apps\common\test_exceptions.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t18">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t18"><data value='FileSecurityValidatorTest'>FileSecurityValidatorTest</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t102">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t102"><data value='SecureFileUploadHandlerTest'>SecureFileUploadHandlerTest</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t140">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t140"><data value='FileUploadAPITest'>FileUploadAPITest</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t258">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html#t258"><data value='FileSecurityIntegrationTest'>FileSecurityIntegrationTest</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html">apps\common\test_file_security.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_file_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t14">apps\common\test_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html#t14"><data value='TestExceptionView'>TestExceptionView</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html">apps\common\test_views.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_test_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t18">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t18"><data value='ApiResponseTestCase'>ApiResponseTestCase</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t81">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t81"><data value='ErrorCodeTestCase'>ErrorCodeTestCase</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t107">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t107"><data value='BusinessExceptionTestCase'>BusinessExceptionTestCase</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t138">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t138"><data value='CustomExceptionHandlerTestCase'>CustomExceptionHandlerTestCase</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t245">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t245"><data value='ExceptionHandlerIntegrationTestCase'>ExceptionHandlerIntegrationTestCase</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t263">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t263"><data value='UtilsTestCase'>UtilsTestCase</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t365">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html#t365"><data value='ResponseHelperTestCase'>ResponseHelperTestCase</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html">apps\common\tests.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6___init___py.html">apps\common\tests\__init__.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t19">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t19"><data value='DataScopeServiceTest'>DataScopeServiceTest</data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t253">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t253"><data value='UserDepartmentServiceTest'>UserDepartmentServiceTest</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t330">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t330"><data value='DataScopeMixinTest'>DataScopeMixinTest</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t393">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html#t393"><data value='DataScopeIntegrationTest'>DataScopeIntegrationTest</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html">apps\common\tests\test_data_scope.py</a></td>
                <td class="name left"><a href="z_e2d79787564ab1b6_test_data_scope_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_urls_py.html">apps\common\urls.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t97">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html#t97"><data value='ResponseHelper'>ResponseHelper</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html">apps\common\utils.py</a></td>
                <td class="name left"><a href="z_ecc233269ded7be5_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c___init___py.html">apps\departments\__init__.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_apps_py.html#t4">apps\departments\apps.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_apps_py.html#t4"><data value='DepartmentsConfig'>DepartmentsConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_apps_py.html">apps\departments\apps.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0001_initial_py.html#t8">apps\departments\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7_0001_initial_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0001_initial_py.html">apps\departments\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0002_initial_py.html#t9">apps\departments\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7_0002_initial_py.html#t9"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7_0002_initial_py.html">apps\departments\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7_0002_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1fbaf518d27a7___init___py.html">apps\departments\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_93b1fbaf518d27a7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t15">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t15"><data value='Department'>Department</data></a></td>
                <td>6</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="1 6">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t31">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t31"><data value='MPTTMeta'>Department.MPTTMeta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t34">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t34"><data value='Meta'>Department.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t61">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t61"><data value='UserDepartment'>UserDepartment</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t95">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html#t95"><data value='Meta'>UserDepartment.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html">apps\departments\models.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="48 49">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t10">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t10"><data value='DepartmentSerializer'>DepartmentSerializer</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t30">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t30"><data value='Meta'>DepartmentSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t88">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t88"><data value='DepartmentTreeSerializer'>DepartmentTreeSerializer</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t95">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t95"><data value='Meta'>DepartmentTreeSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t124">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t124"><data value='DepartmentListSerializer'>DepartmentListSerializer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t130">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t130"><data value='Meta'>DepartmentListSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t143">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t143"><data value='UserDepartmentSerializer'>UserDepartmentSerializer</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t153">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t153"><data value='Meta'>UserDepartmentSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t247">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t247"><data value='DepartmentMemberSerializer'>DepartmentMemberSerializer</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t289">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t289"><data value='DepartmentPathSerializer'>DepartmentPathSerializer</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t295">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html#t295"><data value='Meta'>DepartmentPathSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html">apps\departments\serializers.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_serializers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t17">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t17"><data value='DepartmentModelTestCase'>DepartmentModelTestCase</data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t179">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html#t179"><data value='DepartmentAPITestCase'>DepartmentAPITestCase</data></a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html">apps\departments\tests.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_urls_py.html">apps\departments\urls.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t19">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html#t19"><data value='DepartmentViewSet'>DepartmentViewSet</data></a></td>
                <td>197</td>
                <td>197</td>
                <td>0</td>
                <td class="right" data-ratio="0 197">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html">apps\departments\views.py</a></td>
                <td class="name left"><a href="z_c79fd428a38de31c_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358___init___py.html">apps\health\__init__.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_apps_py.html#t7">apps\health\apps.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_apps_py.html#t7"><data value='HealthConfig'>HealthConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_apps_py.html">apps\health\apps.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_urls_py.html">apps\health\urls.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html">apps\health\views.py</a></td>
                <td class="name left"><a href="z_b165ec8e0f80a358_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>148</td>
                <td>148</td>
                <td>0</td>
                <td class="right" data-ratio="0 148">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc___init___py.html">apps\monitoring\__init__.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html#t7">apps\monitoring\apps.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html#t7"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html">apps\monitoring\apps.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_62e9cac5df6ba8e3_0001_initial_py.html#t9">apps\monitoring\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_62e9cac5df6ba8e3_0001_initial_py.html#t9"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_62e9cac5df6ba8e3_0001_initial_py.html">apps\monitoring\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_62e9cac5df6ba8e3_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_62e9cac5df6ba8e3___init___py.html">apps\monitoring\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_62e9cac5df6ba8e3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t10">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t10"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t42">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t42"><data value='Meta'>SystemMetrics.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t58">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t58"><data value='ApplicationMetrics'>ApplicationMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t92">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t92"><data value='Meta'>ApplicationMetrics.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t107">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t107"><data value='ServiceHealthCheck'>ServiceHealthCheck</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t138">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t138"><data value='Meta'>ServiceHealthCheck.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t153">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t153"><data value='AlertRule'>AlertRule</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t202">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t202"><data value='Meta'>AlertRule.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t215">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t215"><data value='AlertRecord'>AlertRecord</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t259">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html#t259"><data value='Meta'>AlertRecord.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html">apps\monitoring\models.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>108</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="108 108">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t22">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t22"><data value='SystemMonitorService'>SystemMonitorService</data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t240">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t240"><data value='HealthCheckService'>HealthCheckService</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t420">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html#t420"><data value='AlertService'>AlertService</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html">apps\monitoring\services.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html">apps\monitoring\tasks.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_tasks_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_urls_py.html">apps\monitoring\urls.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html">apps\monitoring\views.py</a></td>
                <td class="name left"><a href="z_56e7f2aa79f1a3bc_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f___init___py.html">apps\permissions\__init__.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_apps_py.html#t4">apps\permissions\apps.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_apps_py.html#t4"><data value='PermissionsConfig'>PermissionsConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_apps_py.html">apps\permissions\apps.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t218">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html#t218"><data value='PermissionMixin'>PermissionMixin</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html">apps\permissions\decorators.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e434bc180f346938___init___py.html">apps\permissions\management\__init__.py</a></td>
                <td class="name left"><a href="z_e434bc180f346938___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69___init___py.html">apps\permissions\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t8">apps\permissions\management\commands\init_permissions.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html#t8"><data value='Command'>Command</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html">apps\permissions\management\commands\init_permissions.py</a></td>
                <td class="name left"><a href="z_40492a24c6ed3a69_init_permissions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0001_initial_py.html#t7">apps\permissions\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33_0001_initial_py.html#t7"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0001_initial_py.html">apps\permissions\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0002_initial_py.html#t8">apps\permissions\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33_0002_initial_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33_0002_initial_py.html">apps\permissions\migrations\0002_initial.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33_0002_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39828321a89c5c33___init___py.html">apps\permissions\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_39828321a89c5c33___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t8">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t8"><data value='Role'>Role</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t36">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t36"><data value='Meta'>Role.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t45">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t45"><data value='Permission'>Permission</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t76">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t76"><data value='Meta'>Permission.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t89">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t89"><data value='UserRole'>UserRole</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t100">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html#t100"><data value='Meta'>UserRole.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html">apps\permissions\models.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t12">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t12"><data value='PermissionSerializer'>PermissionSerializer</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t22">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t22"><data value='Meta'>PermissionSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t69">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t69"><data value='PermissionTreeSerializer'>PermissionTreeSerializer</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t74">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t74"><data value='Meta'>PermissionTreeSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t87">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t87"><data value='RoleSerializer'>RoleSerializer</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t102">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t102"><data value='Meta'>RoleSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t161">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t161"><data value='UserRoleSerializer'>UserRoleSerializer</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t171">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t171"><data value='Meta'>UserRoleSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t235">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t235"><data value='RoleAssignmentSerializer'>RoleAssignmentSerializer</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t274">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html#t274"><data value='UserPermissionSerializer'>UserPermissionSerializer</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html">apps\permissions\serializers.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_serializers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t14">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t14"><data value='PermissionService'>PermissionService</data></a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t285">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html#t285"><data value='RoleService'>RoleService</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html">apps\permissions\services.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t16">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t16"><data value='PermissionModelTest'>PermissionModelTest</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t48">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t48"><data value='RoleModelTest'>RoleModelTest</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t82">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t82"><data value='UserRoleModelTest'>UserRoleModelTest</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t123">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t123"><data value='PermissionServiceTest'>PermissionServiceTest</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t209">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t209"><data value='RoleServiceTest'>RoleServiceTest</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t245">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t245"><data value='PermissionAPITest'>PermissionAPITest</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t311">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html#t311"><data value='RoleAPITest'>RoleAPITest</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html">apps\permissions\tests.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_urls_py.html">apps\permissions\urls.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t23">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t23"><data value='PermissionViewSet'>PermissionViewSet</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t143">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t143"><data value='RoleViewSet'>RoleViewSet</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t313">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html#t313"><data value='UserRoleViewSet'>UserRoleViewSet</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html">apps\permissions\views.py</a></td>
                <td class="name left"><a href="z_078d55d899ba2a2f_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d___init___py.html">apps\users\__init__.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_apps_py.html#t4">apps\users\apps.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_apps_py.html#t4"><data value='UsersConfig'>UsersConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_apps_py.html">apps\users\apps.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0001_initial_py.html#t7">apps\users\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb_0001_initial_py.html#t7"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0001_initial_py.html">apps\users\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html#t8">apps\users\migrations\0002_alter_userprofile_managers.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html">apps\users\migrations\0002_alter_userprofile_managers.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a9905534d2b557cb___init___py.html">apps\users\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_a9905534d2b557cb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t9">apps\users\models.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t9"><data value='ActiveUserManager'>ActiveUserManager</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t15">apps\users\models.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t15"><data value='UserProfile'>UserProfile</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t40">apps\users\models.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html#t40"><data value='Meta'>UserProfile.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html">apps\users\models.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t12">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t12"><data value='UserSerializer'>UserSerializer</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t27">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t27"><data value='Meta'>UserSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t90">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t90"><data value='UserCreateSerializer'>UserCreateSerializer</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t96">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t96"><data value='Meta'>UserCreateSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t167">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t167"><data value='UserProfileSerializer'>UserProfileSerializer</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t170">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t170"><data value='Meta'>UserProfileSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t203">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t203"><data value='PasswordResetSerializer'>PasswordResetSerializer</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t230">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t230"><data value='ChangePasswordSerializer'>ChangePasswordSerializer</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t265">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t265"><data value='UserListSerializer'>UserListSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t268">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html#t268"><data value='Meta'>UserListSerializer.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html">apps\users\serializers.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_serializers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t16">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t16"><data value='UserModelTestCase'>UserModelTestCase</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t83">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html#t83"><data value='UserAPITestCase'>UserAPITestCase</data></a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html">apps\users\tests.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_urls_py.html">apps\users\urls.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t21">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html#t21"><data value='UserViewSet'>UserViewSet</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html">apps\users\views.py</a></td>
                <td class="name left"><a href="z_e7a2fb5ce97bac5d_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8449</td>
                <td>7783</td>
                <td>0</td>
                <td class="right" data-ratio="666 8449">8%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
