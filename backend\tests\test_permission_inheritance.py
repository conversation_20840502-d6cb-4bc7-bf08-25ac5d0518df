"""
权限继承机制完整测试
测试多级权限继承、权限树遍历、缓存机制
"""
import pytest
from django.test import TestCase
from apps.permissions.models import Permission, Role, UserRole
from apps.permissions.services import PermissionService
from apps.users.models import UserDepartment
from tests.factories import UserProfileFactory, DepartmentFactory, RoleFactory, PermissionFactory


@pytest.mark.django_db
@pytest.mark.unit
class TestPermissionInheritanceComplete(TestCase):
    """完整权限继承测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.user = UserProfileFactory()
        self.department = DepartmentFactory()
        self.role = RoleFactory()
        
        # 建立用户部门关系
        UserDepartment.objects.create(
            user=self.user,
            department=self.department,
            is_primary=True,
            is_active=True
        )
        
        # 建立用户角色关系
        UserRole.objects.create(
            user=self.user,
            role=self.role,
            department=self.department
        )
    
    def test_single_level_inheritance(self):
        """测试一级权限继承"""
        # 创建父子权限
        parent = PermissionFactory(code='system:admin', name='系统管理')
        child = PermissionFactory(code='system:user', name='用户管理')
        child.parent = parent
        child.save()
        
        # 角色分配父权限
        self.role.permissions.add(parent)
        
        # 测试权限检查
        has_parent = PermissionService.check_user_permission(self.user, 'system:admin')
        has_child = PermissionService.check_user_permission(self.user, 'system:user')
        
        self.assertTrue(has_parent)
        self.assertTrue(has_child)  # 应该继承父权限
    
    def test_multi_level_inheritance(self):
        """测试多级权限继承"""
        # 创建三级权限结构
        level1 = PermissionFactory(code='system', name='系统')
        level2 = PermissionFactory(code='system:admin', name='系统管理')
        level3 = PermissionFactory(code='system:admin:user', name='用户管理')
        level4 = PermissionFactory(code='system:admin:user:create', name='创建用户')
        
        # 建立层级关系
        level2.parent = level1
        level2.save()
        level3.parent = level2
        level3.save()
        level4.parent = level3
        level4.save()
        
        # 角色只分配顶级权限
        self.role.permissions.add(level1)
        
        # 测试所有级别的权限继承
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system'))
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:admin'))
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:admin:user'))
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:admin:user:create'))
    
    def test_permission_tree_methods(self):
        """测试权限树方法"""
        # 创建权限树
        root = PermissionFactory(code='root', name='根权限')
        child1 = PermissionFactory(code='child1', name='子权限1')
        child2 = PermissionFactory(code='child2', name='子权限2')
        grandchild = PermissionFactory(code='grandchild', name='孙子权限')
        
        # 建立关系
        child1.parent = root
        child1.save()
        child2.parent = root
        child2.save()
        grandchild.parent = child1
        grandchild.save()
        
        # 测试get_descendants
        descendants = root.get_descendants()
        self.assertEqual(len(descendants), 3)
        self.assertIn(child1, descendants)
        self.assertIn(child2, descendants)
        self.assertIn(grandchild, descendants)
        
        # 测试get_ancestors
        ancestors = grandchild.get_ancestors()
        self.assertEqual(len(ancestors), 2)
        self.assertIn(child1, ancestors)
        self.assertIn(root, ancestors)
        
        # 测试is_descendant_of
        self.assertTrue(grandchild.is_descendant_of(root))
        self.assertTrue(grandchild.is_descendant_of(child1))
        self.assertFalse(grandchild.is_descendant_of(child2))
        
        # 测试is_ancestor_of
        self.assertTrue(root.is_ancestor_of(grandchild))
        self.assertTrue(child1.is_ancestor_of(grandchild))
        self.assertFalse(child2.is_ancestor_of(grandchild))
        
        # 测试get_permission_path
        path = grandchild.get_permission_path()
        self.assertEqual(len(path), 3)
        self.assertEqual(path[0], root)
        self.assertEqual(path[1], child1)
        self.assertEqual(path[2], grandchild)
        
        # 测试get_level
        self.assertEqual(root.get_level(), 0)
        self.assertEqual(child1.get_level(), 1)
        self.assertEqual(grandchild.get_level(), 2)
    
    def test_complex_inheritance_scenario(self):
        """测试复杂权限继承场景"""
        # 创建复杂的权限树
        # 系统管理
        system = PermissionFactory(code='system', name='系统管理')
        
        # 用户管理分支
        user_mgmt = PermissionFactory(code='system:user', name='用户管理')
        user_mgmt.parent = system
        user_mgmt.save()
        
        user_create = PermissionFactory(code='system:user:create', name='创建用户')
        user_create.parent = user_mgmt
        user_create.save()
        
        user_edit = PermissionFactory(code='system:user:edit', name='编辑用户')
        user_edit.parent = user_mgmt
        user_edit.save()
        
        # 角色管理分支
        role_mgmt = PermissionFactory(code='system:role', name='角色管理')
        role_mgmt.parent = system
        role_mgmt.save()
        
        role_assign = PermissionFactory(code='system:role:assign', name='分配角色')
        role_assign.parent = role_mgmt
        role_assign.save()
        
        # 测试场景1：分配中级权限
        self.role.permissions.clear()
        self.role.permissions.add(user_mgmt)
        
        # 应该有用户管理及其子权限
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:user'))
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:user:create'))
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:user:edit'))
        
        # 不应该有其他分支的权限
        self.assertFalse(PermissionService.check_user_permission(self.user, 'system'))
        self.assertFalse(PermissionService.check_user_permission(self.user, 'system:role'))
        self.assertFalse(PermissionService.check_user_permission(self.user, 'system:role:assign'))
        
        # 测试场景2：分配顶级权限
        self.role.permissions.clear()
        self.role.permissions.add(system)
        
        # 应该有所有权限
        all_permissions = [
            'system', 'system:user', 'system:user:create', 'system:user:edit',
            'system:role', 'system:role:assign'
        ]
        for perm_code in all_permissions:
            self.assertTrue(
                PermissionService.check_user_permission(self.user, perm_code),
                f"应该有权限: {perm_code}"
            )
    
    def test_inheritance_with_multiple_roles(self):
        """测试多角色权限继承"""
        # 创建权限树
        system = PermissionFactory(code='system', name='系统管理')
        user_mgmt = PermissionFactory(code='system:user', name='用户管理')
        role_mgmt = PermissionFactory(code='system:role', name='角色管理')
        
        user_mgmt.parent = system
        user_mgmt.save()
        role_mgmt.parent = system
        role_mgmt.save()
        
        # 创建多个角色
        role1 = RoleFactory(name='用户管理员')
        role2 = RoleFactory(name='角色管理员')
        
        role1.permissions.add(user_mgmt)
        role2.permissions.add(role_mgmt)
        
        # 用户拥有多个角色
        UserRole.objects.create(user=self.user, role=role1, department=self.department)
        UserRole.objects.create(user=self.user, role=role2, department=self.department)
        
        # 应该拥有两个分支的权限
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:user'))
        self.assertTrue(PermissionService.check_user_permission(self.user, 'system:role'))
        
        # 但不应该有顶级权限（因为没有直接分配）
        self.assertFalse(PermissionService.check_user_permission(self.user, 'system'))
    
    def test_inheritance_performance(self):
        """测试权限继承性能"""
        # 创建深层权限树（10级）
        permissions = []
        parent = None
        
        for i in range(10):
            perm = PermissionFactory(code=f'level{i}', name=f'级别{i}')
            if parent:
                perm.parent = parent
                perm.save()
            permissions.append(perm)
            parent = perm
        
        # 分配顶级权限
        self.role.permissions.add(permissions[0])
        
        # 测试深层权限检查（应该很快完成）
        import time
        start_time = time.time()
        
        # 检查最深层权限
        has_deep_permission = PermissionService.check_user_permission(
            self.user, 'level9'
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.assertTrue(has_deep_permission)
        self.assertLess(execution_time, 0.1)  # 应该在100ms内完成
    
    def test_circular_reference_protection(self):
        """测试循环引用保护"""
        # 创建两个权限
        perm1 = PermissionFactory(code='perm1', name='权限1')
        perm2 = PermissionFactory(code='perm2', name='权限2')
        
        # 尝试创建循环引用
        perm1.parent = perm2
        perm1.save()
        perm2.parent = perm1
        perm2.save()
        
        # 权限继承方法应该能处理循环引用而不会无限循环
        try:
            ancestors = perm1.get_ancestors()
            descendants = perm1.get_descendants()
            # 如果没有无限循环，测试通过
            self.assertTrue(True)
        except RecursionError:
            self.fail("权限继承方法应该能处理循环引用")


@pytest.mark.django_db
@pytest.mark.integration
class TestPermissionInheritanceIntegration(TestCase):
    """权限继承集成测试"""
    
    def test_real_world_permission_structure(self):
        """测试真实世界的权限结构"""
        # 模拟真实的企业权限结构
        
        # 顶级模块
        system = PermissionFactory(code='system', name='系统管理')
        business = PermissionFactory(code='business', name='业务管理')
        
        # 系统管理子模块
        user_mgmt = PermissionFactory(code='system:user', name='用户管理')
        user_mgmt.parent = system
        user_mgmt.save()
        
        dept_mgmt = PermissionFactory(code='system:dept', name='部门管理')
        dept_mgmt.parent = system
        dept_mgmt.save()
        
        # 用户管理具体操作
        user_view = PermissionFactory(code='system:user:view', name='查看用户')
        user_view.parent = user_mgmt
        user_view.save()
        
        user_create = PermissionFactory(code='system:user:create', name='创建用户')
        user_create.parent = user_mgmt
        user_create.save()
        
        user_edit = PermissionFactory(code='system:user:edit', name='编辑用户')
        user_edit.parent = user_mgmt
        user_edit.save()
        
        user_delete = PermissionFactory(code='system:user:delete', name='删除用户')
        user_delete.parent = user_mgmt
        user_delete.save()
        
        # 创建不同级别的角色
        super_admin = RoleFactory(name='超级管理员')
        user_admin = RoleFactory(name='用户管理员')
        user_viewer = RoleFactory(name='用户查看员')
        
        # 分配权限
        super_admin.permissions.add(system)  # 顶级权限
        user_admin.permissions.add(user_mgmt)  # 模块权限
        user_viewer.permissions.add(user_view)  # 操作权限
        
        # 创建用户并分配角色
        super_user = UserProfileFactory()
        admin_user = UserProfileFactory()
        viewer_user = UserProfileFactory()
        
        department = DepartmentFactory()
        
        # 建立用户部门关系
        for user in [super_user, admin_user, viewer_user]:
            UserDepartment.objects.create(
                user=user, department=department, is_primary=True, is_active=True
            )
        
        # 分配角色
        UserRole.objects.create(user=super_user, role=super_admin, department=department)
        UserRole.objects.create(user=admin_user, role=user_admin, department=department)
        UserRole.objects.create(user=viewer_user, role=user_viewer, department=department)
        
        # 测试超级管理员权限
        super_permissions = [
            'system', 'system:user', 'system:dept',
            'system:user:view', 'system:user:create', 'system:user:edit', 'system:user:delete'
        ]
        for perm in super_permissions:
            self.assertTrue(
                PermissionService.check_user_permission(super_user, perm),
                f"超级管理员应该有权限: {perm}"
            )
        
        # 测试用户管理员权限
        admin_permissions = [
            'system:user', 'system:user:view', 'system:user:create', 
            'system:user:edit', 'system:user:delete'
        ]
        for perm in admin_permissions:
            self.assertTrue(
                PermissionService.check_user_permission(admin_user, perm),
                f"用户管理员应该有权限: {perm}"
            )
        
        # 用户管理员不应该有系统级权限
        self.assertFalse(PermissionService.check_user_permission(admin_user, 'system'))
        self.assertFalse(PermissionService.check_user_permission(admin_user, 'system:dept'))
        
        # 测试查看员权限
        self.assertTrue(PermissionService.check_user_permission(viewer_user, 'system:user:view'))
        
        # 查看员不应该有其他权限
        viewer_no_permissions = [
            'system', 'system:user', 'system:dept',
            'system:user:create', 'system:user:edit', 'system:user:delete'
        ]
        for perm in viewer_no_permissions:
            self.assertFalse(
                PermissionService.check_user_permission(viewer_user, perm),
                f"查看员不应该有权限: {perm}"
            )
