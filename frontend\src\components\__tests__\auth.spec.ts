/**
 * 认证功能测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { isTokenExpired, getTokenRemainingTime, formatErrorMessage } from '@/utils/auth'

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() }
      },
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn()
    }))
  }
}))

// Mock auth store to avoid circular dependency
vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn(() => ({
    token: '',
    refreshToken: '',
    userInfo: null,
    permissions: [],
    isLoggedIn: false,
    hasPermission: vi.fn(),
    login: vi.fn(),
    logout: vi.fn(),
    refreshTokenAction: vi.fn(),
    initializeAuth: vi.fn()
  }))
}))

describe('认证工具函数', () => {
  describe('isTokenExpired', () => {
    it('应该正确检测过期的令牌', () => {
      // 创建一个过期的JWT令牌（exp时间戳为过去时间）
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid'
      expect(isTokenExpired(expiredToken)).toBe(true)
    })

    it('应该正确处理无效的令牌', () => {
      expect(isTokenExpired('')).toBe(true)
      expect(isTokenExpired('invalid-token')).toBe(true)
    })
  })

  describe('getTokenRemainingTime', () => {
    it('应该返回0对于过期的令牌', () => {
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid'
      expect(getTokenRemainingTime(expiredToken)).toBe(0)
    })

    it('应该返回0对于无效的令牌', () => {
      expect(getTokenRemainingTime('')).toBe(0)
      expect(getTokenRemainingTime('invalid')).toBe(0)
    })
  })

  describe('formatErrorMessage', () => {
    it('应该格式化API错误响应', () => {
      const error = {
        response: {
          data: {
            message: '登录失败'
          }
        }
      }
      expect(formatErrorMessage(error)).toBe('登录失败')
    })

    it('应该格式化验证错误', () => {
      const error = {
        response: {
          data: {
            details: {
              username: ['用户名不能为空'],
              password: ['密码不能为空']
            }
          }
        }
      }
      expect(formatErrorMessage(error)).toBe('用户名不能为空, 密码不能为空')
    })

    it('应该处理普通错误', () => {
      const error = new Error('网络错误')
      expect(formatErrorMessage(error)).toBe('网络错误')
    })

    it('应该处理未知错误', () => {
      const error = {}
      expect(formatErrorMessage(error)).toBe('未知错误')
    })
  })
})

describe('认证状态管理', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('应该正确初始化状态', async () => {
    // 由于我们mock了useAuthStore，这里只是测试mock是否正常工作
    const { useAuthStore } = await import('@/stores/auth')
    expect(vi.isMockFunction(useAuthStore)).toBe(true)
  })
})
