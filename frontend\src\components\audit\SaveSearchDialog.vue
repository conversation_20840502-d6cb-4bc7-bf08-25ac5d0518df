<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="保存搜索条件"
    class="save-search-dialog"
    style="width: 500px"
    @update:show="$emit('update:visible', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="80px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="搜索名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="输入搜索名称"
        />
      </n-form-item>
      
      <n-form-item label="搜索条件">
        <div class="search-conditions">
          <div v-if="hasConditions" class="condition-list">
            <div
              v-for="condition in conditionSummary"
              :key="condition.key"
              class="condition-item"
            >
              <span class="condition-label">{{ condition.label }}:</span>
              <span class="condition-value">{{ condition.value }}</span>
            </div>
          </div>
          <div v-else class="no-conditions">
            <span class="text-gray-500">无搜索条件</span>
          </div>
        </div>
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          保存
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { useAuditStore } from '@/stores/audit'
import type { OperationLogSearchParams } from '@/types/audit'

// Props
interface Props {
  visible: boolean
  searchParams?: OperationLogSearchParams
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const auditStore = useAuditStore()
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)

// 表单数据
const formData = ref({
  name: ''
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入搜索名称', trigger: 'blur' },
    { min: 1, max: 50, message: '搜索名称长度为1-50个字符', trigger: 'blur' }
  ]
}

// 计算属性
const hasConditions = computed(() => {
  return Object.values(props.searchParams).some(value => 
    value !== undefined && value !== null && value !== ''
  )
})

const conditionSummary = computed(() => {
  const summary = []
  
  if (props.searchParams.search) {
    summary.push({
      key: 'search',
      label: '关键词',
      value: props.searchParams.search
    })
  }
  
  if (props.searchParams.start_date) {
    summary.push({
      key: 'start_date',
      label: '开始时间',
      value: new Date(props.searchParams.start_date).toLocaleString('zh-CN')
    })
  }
  
  if (props.searchParams.end_date) {
    summary.push({
      key: 'end_date',
      label: '结束时间',
      value: new Date(props.searchParams.end_date).toLocaleString('zh-CN')
    })
  }
  
  if (props.searchParams.operation_type) {
    const typeLabels: Record<string, string> = {
      'LOGIN': '登录',
      'LOGOUT': '登出',
      'CREATE': '创建',
      'UPDATE': '更新',
      'DELETE': '删除',
      'QUERY': '查询',
      'ERROR': '系统异常'
    }
    summary.push({
      key: 'operation_type',
      label: '操作类型',
      value: typeLabels[props.searchParams.operation_type] || props.searchParams.operation_type
    })
  }
  
  if (props.searchParams.method) {
    summary.push({
      key: 'method',
      label: '请求方法',
      value: props.searchParams.method
    })
  }
  
  if (props.searchParams.ip_address) {
    summary.push({
      key: 'ip_address',
      label: 'IP地址',
      value: props.searchParams.ip_address
    })
  }
  
  if (props.searchParams.user_id) {
    summary.push({
      key: 'user_id',
      label: '用户ID',
      value: props.searchParams.user_id.toString()
    })
  }
  
  if (props.searchParams.status_code) {
    summary.push({
      key: 'status_code',
      label: '状态码',
      value: props.searchParams.status_code.toString()
    })
  }
  
  return summary
})

// 方法
const generateDefaultName = () => {
  const now = new Date()
  const dateStr = now.toLocaleDateString('zh-CN')
  const timeStr = now.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
  
  let name = `搜索_${dateStr}_${timeStr}`
  
  // 如果有特定的搜索条件，生成更有意义的名称
  if (props.searchParams.search) {
    name = `关键词搜索_${props.searchParams.search}_${dateStr}`
  } else if (props.searchParams.operation_type) {
    const typeLabels: Record<string, string> = {
      'LOGIN': '登录',
      'LOGOUT': '登出',
      'CREATE': '创建',
      'UPDATE': '更新',
      'DELETE': '删除',
      'QUERY': '查询',
      'ERROR': '系统异常'
    }
    const typeLabel = typeLabels[props.searchParams.operation_type] || props.searchParams.operation_type
    name = `${typeLabel}操作_${dateStr}`
  } else if (props.searchParams.ip_address) {
    name = `IP搜索_${props.searchParams.ip_address}_${dateStr}`
  }
  
  return name
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 保存搜索条件
    auditStore.saveSearch(formData.value.name, props.searchParams)
    
    message.success('搜索条件保存成功')
    emit('success')
  } catch (error) {
    console.error('保存搜索条件失败:', error)
    message.error('保存搜索条件失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      // 生成默认名称
      formData.value.name = generateDefaultName()
    })
  }
})
</script>

<style scoped>
.save-search-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.search-conditions {
  width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  background: #f9fafb;
}

.condition-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.condition-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
}

.condition-value {
  font-size: 14px;
  color: #1f2937;
  background: white;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.no-conditions {
  text-align: center;
  padding: 16px 0;
}

.text-gray-500 {
  color: #6b7280;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
