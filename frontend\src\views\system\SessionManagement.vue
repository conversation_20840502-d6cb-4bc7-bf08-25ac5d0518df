<template>
  <div class="session-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">会话管理</h1>
          <p class="page-description">管理用户会话和在线状态</p>
        </div>
        
        <!-- 统计信息 -->
        <div class="session-stats">
          <div class="stat-item">
            <span class="stat-label">在线用户:</span>
            <span class="stat-value">{{ sessionStats?.online_users || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">活跃会话:</span>
            <span class="stat-value">{{ sessionStats?.active_sessions || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">今日新增:</span>
            <span class="stat-value">{{ sessionStats?.today_sessions || 0 }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索用户名、IP地址..."
            style="width: 300px"
            clearable
            @update:value="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
          </n-input>
          
          <n-select
            v-model:value="statusFilter"
            placeholder="会话状态"
            style="width: 120px"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="active" label="活跃" />
            <n-option value="inactive" label="非活跃" />
            <n-option value="expired" label="已过期" />
          </n-select>
          
          <n-select
            v-model:value="deviceFilter"
            placeholder="设备类型"
            style="width: 120px"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="desktop" label="桌面端" />
            <n-option value="mobile" label="移动端" />
            <n-option value="tablet" label="平板" />
          </n-select>
        </div>
        
        <div class="toolbar-right">
          <n-button @click="refreshSessions">
            <template #icon>
              <n-icon><RefreshIcon /></n-icon>
            </template>
            刷新
          </n-button>
          
          <n-button
            type="warning"
            @click="showBatchTerminateDialog = true"
          >
            <template #icon>
              <n-icon><LogoutIcon /></n-icon>
            </template>
            批量下线
          </n-button>
          
          <n-button
            type="error"
            @click="cleanupExpiredSessions"
          >
            <template #icon>
              <n-icon><TrashIcon /></n-icon>
            </template>
            清理过期
          </n-button>
        </div>
      </div>

      <!-- 会话列表 -->
      <div class="session-list">
        <n-data-table
          ref="tableRef"
          :columns="tableColumns"
          :data="sessionList"
          :loading="loading"
          :pagination="paginationConfig"
          :row-key="(row: any) => row.id"
          :checked-row-keys="selectedRowKeys"
          @update:checked-row-keys="handleSelectionChange"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
          size="small"
          class="session-table"
        />
      </div>
    </div>

    <!-- 批量下线对话框 -->
    <n-modal
      v-model:show="showBatchTerminateDialog"
      preset="dialog"
      title="批量下线确认"
      positive-text="确认下线"
      negative-text="取消"
      @positive-click="handleBatchTerminate"
    >
      <p>确定要下线选中的 {{ selectedRowKeys.length }} 个会话吗？</p>
      <p class="warning-text">此操作将强制用户重新登录，请谨慎操作。</p>
    </n-modal>

    <!-- 会话详情对话框 -->
    <SessionDetailDialog
      v-model:visible="showDetailDialog"
      :session-id="selectedSessionId"
      @session-terminated="handleSessionTerminated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage, type DataTableColumns } from 'naive-ui'
import {
  SearchIcon,
  RefreshIcon,
  LogoutIcon,
  TrashIcon,
  EyeIcon,
  MapPinIcon,
  DeviceDesktopIcon,
  DeviceMobileIcon,
  DeviceTabletIcon
} from '@vicons/tabler'
import { request } from '@/api'
import SessionDetailDialog from '@/components/session/SessionDetailDialog.vue'

// 状态管理
const message = useMessage()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const deviceFilter = ref('')
const sessionList = ref<any[]>([])
const sessionStats = ref<any>(null)
const selectedRowKeys = ref<string[]>([])
const showBatchTerminateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedSessionId = ref<number | null>(null)

// 分页配置
const paginationConfig = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 表格列配置
const tableColumns: DataTableColumns = [
  {
    type: 'selection',
    width: 50
  },
  {
    title: '用户',
    key: 'user',
    width: 120,
    render: (row: any) => h('div', { class: 'user-info' }, [
      h('div', { class: 'username' }, row.user?.username || '-'),
      h('div', { class: 'nickname' }, row.user?.nickname || '-')
    ])
  },
  {
    title: 'IP地址',
    key: 'ip_address',
    width: 130,
    render: (row: any) => h('span', { class: 'ip-address' }, row.ip_address)
  },
  {
    title: '设备信息',
    key: 'device',
    width: 200,
    render: (row: any) => h('div', { class: 'device-info' }, [
      h('div', { class: 'device-type' }, [
        h('n-icon', { size: 16, class: 'device-icon' }, 
          row.device_type === 'mobile' ? h(DeviceMobileIcon) :
          row.device_type === 'tablet' ? h(DeviceTabletIcon) :
          h(DeviceDesktopIcon)
        ),
        h('span', row.device_type || '未知')
      ]),
      h('div', { class: 'browser-os' }, `${row.browser || ''} / ${row.os || ''}`)
    ])
  },
  {
    title: '地理位置',
    key: 'location',
    width: 150,
    render: (row: any) => {
      if (!row.location?.country) return '-'
      return h('div', { class: 'location-info' }, [
        h('n-icon', { size: 14, class: 'location-icon' }, h(MapPinIcon)),
        h('span', `${row.location.city || ''}, ${row.location.country || ''}`)
      ])
    }
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row: any) => h(
      'n-tag',
      { 
        size: 'small',
        type: row.is_active ? 'success' : 'default'
      },
      row.is_active ? '活跃' : '非活跃'
    )
  },
  {
    title: '最后活动',
    key: 'last_activity',
    width: 160,
    render: (row: any) => formatTime(row.last_activity)
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render: (row: any) => formatTime(row.created_at)
  },
  {
    title: '过期时间',
    key: 'expires_at',
    width: 160,
    render: (row: any) => formatTime(row.expires_at)
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right',
    render: (row: any) => h('div', { class: 'action-buttons' }, [
      h('n-button', {
        size: 'small',
        type: 'primary',
        text: true,
        onClick: () => handleViewDetail(row.id)
      }, { default: () => '查看', icon: () => h(EyeIcon) }),
      
      h('n-button', {
        size: 'small',
        type: 'error',
        text: true,
        onClick: () => handleTerminateSession(row.id)
      }, { default: () => '下线', icon: () => h(LogoutIcon) })
    ])
  }
]

// 方法
const loadSessionList = async () => {
  try {
    loading.value = true
    
    const params = {
      page: paginationConfig.value.page,
      page_size: paginationConfig.value.pageSize,
      search: searchKeyword.value,
      status: statusFilter.value,
      device_type: deviceFilter.value
    }
    
    const response = await request.get('/api/auth/sessions/', { params })
    
    if (response.data.code === 200) {
      sessionList.value = response.data.data.sessions
      paginationConfig.value.itemCount = response.data.data.pagination.total
    }
    
  } catch (error: any) {
    message.error('加载会话列表失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const loadSessionStats = async () => {
  try {
    const response = await request.get('/api/auth/sessions/statistics/')
    
    if (response.data.code === 200) {
      sessionStats.value = response.data.data.overview
    }
    
  } catch (error: any) {
    console.error('加载会话统计失败:', error)
  }
}

const handleSearch = () => {
  paginationConfig.value.page = 1
  loadSessionList()
}

const handleFilter = () => {
  paginationConfig.value.page = 1
  loadSessionList()
}

const handlePageChange = (page: number) => {
  paginationConfig.value.page = page
  loadSessionList()
}

const handlePageSizeChange = (pageSize: number) => {
  paginationConfig.value.pageSize = pageSize
  paginationConfig.value.page = 1
  loadSessionList()
}

const handleSelectionChange = (keys: string[]) => {
  selectedRowKeys.value = keys
}

const handleViewDetail = (sessionId: number) => {
  selectedSessionId.value = sessionId
  showDetailDialog.value = true
}

const handleTerminateSession = async (sessionId: number) => {
  try {
    await request.post('/api/auth/sessions/terminate/', { session_id: sessionId })
    message.success('会话已下线')
    loadSessionList()
    loadSessionStats()
  } catch (error: any) {
    message.error('下线会话失败: ' + (error.response?.data?.message || error.message))
  }
}

const handleBatchTerminate = async () => {
  try {
    const promises = selectedRowKeys.value.map(sessionId =>
      request.post('/api/auth/sessions/terminate/', { session_id: sessionId })
    )
    
    await Promise.all(promises)
    
    message.success(`成功下线 ${selectedRowKeys.value.length} 个会话`)
    selectedRowKeys.value = []
    showBatchTerminateDialog.value = false
    loadSessionList()
    loadSessionStats()
    
  } catch (error: any) {
    message.error('批量下线失败: ' + (error.response?.data?.message || error.message))
  }
}

const cleanupExpiredSessions = async () => {
  try {
    const response = await request.post('/api/auth/sessions/cleanup/')
    
    if (response.data.code === 200) {
      const cleanedCount = response.data.data.cleaned_count
      message.success(`已清理 ${cleanedCount} 个过期会话`)
      loadSessionList()
      loadSessionStats()
    }
    
  } catch (error: any) {
    message.error('清理过期会话失败: ' + (error.response?.data?.message || error.message))
  }
}

const refreshSessions = () => {
  loadSessionList()
  loadSessionStats()
}

const handleSessionTerminated = () => {
  showDetailDialog.value = false
  loadSessionList()
  loadSessionStats()
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadSessionList()
  loadSessionStats()
})
</script>

<style scoped>
.session-management-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.session-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.main-content {
  flex: 1;
  padding: 0 24px 24px;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.session-list {
  flex: 1;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.session-table {
  height: 100%;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 500;
  color: #1f2937;
}

.nickname {
  font-size: 12px;
  color: #6b7280;
}

.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.device-type {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.device-icon {
  color: #6b7280;
}

.browser-os {
  font-size: 11px;
  color: #6b7280;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.location-icon {
  color: #6b7280;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.warning-text {
  color: #d97706;
  font-size: 14px;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .session-stats {
    width: 100%;
    justify-content: space-around;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .main-content {
    padding: 0 16px 16px;
  }
  
  .session-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
</style>
