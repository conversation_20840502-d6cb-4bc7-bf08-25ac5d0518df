<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">个人资料</h1>
        <p class="page-description">管理您的个人信息和账户设置</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <n-grid :cols="3" :x-gap="24">
        <!-- 左侧：头像和基本信息 -->
        <n-grid-item>
          <n-card title="头像设置" class="avatar-card">
            <div class="avatar-section">
              <n-avatar
                :size="120"
                :src="currentUser?.avatar"
                class="user-avatar"
              >
                {{ currentUser?.nickname?.charAt(0) || currentUser?.username?.charAt(0) }}
              </n-avatar>
              
              <div class="avatar-actions">
                <n-upload
                  :show-file-list="false"
                  accept="image/*"
                  @before-upload="handleAvatarUpload"
                >
                  <n-button type="primary" size="small">
                    <template #icon>
                      <n-icon><UploadIcon /></n-icon>
                    </template>
                    更换头像
                  </n-button>
                </n-upload>
                
                <p class="avatar-tip">
                  支持 JPG、PNG 格式，文件大小不超过 2MB
                </p>
              </div>
            </div>
          </n-card>
          
          <!-- 账户信息 -->
          <n-card title="账户信息" class="account-info-card">
            <div class="info-list">
              <div class="info-item">
                <label>用户名</label>
                <span>{{ currentUser?.username }}</span>
              </div>
              <div class="info-item">
                <label>创建时间</label>
                <span>{{ formatDateTime(currentUser?.created_at) }}</span>
              </div>
              <div class="info-item">
                <label>最后登录</label>
                <span>{{ formatDateTime(currentUser?.last_login_time) }}</span>
              </div>
              <div class="info-item">
                <label>账户状态</label>
                <n-tag :type="currentUser?.is_active ? 'success' : 'error'" size="small">
                  {{ currentUser?.is_active ? '正常' : '禁用' }}
                </n-tag>
              </div>
            </div>
          </n-card>
        </n-grid-item>

        <!-- 中间：个人信息编辑 -->
        <n-grid-item :span="2">
          <n-card title="个人信息" class="profile-form-card">
            <n-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-placement="left"
              label-width="100px"
              require-mark-placement="right-hanging"
            >
              <n-form-item label="昵称" path="nickname">
                <n-input
                  v-model:value="profileForm.nickname"
                  placeholder="请输入昵称"
                />
              </n-form-item>
              
              <n-form-item label="邮箱" path="email">
                <n-input
                  v-model:value="profileForm.email"
                  placeholder="请输入邮箱"
                />
              </n-form-item>
              
              <n-form-item label="手机号" path="phone">
                <n-input
                  v-model:value="profileForm.phone"
                  placeholder="请输入手机号"
                />
              </n-form-item>
              
              <n-form-item>
                <div class="form-actions">
                  <n-button
                    type="primary"
                    :loading="profileSubmitting"
                    @click="handleProfileSubmit"
                  >
                    保存信息
                  </n-button>
                  <n-button @click="handleProfileReset">
                    重置
                  </n-button>
                </div>
              </n-form-item>
            </n-form>
          </n-card>
          
          <!-- 密码修改 -->
          <n-card title="修改密码" class="password-form-card">
            <n-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-placement="left"
              label-width="100px"
              require-mark-placement="right-hanging"
            >
              <n-form-item label="当前密码" path="old_password">
                <n-input
                  v-model:value="passwordForm.old_password"
                  type="password"
                  placeholder="请输入当前密码"
                  show-password-on="mousedown"
                />
              </n-form-item>
              
              <n-form-item label="新密码" path="new_password">
                <n-input
                  v-model:value="passwordForm.new_password"
                  type="password"
                  placeholder="请输入新密码"
                  show-password-on="mousedown"
                />
              </n-form-item>
              
              <n-form-item label="确认密码" path="confirm_password">
                <n-input
                  v-model:value="passwordForm.confirm_password"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password-on="mousedown"
                />
              </n-form-item>
              
              <n-form-item>
                <div class="form-actions">
                  <n-button
                    type="primary"
                    :loading="passwordSubmitting"
                    @click="handlePasswordSubmit"
                  >
                    修改密码
                  </n-button>
                  <n-button @click="handlePasswordReset">
                    重置
                  </n-button>
                </div>
              </n-form-item>
            </n-form>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { UploadIcon } from '@vicons/tabler'
import { useAuthStore } from '@/stores/auth'
import { userApi } from '@/api/user'
import type { ProfileForm, ChangePasswordForm } from '@/types/user'

// 状态管理
const authStore = useAuthStore()
const message = useMessage()

// 响应式数据
const profileFormRef = ref<FormInst>()
const passwordFormRef = ref<FormInst>()
const profileSubmitting = ref(false)
const passwordSubmitting = ref(false)

// 表单数据
const profileForm = ref<ProfileForm>({
  nickname: '',
  email: '',
  phone: '',
  avatar: ''
})

const passwordForm = ref<ChangePasswordForm>({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

// 计算属性
const currentUser = computed(() => authStore.userInfo)

// 表单验证规则
const profileRules: FormRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { max: 50, message: '昵称不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: '密码必须包含大小写字母和数字',
      trigger: 'blur'
    }
  ],
  confirm_password: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        return value === passwordForm.value.new_password
      },
      message: '两次输入的密码不一致',
      trigger: 'blur'
    }
  ]
}

// 方法
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const loadProfileData = () => {
  if (currentUser.value) {
    profileForm.value = {
      nickname: currentUser.value.nickname,
      email: currentUser.value.email || '',
      phone: currentUser.value.phone || '',
      avatar: currentUser.value.avatar || ''
    }
  }
}

const handleAvatarUpload = async (file: File) => {
  try {
    // 检查文件大小
    if (file.size > 2 * 1024 * 1024) {
      message.error('文件大小不能超过2MB')
      return false
    }
    
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      message.error('只能上传图片文件')
      return false
    }
    
    const response = await userApi.uploadUserAvatar(file)
    
    if (response.code === 200) {
      profileForm.value.avatar = response.data.url
      message.success('头像上传成功')
      
      // 更新个人资料
      await handleProfileSubmit()
    }
  } catch (error) {
    message.error('头像上传失败')
  }
  
  return false // 阻止默认上传行为
}

const handleProfileSubmit = async () => {
  try {
    await profileFormRef.value?.validate()
    
    profileSubmitting.value = true
    
    const response = await userApi.updateCurrentUserProfile(profileForm.value)
    
    if (response.code === 200) {
      // 更新本地用户信息
      authStore.userInfo = response.data
      message.success('个人信息更新成功')
    }
  } catch (error) {
    message.error('个人信息更新失败')
  } finally {
    profileSubmitting.value = false
  }
}

const handleProfileReset = () => {
  loadProfileData()
}

const handlePasswordSubmit = async () => {
  try {
    await passwordFormRef.value?.validate()
    
    passwordSubmitting.value = true
    
    const response = await userApi.changeCurrentUserPassword(passwordForm.value)
    
    if (response.code === 200) {
      message.success('密码修改成功，请重新登录')
      
      // 清空密码表单
      passwordForm.value = {
        old_password: '',
        new_password: '',
        confirm_password: ''
      }
      
      // 可以选择自动登出用户
      // await authStore.logout()
      // router.push('/login')
    }
  } catch (error) {
    message.error('密码修改失败')
  } finally {
    passwordSubmitting.value = false
  }
}

const handlePasswordReset = () => {
  passwordForm.value = {
    old_password: '',
    new_password: '',
    confirm_password: ''
  }
}

// 生命周期
onMounted(() => {
  loadProfileData()
})
</script>

<style scoped>
.profile-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.main-content {
  flex: 1;
  padding: 0 24px 24px;
}

.avatar-card,
.account-info-card,
.profile-form-card,
.password-form-card {
  margin-bottom: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  border: 3px solid #e5e7eb;
}

.avatar-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.avatar-tip {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  margin: 0;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #1f2937;
}

.form-actions {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    padding: 0 16px 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .avatar-section {
    padding: 16px 0;
  }
  
  .user-avatar {
    width: 80px !important;
    height: 80px !important;
  }
}
</style>
