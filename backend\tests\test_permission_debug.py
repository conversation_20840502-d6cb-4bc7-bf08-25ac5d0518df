"""
权限功能调试测试
"""
import pytest
from django.test import TestCase
from apps.permissions.models import Role, Permission, UserRole
from apps.permissions.services import PermissionService
from tests.factories import UserProfileFactory, RoleFactory, PermissionFactory


@pytest.mark.django_db
class TestPermissionDebug(TestCase):
    """权限功能调试测试"""
    
    def test_basic_permission_creation(self):
        """测试基础权限创建"""
        permission = PermissionFactory(code='test:debug')
        
        self.assertIsNotNone(permission)
        self.assertEqual(permission.code, 'test:debug')
        print(f"创建权限: {permission}")
    
    def test_basic_role_creation(self):
        """测试基础角色创建"""
        role = RoleFactory(name='调试角色')
        
        self.assertIsNotNone(role)
        self.assertEqual(role.name, '调试角色')
        print(f"创建角色: {role}")
    
    def test_role_permission_assignment(self):
        """测试角色权限分配"""
        role = RoleFactory()
        permission = PermissionFactory(code='debug:test')
        
        # 分配权限给角色
        role.permissions.add(permission)
        
        # 验证分配
        self.assertIn(permission, role.permissions.all())
        print(f"角色 {role.name} 拥有权限: {[p.code for p in role.permissions.all()]}")
    
    def test_user_role_assignment(self):
        """测试用户角色分配"""
        user = UserProfileFactory()
        role = RoleFactory()
        permission = PermissionFactory(code='user:test')
        
        # 分配权限给角色
        role.permissions.add(permission)
        
        # 分配角色给用户
        user_role = UserRole.objects.create(user=user, role=role)
        
        # 验证分配
        self.assertIsNotNone(user_role)
        print(f"用户 {user.nickname} 拥有角色: {role.name}")
    
    def test_get_user_permissions_debug(self):
        """测试获取用户权限（调试版）"""
        user = UserProfileFactory()
        role = RoleFactory()
        permission = PermissionFactory(code='debug:permission')
        
        # 建立关系
        role.permissions.add(permission)
        UserRole.objects.create(user=user, role=role)
        
        # 获取用户权限
        permissions = PermissionService.get_user_permissions(user)
        
        print(f"用户权限数量: {len(permissions)}")
        print(f"权限列表: {[p.code for p in permissions]}")
        
        self.assertGreater(len(permissions), 0)
        self.assertIn(permission, permissions)
    
    def test_check_user_permission_debug(self):
        """测试检查用户权限（调试版）"""
        user = UserProfileFactory()
        role = RoleFactory()
        permission = PermissionFactory(code='debug:check')
        
        # 建立关系
        role.permissions.add(permission)
        UserRole.objects.create(user=user, role=role)
        
        # 检查权限
        has_permission = PermissionService.check_user_permission(user, 'debug:check')
        
        print(f"用户是否有权限 'debug:check': {has_permission}")
        
        # 调试信息
        user_permissions = PermissionService.get_user_permissions(user)
        print(f"用户所有权限: {[p.code for p in user_permissions]}")
        
        self.assertTrue(has_permission)
