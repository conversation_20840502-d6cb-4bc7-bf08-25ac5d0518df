#!/usr/bin/env python
"""
HEIM项目测试运行脚本
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔍 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("警告信息:")
            print(result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HEIM项目测试运行器')
    parser.add_argument('--unit', action='store_true', help='只运行单元测试')
    parser.add_argument('--integration', action='store_true', help='只运行集成测试')
    parser.add_argument('--coverage', action='store_true', help='生成覆盖率报告')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--fast', action='store_true', help='快速测试（跳过慢速测试）')
    parser.add_argument('--module', '-m', help='指定测试模块')
    
    args = parser.parse_args()
    
    print("🧪 HEIM项目测试运行器")
    print("严格遵循项目技术规范，使用uv包管理工具")
    
    # 基础pytest命令
    base_cmd = "uv run pytest"
    
    # 构建测试命令
    test_options = []
    
    if args.verbose:
        test_options.append("-v")
    
    if args.fast:
        test_options.append("-m 'not slow'")
    
    if args.unit:
        test_options.append("-m unit")
    elif args.integration:
        test_options.append("-m integration")
    
    if args.coverage:
        test_options.append("--cov=apps")
        test_options.append("--cov-report=html")
        test_options.append("--cov-report=term-missing")
    
    if args.module:
        test_options.append(f"tests/test_{args.module}.py")
    
    # 组合命令
    test_cmd = f"{base_cmd} {' '.join(test_options)}"
    
    # 运行测试前的检查
    print("\n📋 运行测试前检查...")
    
    # 检查uv是否可用
    if not run_command("uv --version", "检查uv包管理工具"):
        print("❌ uv不可用，请先安装uv")
        return 1
    
    # 检查Django配置
    if not run_command("uv run python manage.py check --settings=config.settings.test", "检查Django配置"):
        print("❌ Django配置检查失败")
        return 1
    
    # 运行数据库迁移（测试数据库）
    if not run_command("uv run python manage.py migrate --settings=config.settings.test", "应用数据库迁移"):
        print("❌ 数据库迁移失败")
        return 1
    
    # 运行测试
    print(f"\n🚀 开始运行测试...")
    print(f"命令: {test_cmd}")
    
    success = run_command(test_cmd, "执行测试用例")
    
    if success:
        print("\n🎉 测试运行完成！")
        
        if args.coverage:
            print("\n📊 覆盖率报告已生成:")
            print("  - HTML报告: htmlcov/index.html")
            print("  - 终端报告: 见上方输出")
        
        print("\n📝 测试总结:")
        print("  ✅ 所有测试用例已执行")
        print("  ✅ 遵循项目技术规范")
        print("  ✅ 使用uv包管理工具")
        print("  ✅ 测试配置正确")
        
        return 0
    else:
        print("\n❌ 测试运行失败，请检查错误信息")
        return 1

if __name__ == '__main__':
    sys.exit(main())
