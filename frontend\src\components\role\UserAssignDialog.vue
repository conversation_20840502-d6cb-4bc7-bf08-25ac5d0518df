<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="用户分配"
    class="user-assign-dialog"
    style="width: 800px"
    @update:show="$emit('update:visible', $event)"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else class="user-assign-content">
      <!-- 角色信息 -->
      <div v-if="roleDetail" class="role-info">
        <div class="role-basic">
          <h3 class="role-name">{{ roleDetail.name }}</h3>
          <n-tag type="info" size="small">{{ roleDetail.code }}</n-tag>
        </div>
        <div class="role-stats">
          <span class="stat-item">当前用户: {{ currentUsers.length }}</span>
          <span class="stat-item">新增用户: {{ selectedUsers.length }}</span>
        </div>
      </div>
      
      <!-- 用户搜索和筛选 -->
      <div class="user-toolbar">
        <div class="toolbar-left">
          <n-input
            v-model:value="userSearchKeyword"
            placeholder="搜索用户姓名、用户名、邮箱..."
            clearable
            style="width: 300px"
            @keyup.enter="handleUserSearch"
          >
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
          </n-input>
          
          <n-select
            v-model:value="filterDepartment"
            placeholder="筛选部门"
            clearable
            filterable
            style="width: 200px"
            @update:value="handleUserSearch"
          >
            <n-option
              v-for="dept in departmentOptions"
              :key="dept.id"
              :value="dept.id"
              :label="dept.name"
            />
          </n-select>
          
          <n-button @click="handleUserSearch">
            <template #icon>
              <n-icon><SearchIcon /></n-icon>
            </template>
            搜索
          </n-button>
        </div>
        
        <div class="toolbar-right">
          <n-button
            v-if="selectedUsers.length > 0"
            type="primary"
            @click="handleBatchAssign"
          >
            批量分配 ({{ selectedUsers.length }})
          </n-button>
        </div>
      </div>
      
      <!-- 用户列表 -->
      <div class="user-list-container">
        <n-data-table
          :columns="userColumns"
          :data="userList"
          :loading="userLoading"
          :pagination="userPagination"
          :row-key="getUserRowKey"
          :checked-row-keys="selectedUsers"
          @update:checked-row-keys="handleUserSelectionChange"
          @update:page="handleUserPageChange"
          @update:page-size="handleUserPageSizeChange"
          striped
          class="user-table"
        />
      </div>
      
      <!-- 当前角色用户 -->
      <div v-if="currentUsers.length > 0" class="current-users">
        <h4>当前角色用户 ({{ currentUsers.length }})</h4>
        <div class="current-user-list">
          <div
            v-for="userRole in currentUsers"
            :key="userRole.id"
            class="current-user-item"
          >
            <n-avatar
              :size="32"
              :src="userRole.user_info.avatar"
              class="user-avatar"
            >
              {{ userRole.user_info.nickname?.charAt(0) || userRole.user_info.username?.charAt(0) }}
            </n-avatar>
            
            <div class="user-info">
              <div class="user-name">{{ userRole.user_info.nickname || userRole.user_info.username }}</div>
              <div class="user-email">{{ userRole.user_info.email || '-' }}</div>
            </div>
            
            <div class="user-meta">
              <n-tag v-if="userRole.department_info" size="small">
                {{ userRole.department_info.name }}
              </n-tag>
              <span class="assign-time">{{ formatDateTime(userRole.created_at) }}</span>
            </div>
            
            <n-button
              size="small"
              type="error"
              text
              @click="handleRemoveUser(userRole)"
            >
              移除
            </n-button>
          </div>
        </div>
      </div>
    </div>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          :disabled="selectedUsers.length === 0"
          @click="handleSubmit"
        >
          分配用户 ({{ selectedUsers.length }})
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, h } from 'vue'
import { useMessage, useDialog, type DataTableColumns } from 'naive-ui'
import { SearchIcon, UserIcon } from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { roleApi } from '@/api/role'
import type { RoleDetail, UserRole, UserInfo } from '@/types/role'

// Props
interface Props {
  visible: boolean
  roleId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  roleId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const userLoading = ref(false)
const submitting = ref(false)
const userSearchKeyword = ref('')
const filterDepartment = ref<number | null>(null)
const selectedUsers = ref<number[]>([])
const roleDetail = ref<RoleDetail | null>(null)
const currentUsers = ref<UserRole[]>([])
const userList = ref<UserInfo[]>([])
const departmentOptions = ref<Array<{ id: number; name: string }>>([])

// 用户分页配置
const userPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 用户表格列配置
const userColumns: DataTableColumns<UserInfo> = [
  {
    type: 'selection'
  },
  {
    title: '用户',
    key: 'user',
    width: 200,
    render: (row: UserInfo) => h('div', { class: 'flex items-center gap-3' }, [
      h('n-avatar', {
        size: 32,
        src: row.avatar
      }, row.nickname?.charAt(0) || row.username?.charAt(0)),
      h('div', { class: 'flex flex-col' }, [
        h('span', { class: 'font-medium' }, row.nickname || row.username),
        h('span', { class: 'text-sm text-gray-500' }, row.email || '-')
      ])
    ])
  },
  {
    title: '用户名',
    key: 'username',
    width: 120
  },
  {
    title: '邮箱',
    key: 'email',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row: UserInfo) => h(
      'n-tag',
      {
        type: row.is_active ? 'success' : 'error',
        size: 'small'
      },
      row.is_active ? '正常' : '禁用'
    )
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    render: (row: UserInfo) => h('n-button', {
      size: 'small',
      type: 'primary',
      text: true,
      onClick: () => handleAssignSingleUser(row.id)
    }, '分配')
  }
]

// 方法
const getUserRowKey = (row: UserInfo) => row.id

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const fetchRoleDetail = async () => {
  if (!props.roleId) return
  
  try {
    loading.value = true
    const response = await roleStore.fetchRoleDetail(props.roleId)
    
    if (response.code === 200) {
      roleDetail.value = response.data
    }
  } catch (error) {
    message.error('获取角色详情失败')
  } finally {
    loading.value = false
  }
}

const fetchCurrentUsers = async () => {
  if (!props.roleId) return
  
  try {
    const response = await roleApi.getRoleUsers(props.roleId)
    if (response.code === 200) {
      currentUsers.value = response.data
    }
  } catch (error) {
    console.error('获取当前用户失败:', error)
  }
}

const fetchDepartmentOptions = async () => {
  try {
    // 这里应该调用部门API获取部门列表
    // 临时使用空数组
    departmentOptions.value = []
  } catch (error) {
    console.error('获取部门选项失败:', error)
  }
}

const handleUserSearch = async () => {
  try {
    userLoading.value = true
    
    // 这里应该调用用户API搜索用户
    // 临时使用空数组
    userList.value = []
    userPagination.value.itemCount = 0
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

const handleUserSelectionChange = (keys: Array<string | number>) => {
  selectedUsers.value = keys as number[]
}

const handleUserPageChange = (page: number) => {
  userPagination.value.page = page
  handleUserSearch()
}

const handleUserPageSizeChange = (pageSize: number) => {
  userPagination.value.pageSize = pageSize
  userPagination.value.page = 1
  handleUserSearch()
}

const handleAssignSingleUser = (userId: number) => {
  selectedUsers.value = [userId]
  handleSubmit()
}

const handleBatchAssign = () => {
  handleSubmit()
}

const handleRemoveUser = (userRole: UserRole) => {
  dialog.warning({
    title: '确认移除用户',
    content: `确定要将用户 "${userRole.user_info.nickname || userRole.user_info.username}" 从角色中移除吗？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await roleApi.deleteUserRole(userRole.id)
        message.success('用户移除成功')
        await fetchCurrentUsers()
      } catch (error) {
        message.error('用户移除失败')
      }
    }
  })
}

const handleSubmit = async () => {
  if (!props.roleId || selectedUsers.value.length === 0) return
  
  try {
    submitting.value = true
    
    await roleApi.assignUsersToRole(props.roleId, {
      user_ids: selectedUsers.value,
      role_id: props.roleId,
      department_id: filterDepartment.value || undefined
    })
    
    message.success('用户分配成功')
    selectedUsers.value = []
    await fetchCurrentUsers()
    emit('success')
  } catch (error) {
    message.error('用户分配失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(async () => {
      if (props.roleId) {
        await Promise.all([
          fetchRoleDetail(),
          fetchCurrentUsers(),
          fetchDepartmentOptions()
        ])
      }
    })
  }
})

watch(() => props.roleId, (newRoleId) => {
  if (props.visible && newRoleId) {
    Promise.all([
      fetchRoleDetail(),
      fetchCurrentUsers(),
      fetchDepartmentOptions()
    ])
  }
})
</script>

<style scoped>
.user-assign-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.user-assign-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.role-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.role-basic {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.role-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  font-size: 14px;
  color: #6b7280;
}

.user-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
  gap: 12px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-right {
  flex-shrink: 0;
}

.user-list-container {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.user-table {
  min-height: 300px;
}

.current-users {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.current-users h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.current-user-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.current-user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.user-email {
  font-size: 12px;
  color: #6b7280;
}

.user-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.assign-time {
  font-size: 11px;
  color: #9ca3af;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
