"""
部门管理功能测试
"""
import pytest
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from apps.departments.models import Department
from apps.departments.services import DepartmentService
from tests.factories import (
    UserFactory, UserProfileFactory, DepartmentFactory,
    create_user_with_profile, create_department_hierarchy
)


@pytest.mark.django_db
class TestDepartmentManagementAPI(APITestCase):
    """部门管理API测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建管理员用户
        self.admin_user, self.admin_profile = create_user_with_profile()
        self.admin_user.is_staff = True
        self.admin_user.save()
        
        # API端点
        self.departments_url = reverse('departments:department-list')
        self.department_detail_url = lambda pk: reverse('departments:department-detail', kwargs={'pk': pk})
        
        # 管理员认证
        refresh = RefreshToken.for_user(self.admin_user)
        self.admin_token = str(refresh.access_token)
    
    def test_create_department_success(self):
        """测试成功创建部门"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        data = {
            'name': '新部门',
            'code': 'NEW_DEPT',
            'description': '这是一个新部门',
            'sort_order': 1
        }
        
        response = self.client.post(self.departments_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], '新部门')
        self.assertEqual(response.data['code'], 'NEW_DEPT')
        
        # 验证部门已创建
        department = Department.objects.get(name='新部门')
        self.assertIsNotNone(department)
        self.assertEqual(department.code, 'NEW_DEPT')
    
    def test_create_department_with_parent(self):
        """测试创建子部门"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        parent_dept = DepartmentFactory(name='父部门')
        
        data = {
            'name': '子部门',
            'code': 'CHILD_DEPT',
            'parent': parent_dept.id,
            'description': '这是一个子部门'
        }
        
        response = self.client.post(self.departments_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证父子关系
        child_dept = Department.objects.get(name='子部门')
        self.assertEqual(child_dept.parent, parent_dept)
        self.assertIn(child_dept, parent_dept.get_children())
    
    def test_create_department_duplicate_code(self):
        """测试创建重复编码的部门"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        existing_dept = DepartmentFactory(code='EXISTING')
        
        data = {
            'name': '重复编码部门',
            'code': 'EXISTING',  # 重复的编码
            'description': '测试重复编码'
        }
        
        response = self.client.post(self.departments_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('code', response.data)
    
    def test_get_department_list(self):
        """测试获取部门列表"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建部门层级结构
        departments = create_department_hierarchy(levels=3)
        
        response = self.client.get(self.departments_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['results']), 3)
    
    def test_get_department_tree(self):
        """测试获取部门树形结构"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # 创建部门层级结构
        departments = create_department_hierarchy(levels=3)
        
        url = reverse('departments:department-tree')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证树形结构
        tree_data = response.data
        self.assertIsInstance(tree_data, list)
        
        # 根部门应该有子部门
        if tree_data:
            root_dept = tree_data[0]
            self.assertIn('children', root_dept)
    
    def test_get_department_detail(self):
        """测试获取部门详情"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        department = DepartmentFactory()
        url = self.department_detail_url(department.id)
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], department.id)
        self.assertEqual(response.data['name'], department.name)
    
    def test_update_department_success(self):
        """测试成功更新部门"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        department = DepartmentFactory()
        url = self.department_detail_url(department.id)
        
        data = {
            'name': '更新的部门名称',
            'description': '更新的部门描述'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证更新
        department.refresh_from_db()
        self.assertEqual(department.name, '更新的部门名称')
        self.assertEqual(department.description, '更新的部门描述')
    
    def test_move_department(self):
        """测试移动部门"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        parent1 = DepartmentFactory(name='父部门1')
        parent2 = DepartmentFactory(name='父部门2')
        child = DepartmentFactory(name='子部门', parent=parent1)
        
        # 将子部门移动到父部门2下
        url = reverse('departments:department-move', kwargs={'pk': child.id})
        data = {'new_parent': parent2.id}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证移动
        child.refresh_from_db()
        self.assertEqual(child.parent, parent2)
    
    def test_delete_department_soft_delete(self):
        """测试软删除部门"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        department = DepartmentFactory()
        url = self.department_detail_url(department.id)
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # 验证软删除
        department.refresh_from_db()
        self.assertFalse(department.is_active)
    
    def test_delete_department_with_children_error(self):
        """测试删除有子部门的部门时报错"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        parent = DepartmentFactory(name='父部门')
        child = DepartmentFactory(name='子部门', parent=parent)
        
        url = self.department_detail_url(parent.id)
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('子部门', str(response.data))
    
    def test_restore_deleted_department(self):
        """测试恢复已删除部门"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        department = DepartmentFactory(is_active=False)
        
        url = reverse('departments:department-restore', kwargs={'pk': department.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证恢复
        department.refresh_from_db()
        self.assertTrue(department.is_active)
    
    def test_assign_manager_to_department(self):
        """测试为部门分配主管"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        department = DepartmentFactory()
        manager_user, manager_profile = create_user_with_profile()
        
        url = reverse('departments:department-assign-manager', kwargs={'pk': department.id})
        data = {'manager_id': manager_profile.id}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证主管分配
        department.refresh_from_db()
        self.assertIn(manager_profile, department.managers.all())
    
    def test_remove_manager_from_department(self):
        """测试移除部门主管"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        department = DepartmentFactory()
        manager_user, manager_profile = create_user_with_profile()
        department.managers.add(manager_profile)
        
        url = reverse('departments:department-remove-manager', kwargs={'pk': department.id})
        data = {'manager_id': manager_profile.id}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证主管移除
        department.refresh_from_db()
        self.assertNotIn(manager_profile, department.managers.all())


@pytest.mark.django_db
class TestDepartmentService(TestCase):
    """部门服务测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.dept_service = DepartmentService()
    
    def test_create_department(self):
        """测试创建部门"""
        data = {
            'name': '测试部门',
            'code': 'TEST_DEPT',
            'description': '这是一个测试部门'
        }
        
        department = self.dept_service.create_department(data)
        
        self.assertIsNotNone(department)
        self.assertEqual(department.name, '测试部门')
        self.assertEqual(department.code, 'TEST_DEPT')
    
    def test_create_child_department(self):
        """测试创建子部门"""
        parent = DepartmentFactory()
        
        data = {
            'name': '子部门',
            'code': 'CHILD_DEPT',
            'parent_id': parent.id
        }
        
        child = self.dept_service.create_department(data)
        
        self.assertEqual(child.parent, parent)
        self.assertIn(child, parent.get_children())
    
    def test_get_department_tree(self):
        """测试获取部门树"""
        # 创建部门层级结构
        departments = create_department_hierarchy(levels=3)
        
        tree = self.dept_service.get_department_tree()
        
        self.assertIsInstance(tree, list)
        self.assertGreater(len(tree), 0)
    
    def test_move_department(self):
        """测试移动部门"""
        parent1 = DepartmentFactory()
        parent2 = DepartmentFactory()
        child = DepartmentFactory(parent=parent1)
        
        result = self.dept_service.move_department(child.id, parent2.id)
        
        self.assertTrue(result)
        child.refresh_from_db()
        self.assertEqual(child.parent, parent2)
    
    def test_move_department_to_descendant_error(self):
        """测试移动部门到其后代时报错"""
        parent = DepartmentFactory()
        child = DepartmentFactory(parent=parent)
        grandchild = DepartmentFactory(parent=child)
        
        # 尝试将父部门移动到孙子部门下（不允许）
        with self.assertRaises(ValueError):
            self.dept_service.move_department(parent.id, grandchild.id)
    
    def test_delete_department_with_users(self):
        """测试删除有用户的部门"""
        department = DepartmentFactory()
        user, profile = create_user_with_profile()
        profile.department = department
        profile.save()
        
        # 应该不能删除有用户的部门
        with self.assertRaises(ValueError):
            self.dept_service.delete_department(department.id)
    
    def test_get_department_users(self):
        """测试获取部门用户"""
        department = DepartmentFactory()
        
        # 创建部门用户
        users = []
        for _ in range(3):
            user, profile = create_user_with_profile()
            profile.department = department
            profile.save()
            users.append(user)
        
        dept_users = self.dept_service.get_department_users(department.id)
        
        self.assertEqual(len(dept_users), 3)
        for user in users:
            self.assertIn(user, dept_users)
    
    def test_get_department_hierarchy(self):
        """测试获取部门层级"""
        departments = create_department_hierarchy(levels=4)
        
        # 获取最底层部门的层级
        bottom_dept = departments[-1]
        hierarchy = self.dept_service.get_department_hierarchy(bottom_dept.id)
        
        self.assertEqual(len(hierarchy), 4)
        self.assertEqual(hierarchy[0], departments[0])  # 根部门
        self.assertEqual(hierarchy[-1], bottom_dept)    # 底层部门
    
    def test_assign_multiple_managers(self):
        """测试分配多个主管"""
        department = DepartmentFactory()
        
        managers = []
        for _ in range(3):
            user, profile = create_user_with_profile()
            managers.append(profile)
        
        manager_ids = [m.id for m in managers]
        result = self.dept_service.assign_managers(department.id, manager_ids)
        
        self.assertTrue(result)
        department.refresh_from_db()
        
        for manager in managers:
            self.assertIn(manager, department.managers.all())
    
    def test_get_managed_departments(self):
        """测试获取用户管理的部门"""
        user, profile = create_user_with_profile()
        
        # 创建多个部门并分配给用户管理
        departments = []
        for _ in range(3):
            dept = DepartmentFactory()
            dept.managers.add(profile)
            departments.append(dept)
        
        managed_depts = self.dept_service.get_managed_departments(profile.id)
        
        self.assertEqual(len(managed_depts), 3)
        for dept in departments:
            self.assertIn(dept, managed_depts)


@pytest.mark.django_db
class TestDepartmentModel(TestCase):
    """部门模型测试"""
    
    def test_department_creation(self):
        """测试部门创建"""
        department = DepartmentFactory()
        
        self.assertIsNotNone(department.name)
        self.assertIsNotNone(department.code)
        self.assertTrue(department.is_active)
    
    def test_department_str_representation(self):
        """测试部门字符串表示"""
        department = DepartmentFactory(name='测试部门')
        
        self.assertEqual(str(department), '测试部门')
    
    def test_department_hierarchy(self):
        """测试部门层级关系"""
        parent = DepartmentFactory(name='父部门')
        child1 = DepartmentFactory(name='子部门1', parent=parent)
        child2 = DepartmentFactory(name='子部门2', parent=parent)
        grandchild = DepartmentFactory(name='孙子部门', parent=child1)
        
        # 测试父子关系
        self.assertEqual(child1.parent, parent)
        self.assertEqual(child2.parent, parent)
        self.assertEqual(grandchild.parent, child1)
        
        # 测试子部门获取
        children = parent.get_children()
        self.assertIn(child1, children)
        self.assertIn(child2, children)
        
        # 测试后代部门获取
        descendants = parent.get_descendants()
        self.assertIn(child1, descendants)
        self.assertIn(child2, descendants)
        self.assertIn(grandchild, descendants)
    
    def test_department_level(self):
        """测试部门层级深度"""
        departments = create_department_hierarchy(levels=4)
        
        self.assertEqual(departments[0].level, 0)  # 根部门
        self.assertEqual(departments[1].level, 1)  # 第二层
        self.assertEqual(departments[2].level, 2)  # 第三层
        self.assertEqual(departments[3].level, 3)  # 第四层
    
    def test_department_path(self):
        """测试部门路径"""
        departments = create_department_hierarchy(levels=3)
        
        bottom_dept = departments[-1]
        path = bottom_dept.get_path()
        
        # 路径应该包含所有祖先部门
        self.assertEqual(len(path), 3)
        self.assertEqual(path[0], departments[0])
        self.assertEqual(path[-1], bottom_dept)
    
    def test_is_ancestor_of(self):
        """测试祖先关系判断"""
        departments = create_department_hierarchy(levels=3)
        
        root = departments[0]
        middle = departments[1]
        bottom = departments[2]
        
        self.assertTrue(root.is_ancestor_of(middle))
        self.assertTrue(root.is_ancestor_of(bottom))
        self.assertTrue(middle.is_ancestor_of(bottom))
        self.assertFalse(bottom.is_ancestor_of(root))
    
    def test_is_descendant_of(self):
        """测试后代关系判断"""
        departments = create_department_hierarchy(levels=3)
        
        root = departments[0]
        middle = departments[1]
        bottom = departments[2]
        
        self.assertTrue(middle.is_descendant_of(root))
        self.assertTrue(bottom.is_descendant_of(root))
        self.assertTrue(bottom.is_descendant_of(middle))
        self.assertFalse(root.is_descendant_of(bottom))
