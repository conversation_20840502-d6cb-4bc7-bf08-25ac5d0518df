/**
 * 用户管理状态管理
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { userApi } from '@/api/user'
import type {
  User,
  UserListItem,
  UserSearchParams,
  UserCreateForm,
  UserEditForm,
  UserDetail,
  UserStats,
  PaginationInfo,
  BatchOperationParams
} from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userList = ref<UserListItem[]>([])
  const currentUser = ref<UserDetail | null>(null)
  const userStats = ref<UserStats | null>(null)
  const loading = ref(false)
  const searchParams = ref<UserSearchParams>({
    page: 1,
    page_size: 20,
    ordering: '-created_at'
  })
  const pagination = ref<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0
  })
  const selectedUserIds = ref<number[]>([])

  // 计算属性
  const hasSelectedUsers = computed(() => selectedUserIds.value.length > 0)
  const selectedUsersCount = computed(() => selectedUserIds.value.length)

  // 获取用户列表
  const fetchUserList = async (params?: UserSearchParams) => {
    try {
      loading.value = true
      
      // 合并搜索参数
      const mergedParams = { ...searchParams.value, ...params }
      searchParams.value = mergedParams

      const response = await userApi.getUserList(mergedParams)
      
      if (response.code === 200) {
        userList.value = response.data.results
        
        // 更新分页信息
        pagination.value = {
          page: mergedParams.page || 1,
          page_size: mergedParams.page_size || 20,
          total: response.data.count,
          total_pages: Math.ceil(response.data.count / (mergedParams.page_size || 20))
        }
      }
      
      return response
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户详情
  const fetchUserDetail = async (id: number) => {
    try {
      loading.value = true
      const response = await userApi.getUserDetail(id)
      
      if (response.code === 200) {
        currentUser.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取用户详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建用户
  const createUser = async (data: UserCreateForm) => {
    try {
      loading.value = true
      const response = await userApi.createUser(data)
      
      if (response.code === 200 || response.code === 1001) {
        // 重新获取用户列表
        await fetchUserList()
      }
      
      return response
    } catch (error) {
      console.error('创建用户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新用户
  const updateUser = async (id: number, data: UserEditForm) => {
    try {
      loading.value = true
      const response = await userApi.updateUser(id, data)
      
      if (response.code === 200) {
        // 更新列表中的用户信息
        const index = userList.value.findIndex(user => user.id === id)
        if (index !== -1) {
          userList.value[index] = { ...userList.value[index], ...response.data }
        }
        
        // 如果是当前查看的用户，更新详情
        if (currentUser.value?.id === id) {
          currentUser.value = { ...currentUser.value, ...response.data }
        }
      }
      
      return response
    } catch (error) {
      console.error('更新用户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除用户
  const deleteUser = async (id: number) => {
    try {
      loading.value = true
      const response = await userApi.deleteUser(id)
      
      if (response.code === 200) {
        // 从列表中移除用户
        userList.value = userList.value.filter(user => user.id !== id)
        
        // 更新分页信息
        pagination.value.total -= 1
        
        // 如果是当前查看的用户，清空详情
        if (currentUser.value?.id === id) {
          currentUser.value = null
        }
      }
      
      return response
    } catch (error) {
      console.error('删除用户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 切换用户状态
  const toggleUserActive = async (id: number) => {
    try {
      const response = await userApi.toggleUserActive(id)
      
      if (response.code === 200) {
        // 更新列表中的用户状态
        const index = userList.value.findIndex(user => user.id === id)
        if (index !== -1) {
          userList.value[index].is_active = response.data.is_active
        }
        
        // 如果是当前查看的用户，更新详情
        if (currentUser.value?.id === id) {
          currentUser.value.is_active = response.data.is_active
        }
      }
      
      return response
    } catch (error) {
      console.error('切换用户状态失败:', error)
      throw error
    }
  }

  // 批量操作用户
  const batchOperateUsers = async (params: BatchOperationParams) => {
    try {
      loading.value = true
      const response = await userApi.batchOperateUsers(params)
      
      if (response.code === 200) {
        // 重新获取用户列表
        await fetchUserList()
        // 清空选中的用户
        selectedUserIds.value = []
      }
      
      return response
    } catch (error) {
      console.error('批量操作失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户统计信息
  const fetchUserStats = async () => {
    try {
      const response = await userApi.getUserStats()
      
      if (response.code === 200) {
        userStats.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取用户统计失败:', error)
      throw error
    }
  }

  // 搜索用户
  const searchUsers = async (keyword: string) => {
    await fetchUserList({ ...searchParams.value, search: keyword, page: 1 })
  }

  // 筛选用户
  const filterUsers = async (filters: Partial<UserSearchParams>) => {
    await fetchUserList({ ...searchParams.value, ...filters, page: 1 })
  }

  // 排序用户
  const sortUsers = async (ordering: string) => {
    await fetchUserList({ ...searchParams.value, ordering, page: 1 })
  }

  // 切换页面
  const changePage = async (page: number) => {
    await fetchUserList({ ...searchParams.value, page })
  }

  // 改变每页大小
  const changePageSize = async (pageSize: number) => {
    await fetchUserList({ ...searchParams.value, page_size: pageSize, page: 1 })
  }

  // 选择用户
  const selectUser = (userId: number) => {
    if (!selectedUserIds.value.includes(userId)) {
      selectedUserIds.value.push(userId)
    }
  }

  // 取消选择用户
  const unselectUser = (userId: number) => {
    const index = selectedUserIds.value.indexOf(userId)
    if (index > -1) {
      selectedUserIds.value.splice(index, 1)
    }
  }

  // 切换用户选择状态
  const toggleUserSelection = (userId: number) => {
    if (selectedUserIds.value.includes(userId)) {
      unselectUser(userId)
    } else {
      selectUser(userId)
    }
  }

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedUserIds.value.length === userList.value.length) {
      selectedUserIds.value = []
    } else {
      selectedUserIds.value = userList.value.map(user => user.id)
    }
  }

  // 清空选择
  const clearSelection = () => {
    selectedUserIds.value = []
  }

  // 导出用户数据
  const exportUsers = async (params?: UserSearchParams) => {
    try {
      return await userApi.exportUsers(params)
    } catch (error) {
      console.error('导出用户数据失败:', error)
      throw error
    }
  }

  // 导入用户数据
  const importUsers = async (file: File) => {
    try {
      const response = await userApi.importUsers(file)
      return response
    } catch (error) {
      console.error('导入用户数据失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    userList.value = []
    currentUser.value = null
    userStats.value = null
    loading.value = false
    searchParams.value = {
      page: 1,
      page_size: 20,
      ordering: '-created_at'
    }
    pagination.value = {
      page: 1,
      page_size: 20,
      total: 0,
      total_pages: 0
    }
    selectedUserIds.value = []
  }

  return {
    // 状态
    userList,
    currentUser,
    userStats,
    loading,
    searchParams,
    pagination,
    selectedUserIds,
    
    // 计算属性
    hasSelectedUsers,
    selectedUsersCount,
    
    // 方法
    fetchUserList,
    fetchUserDetail,
    createUser,
    updateUser,
    deleteUser,
    toggleUserActive,
    batchOperateUsers,
    fetchUserStats,
    searchUsers,
    filterUsers,
    sortUsers,
    changePage,
    changePageSize,
    selectUser,
    unselectUser,
    toggleUserSelection,
    toggleSelectAll,
    clearSelection,
    exportUsers,
    importUsers,
    resetState
  }
})
