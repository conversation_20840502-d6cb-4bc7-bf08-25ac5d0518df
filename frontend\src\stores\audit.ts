/**
 * 审计日志状态管理
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { auditApi } from '@/api/audit'
import type {
  OperationLogListItem,
  OperationLogDetail,
  OperationLogSearchParams,
  OperationLogStats,
  UserOperationStats,
  SystemHealthStats,
  ExportTask,
  LogCleanupPolicy,
  LogAlertRule,
  LogAlertEvent,
  LogAnalysisResult,
  LogExportTemplate,
  SearchHistory,
  SavedSearch,
  LogDisplayConfig,
  RealTimeLogData,
  RealTimeStatsData
} from '@/types/audit'

// 分页信息类型
interface PaginationInfo {
  page: number
  page_size: number
  total: number
  total_pages: number
}

export const useAuditStore = defineStore('audit', () => {
  // 操作日志相关状态
  const operationLogList = ref<OperationLogListItem[]>([])
  const currentOperationLog = ref<OperationLogDetail | null>(null)
  const operationLogStats = ref<OperationLogStats | null>(null)
  const userOperationStats = ref<UserOperationStats | null>(null)
  const systemHealthStats = ref<SystemHealthStats | null>(null)
  const logAnalysisResult = ref<LogAnalysisResult | null>(null)
  const operationLogLoading = ref(false)
  
  // 实时数据状态
  const realTimeLogs = ref<OperationLogListItem[]>([])
  const realTimeStats = ref<RealTimeStatsData | null>(null)
  const realTimeEnabled = ref(false)
  const realTimeLoading = ref(false)
  
  // 导出任务状态
  const exportTaskList = ref<ExportTask[]>([])
  const exportTaskLoading = ref(false)
  
  // 清理策略状态
  const cleanupPolicyList = ref<LogCleanupPolicy[]>([])
  const cleanupPolicyLoading = ref(false)
  
  // 告警管理状态
  const alertRuleList = ref<LogAlertRule[]>([])
  const alertEventList = ref<LogAlertEvent[]>([])
  const alertLoading = ref(false)
  
  // 导出模板状态
  const exportTemplateList = ref<LogExportTemplate[]>([])
  const exportTemplateLoading = ref(false)
  
  // 搜索和分页状态
  const searchParams = ref<OperationLogSearchParams>({
    page: 1,
    page_size: 20,
    ordering: '-created_at'
  })
  
  const pagination = ref<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0
  })
  
  // 搜索历史和保存的搜索
  const searchHistory = ref<SearchHistory[]>([])
  const savedSearches = ref<SavedSearch[]>([])
  
  // 显示配置
  const displayConfig = ref<LogDisplayConfig>({
    pageSize: 20,
    autoRefresh: false,
    refreshInterval: 30,
    showColumns: ['user_nickname', 'operation_type', 'operation_desc', 'method', 'ip_address', 'status_code', 'response_time', 'created_at_formatted'],
    highlight: {
      keywords: [],
      colors: ['#ff4d4f', '#faad14', '#52c41a', '#1890ff'],
      caseSensitive: false,
      wholeWord: false
    }
  })
  
  // 筛选选项
  const filterOptions = ref<{
    operation_types: Array<{ value: string; label: string }>
    methods: Array<{ value: string; label: string }>
    status_codes: Array<{ value: number; label: string }>
  }>({
    operation_types: [],
    methods: [],
    status_codes: []
  })
  
  // 计算属性
  const hasOperationLogs = computed(() => operationLogList.value.length > 0)
  const totalOperationLogs = computed(() => pagination.value.total)
  const hasRealTimeLogs = computed(() => realTimeLogs.value.length > 0)
  const unacknowledgedAlerts = computed(() => 
    alertEventList.value.filter(event => !event.acknowledged).length
  )
  
  // 操作日志管理方法
  const fetchOperationLogList = async (params?: OperationLogSearchParams) => {
    try {
      operationLogLoading.value = true
      
      const mergedParams = { ...searchParams.value, ...params }
      searchParams.value = mergedParams
      
      const response = await auditApi.getOperationLogList(mergedParams)
      
      if (response.code === 200) {
        operationLogList.value = response.data.results
        
        pagination.value = {
          page: mergedParams.page || 1,
          page_size: mergedParams.page_size || 20,
          total: response.data.count,
          total_pages: Math.ceil(response.data.count / (mergedParams.page_size || 20))
        }
      }
      
      return response
    } catch (error) {
      console.error('获取操作日志列表失败:', error)
      throw error
    } finally {
      operationLogLoading.value = false
    }
  }
  
  const fetchOperationLogDetail = async (id: number) => {
    try {
      operationLogLoading.value = true
      const response = await auditApi.getOperationLogDetail(id)
      
      if (response.code === 200) {
        currentOperationLog.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取操作日志详情失败:', error)
      throw error
    } finally {
      operationLogLoading.value = false
    }
  }
  
  const fetchOperationLogStats = async () => {
    try {
      const response = await auditApi.getOperationLogStats()
      
      if (response.code === 200) {
        operationLogStats.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取操作日志统计失败:', error)
      throw error
    }
  }
  
  const fetchUserOperationStats = async (days?: number) => {
    try {
      const response = await auditApi.getUserOperationStats(days)
      
      if (response.code === 200) {
        userOperationStats.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取用户操作统计失败:', error)
      throw error
    }
  }
  
  const fetchSystemHealthStats = async () => {
    try {
      const response = await auditApi.getSystemHealthStats()
      
      if (response.code === 200) {
        systemHealthStats.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取系统健康状态失败:', error)
      throw error
    }
  }
  
  const fetchLogAnalysis = async (params?: OperationLogSearchParams) => {
    try {
      operationLogLoading.value = true
      const response = await auditApi.getLogAnalysis(params)
      
      if (response.code === 200) {
        logAnalysisResult.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取日志分析结果失败:', error)
      throw error
    } finally {
      operationLogLoading.value = false
    }
  }
  
  // 实时数据管理方法
  const fetchRealTimeLogs = async (limit?: number) => {
    try {
      realTimeLoading.value = true
      const response = await auditApi.getRealTimeLogs(limit)
      
      if (response.code === 200) {
        realTimeLogs.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取实时日志失败:', error)
      throw error
    } finally {
      realTimeLoading.value = false
    }
  }
  
  const fetchRealTimeStats = async () => {
    try {
      const response = await auditApi.getRealTimeStats()
      
      if (response.code === 200) {
        realTimeStats.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取实时统计失败:', error)
      throw error
    }
  }
  
  const addRealTimeLog = (log: OperationLogListItem) => {
    realTimeLogs.value.unshift(log)
    
    // 限制实时日志数量
    const maxLogs = displayConfig.value.highlight.keywords.length || 100
    if (realTimeLogs.value.length > maxLogs) {
      realTimeLogs.value = realTimeLogs.value.slice(0, maxLogs)
    }
  }
  
  const toggleRealTime = () => {
    realTimeEnabled.value = !realTimeEnabled.value
  }
  
  // 导出任务管理方法
  const fetchExportTaskList = async () => {
    try {
      exportTaskLoading.value = true
      const response = await auditApi.getExportTaskList()
      
      if (response.code === 200) {
        exportTaskList.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取导出任务列表失败:', error)
      throw error
    } finally {
      exportTaskLoading.value = false
    }
  }
  
  const cancelExportTask = async (taskId: string) => {
    try {
      const response = await auditApi.cancelExportTask(taskId)
      
      if (response.code === 200) {
        await fetchExportTaskList()
      }
      
      return response
    } catch (error) {
      console.error('取消导出任务失败:', error)
      throw error
    }
  }
  
  const deleteExportTask = async (taskId: string) => {
    try {
      const response = await auditApi.deleteExportTask(taskId)
      
      if (response.code === 200) {
        exportTaskList.value = exportTaskList.value.filter(task => task.id !== taskId)
      }
      
      return response
    } catch (error) {
      console.error('删除导出任务失败:', error)
      throw error
    }
  }
  
  // 清理策略管理方法
  const fetchCleanupPolicyList = async () => {
    try {
      cleanupPolicyLoading.value = true
      const response = await auditApi.getCleanupPolicyList()
      
      if (response.code === 200) {
        cleanupPolicyList.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取清理策略列表失败:', error)
      throw error
    } finally {
      cleanupPolicyLoading.value = false
    }
  }
  
  const createCleanupPolicy = async (data: Partial<LogCleanupPolicy>) => {
    try {
      cleanupPolicyLoading.value = true
      const response = await auditApi.createCleanupPolicy(data)
      
      if (response.code === 200 || response.code === 201) {
        await fetchCleanupPolicyList()
      }
      
      return response
    } catch (error) {
      console.error('创建清理策略失败:', error)
      throw error
    } finally {
      cleanupPolicyLoading.value = false
    }
  }
  
  const updateCleanupPolicy = async (id: string, data: Partial<LogCleanupPolicy>) => {
    try {
      cleanupPolicyLoading.value = true
      const response = await auditApi.updateCleanupPolicy(id, data)
      
      if (response.code === 200) {
        const index = cleanupPolicyList.value.findIndex(policy => policy.id === id)
        if (index !== -1) {
          cleanupPolicyList.value[index] = { ...cleanupPolicyList.value[index], ...response.data }
        }
      }
      
      return response
    } catch (error) {
      console.error('更新清理策略失败:', error)
      throw error
    } finally {
      cleanupPolicyLoading.value = false
    }
  }
  
  const deleteCleanupPolicy = async (id: string) => {
    try {
      cleanupPolicyLoading.value = true
      const response = await auditApi.deleteCleanupPolicy(id)
      
      if (response.code === 200) {
        cleanupPolicyList.value = cleanupPolicyList.value.filter(policy => policy.id !== id)
      }
      
      return response
    } catch (error) {
      console.error('删除清理策略失败:', error)
      throw error
    } finally {
      cleanupPolicyLoading.value = false
    }
  }
  
  // 搜索管理方法
  const addSearchHistory = (query: string, filters: OperationLogSearchParams) => {
    const historyItem: SearchHistory = {
      id: Date.now().toString(),
      query,
      filters,
      timestamp: new Date().toISOString()
    }
    
    searchHistory.value.unshift(historyItem)
    
    // 限制历史记录数量
    if (searchHistory.value.length > 20) {
      searchHistory.value = searchHistory.value.slice(0, 20)
    }
    
    // 保存到本地存储
    localStorage.setItem('audit_search_history', JSON.stringify(searchHistory.value))
  }
  
  const saveSearch = (name: string, filters: OperationLogSearchParams) => {
    const savedSearch: SavedSearch = {
      id: Date.now().toString(),
      name,
      filters,
      created_at: new Date().toISOString()
    }
    
    savedSearches.value.push(savedSearch)
    
    // 保存到本地存储
    localStorage.setItem('audit_saved_searches', JSON.stringify(savedSearches.value))
  }
  
  const deleteSavedSearch = (id: string) => {
    savedSearches.value = savedSearches.value.filter(search => search.id !== id)
    localStorage.setItem('audit_saved_searches', JSON.stringify(savedSearches.value))
  }
  
  const loadLocalData = () => {
    // 加载搜索历史
    const historyData = localStorage.getItem('audit_search_history')
    if (historyData) {
      try {
        searchHistory.value = JSON.parse(historyData)
      } catch (error) {
        console.error('加载搜索历史失败:', error)
      }
    }
    
    // 加载保存的搜索
    const savedData = localStorage.getItem('audit_saved_searches')
    if (savedData) {
      try {
        savedSearches.value = JSON.parse(savedData)
      } catch (error) {
        console.error('加载保存的搜索失败:', error)
      }
    }
    
    // 加载显示配置
    const configData = localStorage.getItem('audit_display_config')
    if (configData) {
      try {
        displayConfig.value = { ...displayConfig.value, ...JSON.parse(configData) }
      } catch (error) {
        console.error('加载显示配置失败:', error)
      }
    }
  }
  
  const saveDisplayConfig = () => {
    localStorage.setItem('audit_display_config', JSON.stringify(displayConfig.value))
  }
  
  // 获取筛选选项
  const fetchFilterOptions = async () => {
    try {
      const response = await auditApi.getFilterOptions()
      
      if (response.code === 200) {
        filterOptions.value = response.data
      }
      
      return response
    } catch (error) {
      console.error('获取筛选选项失败:', error)
      throw error
    }
  }
  
  // 重置状态
  const resetState = () => {
    operationLogList.value = []
    currentOperationLog.value = null
    operationLogStats.value = null
    userOperationStats.value = null
    systemHealthStats.value = null
    logAnalysisResult.value = null
    operationLogLoading.value = false
    
    realTimeLogs.value = []
    realTimeStats.value = null
    realTimeEnabled.value = false
    realTimeLoading.value = false
    
    exportTaskList.value = []
    exportTaskLoading.value = false
    
    cleanupPolicyList.value = []
    cleanupPolicyLoading.value = false
    
    alertRuleList.value = []
    alertEventList.value = []
    alertLoading.value = false
    
    exportTemplateList.value = []
    exportTemplateLoading.value = false
    
    searchParams.value = {
      page: 1,
      page_size: 20,
      ordering: '-created_at'
    }
    
    pagination.value = {
      page: 1,
      page_size: 20,
      total: 0,
      total_pages: 0
    }
  }
  
  return {
    // 状态
    operationLogList,
    currentOperationLog,
    operationLogStats,
    userOperationStats,
    systemHealthStats,
    logAnalysisResult,
    operationLogLoading,
    realTimeLogs,
    realTimeStats,
    realTimeEnabled,
    realTimeLoading,
    exportTaskList,
    exportTaskLoading,
    cleanupPolicyList,
    cleanupPolicyLoading,
    alertRuleList,
    alertEventList,
    alertLoading,
    exportTemplateList,
    exportTemplateLoading,
    searchParams,
    pagination,
    searchHistory,
    savedSearches,
    displayConfig,
    filterOptions,
    
    // 计算属性
    hasOperationLogs,
    totalOperationLogs,
    hasRealTimeLogs,
    unacknowledgedAlerts,
    
    // 方法
    fetchOperationLogList,
    fetchOperationLogDetail,
    fetchOperationLogStats,
    fetchUserOperationStats,
    fetchSystemHealthStats,
    fetchLogAnalysis,
    fetchRealTimeLogs,
    fetchRealTimeStats,
    addRealTimeLog,
    toggleRealTime,
    fetchExportTaskList,
    cancelExportTask,
    deleteExportTask,
    fetchCleanupPolicyList,
    createCleanupPolicy,
    updateCleanupPolicy,
    deleteCleanupPolicy,
    addSearchHistory,
    saveSearch,
    deleteSavedSearch,
    loadLocalData,
    saveDisplayConfig,
    fetchFilterOptions,
    resetState
  }
})
