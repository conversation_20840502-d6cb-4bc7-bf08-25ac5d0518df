"""
审计日志模块 - 序列化器
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import OperationLog

User = get_user_model()


class OperationLogSerializer(serializers.ModelSerializer):
    """操作日志序列化器"""
    
    user_nickname = serializers.CharField(source='user.nickname', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    operation_type_display = serializers.CharField(source='get_operation_type_display', read_only=True)
    created_at_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = OperationLog
        fields = [
            'id', 'user', 'user_nickname', 'user_username',
            'operation_type', 'operation_type_display', 'operation_desc',
            'method', 'path', 'ip_address', 'user_agent',
            'status_code', 'response_time', 'created_at', 'created_at_formatted'
        ]
        read_only_fields = ['id', 'created_at']
    
    def get_created_at_formatted(self, obj):
        """格式化创建时间"""
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')


class OperationLogListSerializer(serializers.ModelSerializer):
    """操作日志列表序列化器（简化版）"""
    
    user_nickname = serializers.CharField(source='user.nickname', read_only=True)
    operation_type_display = serializers.CharField(source='get_operation_type_display', read_only=True)
    created_at_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = OperationLog
        fields = [
            'id', 'user_nickname', 'operation_type', 'operation_type_display',
            'operation_desc', 'method', 'ip_address', 'status_code',
            'response_time', 'created_at_formatted'
        ]
    
    def get_created_at_formatted(self, obj):
        """格式化创建时间"""
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')


class OperationLogDetailSerializer(serializers.ModelSerializer):
    """操作日志详情序列化器"""
    
    user_info = serializers.SerializerMethodField()
    operation_type_display = serializers.CharField(source='get_operation_type_display', read_only=True)
    created_at_formatted = serializers.SerializerMethodField()
    user_agent_parsed = serializers.SerializerMethodField()
    
    class Meta:
        model = OperationLog
        fields = [
            'id', 'user_info', 'operation_type', 'operation_type_display',
            'operation_desc', 'method', 'path', 'ip_address',
            'user_agent', 'user_agent_parsed', 'status_code',
            'response_time', 'created_at', 'created_at_formatted'
        ]
    
    def get_user_info(self, obj):
        """获取用户信息"""
        if obj.user:
            return {
                'id': obj.user.id,
                'username': obj.user.username,
                'nickname': obj.user.nickname,
                'email': obj.user.email,
            }
        return {
            'id': None,
            'username': '未知用户',
            'nickname': '未知用户',
            'email': '',
        }
    
    def get_created_at_formatted(self, obj):
        """格式化创建时间"""
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_user_agent_parsed(self, obj):
        """解析用户代理信息"""
        user_agent = obj.user_agent
        if not user_agent:
            return {'browser': '未知', 'os': '未知', 'device': '未知'}
        
        # 简单的用户代理解析
        browser = '未知'
        os = '未知'
        device = '桌面'
        
        # 浏览器检测
        if 'Chrome' in user_agent:
            browser = 'Chrome'
        elif 'Firefox' in user_agent:
            browser = 'Firefox'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            browser = 'Safari'
        elif 'Edge' in user_agent:
            browser = 'Edge'
        elif 'Opera' in user_agent:
            browser = 'Opera'
        
        # 操作系统检测
        if 'Windows' in user_agent:
            os = 'Windows'
        elif 'Mac OS' in user_agent or 'macOS' in user_agent:
            os = 'macOS'
        elif 'Linux' in user_agent:
            os = 'Linux'
        elif 'Android' in user_agent:
            os = 'Android'
            device = '移动设备'
        elif 'iOS' in user_agent or 'iPhone' in user_agent or 'iPad' in user_agent:
            os = 'iOS'
            device = '移动设备'
        
        # 设备类型检测
        if 'Mobile' in user_agent or 'Android' in user_agent:
            device = '移动设备'
        elif 'Tablet' in user_agent or 'iPad' in user_agent:
            device = '平板设备'
        
        return {
            'browser': browser,
            'os': os,
            'device': device
        }


class OperationLogExportSerializer(serializers.Serializer):
    """操作日志导出序列化器"""
    
    start_date = serializers.DateTimeField(required=False, help_text="开始时间")
    end_date = serializers.DateTimeField(required=False, help_text="结束时间")
    user_id = serializers.IntegerField(required=False, help_text="用户ID")
    operation_type = serializers.ChoiceField(
        choices=OperationLog.OPERATION_TYPE_CHOICES,
        required=False,
        help_text="操作类型"
    )
    export_format = serializers.ChoiceField(
        choices=[('excel', 'Excel'), ('pdf', 'PDF')],
        default='excel',
        help_text="导出格式"
    )
    
    def validate(self, data):
        """验证数据"""
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("开始时间不能大于结束时间")
        
        return data


class OperationLogStatsSerializer(serializers.Serializer):
    """操作日志统计序列化器"""
    
    total_count = serializers.IntegerField(help_text="总操作数")
    today_count = serializers.IntegerField(help_text="今日操作数")
    login_count = serializers.IntegerField(help_text="登录次数")
    operation_type_stats = serializers.DictField(help_text="操作类型统计")
    user_stats = serializers.ListField(help_text="用户操作统计")
    hourly_stats = serializers.ListField(help_text="小时统计")
