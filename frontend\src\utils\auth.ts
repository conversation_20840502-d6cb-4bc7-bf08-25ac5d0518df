/**
 * 认证和权限工具函数
 */
import { useAuthStore } from '@/stores/auth'
import type { DepartmentInfo } from '@/types/auth'

/**
 * 检查令牌是否过期
 * @param token JWT令牌
 * @returns 是否过期
 */
export function isTokenExpired(token: string): boolean {
  if (!token) return true
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    return payload.exp < currentTime
  } catch (error) {
    console.error('解析令牌失败:', error)
    return true
  }
}

/**
 * 获取令牌剩余时间（秒）
 * @param token JWT令牌
 * @returns 剩余时间（秒），如果过期返回0
 */
export function getTokenRemainingTime(token: string): number {
  if (!token) return 0
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    const remainingTime = payload.exp - currentTime
    return Math.max(0, remainingTime)
  } catch (error) {
    console.error('解析令牌失败:', error)
    return 0
  }
}

/**
 * 格式化错误消息
 * @param error 错误对象
 * @returns 格式化后的错误消息
 */
export function formatErrorMessage(error: any): string {
  if (error.response?.data?.message) {
    return error.response.data.message
  }
  
  if (error.response?.data?.details) {
    const details = error.response.data.details
    const messages = Object.values(details).flat()
    return messages.join(', ')
  }
  
  if (error.message) {
    return error.message
  }
  
  return '未知错误'
}

/**
 * 检查是否为认证错误
 * @param error 错误对象
 * @returns 是否为认证错误
 */
export function isAuthError(error: any): boolean {
  return error.response?.status === 401 || 
         error.response?.data?.code === 2003 || // TOKEN_EXPIRED
         error.response?.data?.code === 2004    // TOKEN_INVALID
}

/**
 * 检查是否为权限错误
 * @param error 错误对象
 * @returns 是否为权限错误
 */
export function isPermissionError(error: any): boolean {
  return error.response?.status === 403 || 
         error.response?.data?.code === 2101 || // PERMISSION_DENIED
         error.response?.data?.code === 2102    // INSUFFICIENT_PERMISSIONS
}

/**
 * 生成随机字符串
 * @param length 长度
 * @returns 随机字符串
 */
export function generateRandomString(length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

// ==================== 权限检查工具函数 ====================

/**
 * 检查用户是否拥有指定权限
 * @param permission 权限代码或权限数组
 * @returns 是否拥有权限
 */
export function hasPermission(permission: string | string[]): boolean {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    return false
  }

  const userPermissions = authStore.permissions

  if (typeof permission === 'string') {
    return userPermissions.includes(permission)
  }

  if (Array.isArray(permission)) {
    // 检查是否拥有任意一个权限
    return permission.some(p => userPermissions.includes(p))
  }

  return false
}

/**
 * 检查用户是否拥有所有指定权限
 * @param permissions 权限数组
 * @returns 是否拥有所有权限
 */
export function hasAllPermissions(permissions: string[]): boolean {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    return false
  }

  const userPermissions = authStore.permissions

  // 检查是否拥有所有权限
  return permissions.every(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否拥有任意一个指定权限
 * @param permissions 权限数组
 * @returns 是否拥有任意权限
 */
export function hasAnyPermission(permissions: string[]): boolean {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    return false
  }

  const userPermissions = authStore.permissions

  // 检查是否拥有任意一个权限
  return permissions.some(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否为超级管理员
 * @returns 是否为超级管理员
 */
export function isSuperAdmin(): boolean {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn || !authStore.userInfo) {
    return false
  }

  // 检查是否有超级管理员权限或特殊标识
  return authStore.permissions.includes('*') ||
         authStore.permissions.includes('super:admin') ||
         authStore.userInfo.username === 'admin'
}

/**
 * 检查用户在指定部门是否拥有权限
 * @param permission 权限代码
 * @param departmentId 部门ID
 * @returns 是否拥有权限
 */
export function hasPermissionInDepartment(permission: string, departmentId?: number): boolean {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    return false
  }

  // 如果是超级管理员，直接返回true
  if (isSuperAdmin()) {
    return true
  }

  // 如果没有指定部门，检查当前部门
  const targetDepartmentId = departmentId || authStore.currentDepartment

  // TODO: 实现部门级权限检查逻辑
  // 这里需要根据实际的权限模型来实现
  return hasPermission(permission)
}

/**
 * 获取用户的数据范围权限
 * @returns 数据范围类型
 */
export function getDataScope(): 'ALL' | 'DEPT_AND_SUB' | 'DEPT_ONLY' | 'SELF_ONLY' | 'CUSTOM' {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    return 'SELF_ONLY'
  }

  // 如果是超级管理员，返回全部权限
  if (isSuperAdmin()) {
    return 'ALL'
  }

  // TODO: 根据用户角色和权限确定数据范围
  // 这里需要根据实际的权限模型来实现
  if (hasPermission('data:all')) {
    return 'ALL'
  } else if (hasPermission('data:dept_and_sub')) {
    return 'DEPT_AND_SUB'
  } else if (hasPermission('data:dept_only')) {
    return 'DEPT_ONLY'
  } else {
    return 'SELF_ONLY'
  }
}

/**
 * 检查用户是否可以访问指定路由
 * @param routePath 路由路径
 * @param requiredPermissions 所需权限
 * @returns 是否可以访问
 */
export function canAccessRoute(routePath: string, requiredPermissions?: string[]): boolean {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    return false
  }

  // 如果没有权限要求，直接返回true
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true
  }

  // 如果是超级管理员，直接返回true
  if (isSuperAdmin()) {
    return true
  }

  // 检查是否拥有任意一个所需权限
  return hasAnyPermission(requiredPermissions)
}

/**
 * 获取用户可访问的菜单权限列表
 * @returns 菜单权限列表
 */
export function getAccessibleMenuPermissions(): string[] {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    return []
  }

  const userPermissions = authStore.permissions

  // 过滤出菜单相关的权限
  return userPermissions.filter(permission => {
    // 菜单权限通常以特定模式命名，如 'menu:xxx' 或直接是模块名
    return permission.includes(':') ||
           ['system', 'user', 'role', 'permission', 'department'].includes(permission)
  })
}

/**
 * 检查用户是否可以切换到指定部门
 * @param departmentId 部门ID
 * @returns 是否可以切换
 */
export function canSwitchToDepartment(departmentId: number): boolean {
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn || !authStore.userInfo) {
    return false
  }

  // TODO: 检查用户是否属于该部门
  // 这里需要根据实际的部门数据结构来实现
  return true
}

/**
 * 格式化权限显示名称
 * @param permission 权限代码
 * @returns 权限显示名称
 */
export function formatPermissionName(permission: string): string {
  const permissionMap: Record<string, string> = {
    'system': '系统管理',
    'user:manage': '用户管理',
    'user:create': '创建用户',
    'user:update': '编辑用户',
    'user:delete': '删除用户',
    'user:view': '查看用户',
    'role:manage': '角色管理',
    'role:create': '创建角色',
    'role:update': '编辑角色',
    'role:delete': '删除角色',
    'permission:manage': '权限管理',
    'department:manage': '部门管理',
    'audit:view': '查看审计日志',
    'data:all': '全部数据',
    'data:dept_and_sub': '本部门及下级',
    'data:dept_only': '仅本部门',
    'data:self_only': '仅本人'
  }

  return permissionMap[permission] || permission
}
