<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="权限分配"
    class="permission-assign-dialog"
    style="width: 800px"
    @update:show="$emit('update:visible', $event)"
  >
    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>
    
    <div v-else class="permission-assign-content">
      <!-- 角色信息 -->
      <div v-if="roleDetail" class="role-info">
        <div class="role-basic">
          <h3 class="role-name">{{ roleDetail.name }}</h3>
          <n-tag type="info" size="small">{{ roleDetail.code }}</n-tag>
        </div>
        <div class="role-stats">
          <span class="stat-item">当前权限: {{ selectedPermissions.length }}</span>
          <span class="stat-item">总权限: {{ totalPermissions }}</span>
        </div>
      </div>
      
      <!-- 权限树操作栏 -->
      <div class="permission-toolbar">
        <div class="toolbar-left">
          <n-space>
            <n-button size="small" @click="handleExpandAll">
              全部展开
            </n-button>
            <n-button size="small" @click="handleCollapseAll">
              全部折叠
            </n-button>
            <n-button size="small" @click="handleCheckAll">
              全选
            </n-button>
            <n-button size="small" @click="handleUncheckAll">
              全不选
            </n-button>
            <n-button size="small" @click="handleCheckByType('MENU')">
              选择菜单
            </n-button>
            <n-button size="small" @click="handleCheckByType('BUTTON')">
              选择按钮
            </n-button>
            <n-button size="small" @click="handleCheckByType('API')">
              选择API
            </n-button>
          </n-space>
        </div>
        
        <div class="toolbar-right">
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索权限..."
            size="small"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
          </n-input>
        </div>
      </div>
      
      <!-- 权限树 -->
      <div class="permission-tree-container">
        <n-tree
          :data="filteredPermissionTree"
          :checked-keys="selectedPermissions"
          :expanded-keys="expandedKeys"
          :loading="treeLoading"
          key-field="id"
          label-field="name"
          children-field="children"
          checkable
          cascade
          block-line
          @update:checked-keys="handlePermissionCheck"
          @update:expanded-keys="handleExpandedKeysChange"
          class="permission-tree"
        >
          <template #default="{ option }">
            <div class="permission-node">
              <n-icon
                v-if="option.icon"
                :component="getPermissionIcon(option.icon)"
                class="permission-icon"
              />
              <span class="permission-name">{{ option.name }}</span>
              <n-tag
                :type="getPermissionTypeColor(option.permission_type)"
                size="small"
                class="permission-type"
              >
                {{ getPermissionTypeLabel(option.permission_type) }}
              </n-tag>
              <span v-if="option.code" class="permission-code">{{ option.code }}</span>
              <span v-if="option.path" class="permission-path">{{ option.path }}</span>
            </div>
          </template>
        </n-tree>
      </div>
      
      <!-- 已选权限预览 -->
      <div v-if="selectedPermissions.length > 0" class="selected-permissions">
        <h4>已选权限 ({{ selectedPermissions.length }})</h4>
        <div class="selected-list">
          <n-tag
            v-for="permissionId in selectedPermissions"
            :key="permissionId"
            closable
            size="small"
            class="selected-tag"
            @close="handleRemovePermission(permissionId)"
          >
            {{ getPermissionName(permissionId) }}
          </n-tag>
        </div>
      </div>
    </div>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          保存权限
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { SearchIcon } from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { roleApi } from '@/api/role'
import type { RoleDetail, PermissionTree, PermissionType } from '@/types/role'

// Props
interface Props {
  visible: boolean
  roleId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  roleId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const treeLoading = ref(false)
const submitting = ref(false)
const searchKeyword = ref('')
const expandedKeys = ref<number[]>([])
const selectedPermissions = ref<number[]>([])
const roleDetail = ref<RoleDetail | null>(null)

// 计算属性
const filteredPermissionTree = computed(() => {
  if (!searchKeyword.value) {
    return roleStore.permissionTree
  }
  
  const filterTree = (nodes: PermissionTree[]): PermissionTree[] => {
    return nodes.filter(node => {
      const matchesKeyword = node.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                           node.code.toLowerCase().includes(searchKeyword.value.toLowerCase())
      
      if (matchesKeyword) {
        return true
      }
      
      if (node.children && node.children.length > 0) {
        const filteredChildren = filterTree(node.children)
        if (filteredChildren.length > 0) {
          return true
        }
      }
      
      return false
    }).map(node => ({
      ...node,
      children: node.children ? filterTree(node.children) : []
    }))
  }
  
  return filterTree(roleStore.permissionTree)
})

const totalPermissions = computed(() => {
  const countPermissions = (nodes: PermissionTree[]): number => {
    let count = 0
    nodes.forEach(node => {
      count += 1
      if (node.children && node.children.length > 0) {
        count += countPermissions(node.children)
      }
    })
    return count
  }
  
  return countPermissions(roleStore.permissionTree)
})

// 方法
const getPermissionIcon = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  return SearchIcon // 临时使用搜索图标
}

const getPermissionTypeColor = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return 'info'
    case 'BUTTON':
      return 'warning'
    case 'API':
      return 'success'
    default:
      return 'default'
  }
}

const getPermissionTypeLabel = (type: PermissionType) => {
  switch (type) {
    case 'MENU':
      return '菜单'
    case 'BUTTON':
      return '按钮'
    case 'API':
      return 'API'
    default:
      return type
  }
}

const getPermissionName = (permissionId: number) => {
  const findPermission = (nodes: PermissionTree[]): string => {
    for (const node of nodes) {
      if (node.id === permissionId) {
        return node.name
      }
      if (node.children && node.children.length > 0) {
        const found = findPermission(node.children)
        if (found) return found
      }
    }
    return `权限${permissionId}`
  }
  
  return findPermission(roleStore.permissionTree)
}

const getAllPermissionIds = (nodes: PermissionTree[]): number[] => {
  let ids: number[] = []
  nodes.forEach(node => {
    ids.push(node.id)
    if (node.children && node.children.length > 0) {
      ids = ids.concat(getAllPermissionIds(node.children))
    }
  })
  return ids
}

const getPermissionIdsByType = (nodes: PermissionTree[], type: PermissionType): number[] => {
  let ids: number[] = []
  nodes.forEach(node => {
    if (node.permission_type === type) {
      ids.push(node.id)
    }
    if (node.children && node.children.length > 0) {
      ids = ids.concat(getPermissionIdsByType(node.children, type))
    }
  })
  return ids
}

const handlePermissionCheck = (keys: number[]) => {
  selectedPermissions.value = keys
}

const handleExpandedKeysChange = (keys: number[]) => {
  expandedKeys.value = keys
}

const handleExpandAll = () => {
  expandedKeys.value = getAllPermissionIds(roleStore.permissionTree)
}

const handleCollapseAll = () => {
  expandedKeys.value = []
}

const handleCheckAll = () => {
  selectedPermissions.value = getAllPermissionIds(roleStore.permissionTree)
}

const handleUncheckAll = () => {
  selectedPermissions.value = []
}

const handleCheckByType = (type: PermissionType) => {
  const typeIds = getPermissionIdsByType(roleStore.permissionTree, type)
  const newSelected = [...new Set([...selectedPermissions.value, ...typeIds])]
  selectedPermissions.value = newSelected
}

const handleRemovePermission = (permissionId: number) => {
  selectedPermissions.value = selectedPermissions.value.filter(id => id !== permissionId)
}

const fetchRoleDetail = async () => {
  if (!props.roleId) return
  
  try {
    loading.value = true
    const response = await roleStore.fetchRoleDetail(props.roleId)
    
    if (response.code === 200) {
      roleDetail.value = response.data
      selectedPermissions.value = response.data.permission_ids
    }
  } catch (error) {
    message.error('获取角色详情失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!props.roleId) return
  
  try {
    submitting.value = true
    
    await roleApi.assignPermissionsToRole(props.roleId, selectedPermissions.value)
    
    emit('success')
  } catch (error) {
    message.error('权限分配失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(async () => {
      // 获取权限树
      await roleStore.fetchPermissionTree()
      
      // 获取角色详情
      if (props.roleId) {
        await fetchRoleDetail()
      }
    })
  }
})

watch(() => props.roleId, (newRoleId) => {
  if (props.visible && newRoleId) {
    fetchRoleDetail()
  }
})
</script>

<style scoped>
.permission-assign-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.permission-assign-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.role-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.role-basic {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.role-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  font-size: 14px;
  color: #6b7280;
}

.permission-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
}

.toolbar-left {
  flex: 1;
}

.toolbar-right {
  flex-shrink: 0;
}

.permission-tree-container {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
}

.permission-tree {
  width: 100%;
}

.permission-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.permission-icon {
  flex-shrink: 0;
  color: #6b7280;
}

.permission-name {
  flex: 1;
  font-weight: 500;
  color: #1f2937;
}

.permission-type {
  flex-shrink: 0;
}

.permission-code {
  flex-shrink: 0;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.permission-path {
  flex-shrink: 0;
  font-size: 11px;
  color: #9ca3af;
}

.selected-permissions {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.selected-permissions h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
}

.selected-tag {
  margin: 0;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
