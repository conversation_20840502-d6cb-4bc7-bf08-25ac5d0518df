<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { RouterView, useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import SideMenu from '@/components/layout/SideMenu.vue'
import Breadcrumb from '@/components/layout/Breadcrumb.vue'
import DepartmentSwitcher from '@/components/layout/DepartmentSwitcher.vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()

// 是否显示布局（登录页等不显示）
const showLayout = computed(() => {
  return authStore.isLoggedIn && !route.meta.hidden
})

// 是否显示侧边栏
const showSidebar = computed(() => {
  return showLayout.value && route.name !== 'login'
})

// 处理登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    message.success('登出成功')
    router.push('/login')
  } catch (error) {
    message.error('登出失败')
  }
}

onMounted(() => {
  // 初始化认证状态
  authStore.initializeAuth()
})
</script>

<template>
  <div class="app-container">
    <!-- 登录页面或隐藏页面 -->
    <div v-if="!showLayout" class="full-page">
      <RouterView />
    </div>

    <!-- 主布局 -->
    <div v-else class="app-layout">
      <!-- 侧边栏 -->
      <aside v-if="showSidebar" class="sidebar">
        <SideMenu />
      </aside>

      <!-- 主内容区域 -->
      <div class="main-content" :class="{ 'full-width': !showSidebar }">
        <!-- 顶部导航栏 -->
        <header class="top-header">
          <!-- 面包屑导航 -->
          <div class="breadcrumb-section">
            <Breadcrumb />
          </div>

          <!-- 右侧工具栏 -->
          <div class="toolbar">
            <!-- 部门切换器 -->
            <DepartmentSwitcher />

            <!-- 用户信息 -->
            <div v-if="authStore.userInfo" class="user-info">
              <span class="user-name">{{ authStore.userInfo.nickname || authStore.userInfo.username }}</span>
              <n-button text @click="handleLogout" class="logout-btn">
                登出
              </n-button>
            </div>
          </div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
          <RouterView />
        </main>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  overflow: hidden;
}

.full-page {
  height: 100vh;
  width: 100vw;
}

.app-layout {
  display: flex;
  height: 100vh;
}

.sidebar {
  flex-shrink: 0;
  width: 240px;
  background: #fff;
  border-right: 1px solid #e5e7eb;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content.full-width {
  width: 100%;
}

.top-header {
  height: 64px;
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  flex-shrink: 0;
}

.breadcrumb-section {
  flex: 1;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.logout-btn {
  font-size: 14px;
  color: #6b7280;
}

.logout-btn:hover {
  color: #374151;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 64px;
  }

  .toolbar {
    gap: 8px;
  }

  .user-name {
    display: none;
  }
}
</style>
