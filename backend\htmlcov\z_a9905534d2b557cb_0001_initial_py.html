<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for apps\users\migrations\0001_initial.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>apps\users\migrations\0001_initial.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">6 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">6<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_e7a2fb5ce97bac5d_apps_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># Generated by Django 4.2.23 on 2025-07-28 08:26</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">from</span> <span class="nam">django</span><span class="op">.</span><span class="nam">db</span> <span class="key">import</span> <span class="nam">migrations</span><span class="op">,</span> <span class="nam">models</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">django</span><span class="op">.</span><span class="nam">utils</span><span class="op">.</span><span class="nam">timezone</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">class</span> <span class="nam">Migration</span><span class="op">(</span><span class="nam">migrations</span><span class="op">.</span><span class="nam">Migration</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t">    <span class="nam">initial</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">    <span class="nam">dependencies</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">        <span class="op">(</span><span class="str">'auth'</span><span class="op">,</span> <span class="str">'0012_alter_user_first_name_max_length'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="nam">operations</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'UserProfile'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">                <span class="op">(</span><span class="str">'password'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">max_length</span><span class="op">=</span><span class="num">128</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'password'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">                <span class="op">(</span><span class="str">'last_login'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'last login'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">                <span class="op">(</span><span class="str">'is_superuser'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Designates that this user has all permissions without explicitly assigning them.'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'superuser status'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">                <span class="op">(</span><span class="str">'first_name'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">150</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'first name'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">                <span class="op">(</span><span class="str">'last_name'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">150</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'last name'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">                <span class="op">(</span><span class="str">'is_staff'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Designates whether the user can log into this admin site.'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'staff status'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">                <span class="op">(</span><span class="str">'date_joined'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">utils</span><span class="op">.</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">now</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'date joined'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#21019;&#24314;&#26102;&#38388;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#26356;&#26032;&#26102;&#38388;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">                <span class="op">(</span><span class="str">'is_deleted'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#26159;&#21542;&#21024;&#38500;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">                <span class="op">(</span><span class="str">'deleted_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#21024;&#38500;&#26102;&#38388;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">                <span class="op">(</span><span class="str">'username'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">max_length</span><span class="op">=</span><span class="num">150</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#29992;&#25143;&#21517;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">                <span class="op">(</span><span class="str">'nickname'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#26165;&#31216;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">                <span class="op">(</span><span class="str">'email'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">EmailField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">254</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#37038;&#31665;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">                <span class="op">(</span><span class="str">'phone'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">11</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#25163;&#26426;&#21495;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">                <span class="op">(</span><span class="str">'wechat_work_id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#20225;&#19994;&#24494;&#20449;ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">                <span class="op">(</span><span class="str">'avatar'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">URLField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#22836;&#20687;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">                <span class="op">(</span><span class="str">'is_active'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#26159;&#21542;&#28608;&#27963;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">                <span class="op">(</span><span class="str">'last_login_ip'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">GenericIPAddressField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#26368;&#21518;&#30331;&#24405;IP'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">                <span class="op">(</span><span class="str">'last_login_time'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#26368;&#21518;&#30331;&#24405;&#26102;&#38388;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">                <span class="op">(</span><span class="str">'login_fail_count'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">IntegerField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#30331;&#24405;&#22833;&#36133;&#27425;&#25968;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">                <span class="op">(</span><span class="str">'locked_until'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#38145;&#23450;&#21040;&#26399;&#26102;&#38388;'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">                <span class="op">(</span><span class="str">'groups'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ManyToManyField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'The groups this user belongs to. A user will get all permissions granted to each of their groups.'</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'user_set'</span><span class="op">,</span> <span class="nam">related_query_name</span><span class="op">=</span><span class="str">'user'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'auth.group'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'groups'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">                <span class="op">(</span><span class="str">'user_permissions'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ManyToManyField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Specific permissions for this user.'</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'user_set'</span><span class="op">,</span> <span class="nam">related_query_name</span><span class="op">=</span><span class="str">'user'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'auth.permission'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'user permissions'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'&#29992;&#25143;'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'&#29992;&#25143;'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">                <span class="str">'db_table'</span><span class="op">:</span> <span class="str">'auth_user_profile'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_e7a2fb5ce97bac5d_apps_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
</footer>
</body>
</html>
