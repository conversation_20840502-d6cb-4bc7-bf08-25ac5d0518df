<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for apps\common\urls.py: 0%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>apps\common\urls.py</b>:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">7 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">0<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">7<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_e2d79787564ab1b6_test_data_scope_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_ecc233269ded7be5_utils_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># -*- coding: utf-8 -*-</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">&#20844;&#20849;&#27169;&#22359;URL&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">django</span><span class="op">.</span><span class="nam">urls</span> <span class="key">import</span> <span class="nam">path</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">test_views</span> <span class="key">import</span> <span class="nam">TestExceptionView</span><span class="op">,</span> <span class="nam">test_response_helper</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">example_views</span> <span class="key">import</span> <span class="nam">ExceptionDemoView</span><span class="op">,</span> <span class="nam">create_user_demo</span><span class="op">,</span> <span class="nam">paginated_demo</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="op">.</span> <span class="key">import</span> <span class="nam">file_views</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="op">.</span> <span class="key">import</span> <span class="nam">config_views</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="nam">app_name</span> <span class="op">=</span> <span class="str">'common'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="nam">urlpatterns</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">    <span class="com"># &#27979;&#35797;&#24322;&#24120;&#22788;&#29702;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'test/exception/'</span><span class="op">,</span> <span class="nam">TestExceptionView</span><span class="op">.</span><span class="nam">as_view</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'test_exception'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="com"># &#27979;&#35797;&#21709;&#24212;&#21161;&#25163;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'test/response/'</span><span class="op">,</span> <span class="nam">test_response_helper</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'test_response'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">    <span class="com"># &#24322;&#24120;&#22788;&#29702;&#28436;&#31034;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'demo/exception/'</span><span class="op">,</span> <span class="nam">ExceptionDemoView</span><span class="op">.</span><span class="nam">as_view</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'exception_demo'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'demo/create-user/'</span><span class="op">,</span> <span class="nam">create_user_demo</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'create_user_demo'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'demo/paginated/'</span><span class="op">,</span> <span class="nam">paginated_demo</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'paginated_demo'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="com"># &#25991;&#20214;&#19978;&#20256;&#25509;&#21475;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'upload/'</span><span class="op">,</span> <span class="nam">file_views</span><span class="op">.</span><span class="nam">upload_file</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'upload_file'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'upload/validate/'</span><span class="op">,</span> <span class="nam">file_views</span><span class="op">.</span><span class="nam">validate_file</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'validate_file'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'upload/config/'</span><span class="op">,</span> <span class="nam">file_views</span><span class="op">.</span><span class="nam">get_upload_config</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'upload_config'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'upload/batch/'</span><span class="op">,</span> <span class="nam">file_views</span><span class="op">.</span><span class="nam">batch_upload</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'batch_upload'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'files/'</span><span class="op">,</span> <span class="nam">file_views</span><span class="op">.</span><span class="nam">list_uploaded_files</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'list_files'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'files/delete/'</span><span class="op">,</span> <span class="nam">file_views</span><span class="op">.</span><span class="nam">delete_file</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'delete_file'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="com"># &#37197;&#32622;&#31649;&#29702;&#25509;&#21475;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/categories/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">get_config_categories</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'config_categories'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">get_configs</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'get_configs'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/&lt;str:key>/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">get_config</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'get_config'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/&lt;str:key>/update/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">update_config</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'update_config'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/&lt;str:key>/reset/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">reset_config</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'reset_config'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/batch/update/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">batch_update_configs</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'batch_update_configs'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/validate/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">validate_configs</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'validate_configs'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/export/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">export_configs</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'export_configs'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">    <span class="nam">path</span><span class="op">(</span><span class="str">'config/import/'</span><span class="op">,</span> <span class="nam">config_views</span><span class="op">.</span><span class="nam">import_configs</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'import_configs'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t"><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_e2d79787564ab1b6_test_data_scope_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_ecc233269ded7be5_utils_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-12 10:58 +0800
        </p>
    </div>
</footer>
</body>
</html>
