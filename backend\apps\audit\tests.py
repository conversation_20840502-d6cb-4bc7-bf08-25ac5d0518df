"""
审计日志模块 - 单元测试
"""
import json
from datetime import timed<PERSON>ta
from django.test import TestCase, RequestFactory
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock
from django.test.utils import override_settings

from .models import OperationLog
from .services import ExceptionLogService, AuditLogService
from .tasks import cleanup_old_operation_logs, generate_audit_statistics
from apps.common.middleware import AuditLogMiddleware

User = get_user_model()


class OperationLogModelTest(TestCase):
    """操作日志模型测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
    
    def test_create_operation_log(self):
        """测试创建操作日志"""
        log = OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='用户登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            user_agent='Test Agent',
            status_code=200,
            response_time=100
        )
        
        self.assertEqual(log.user, self.user)
        self.assertEqual(log.operation_type, 'LOGIN')
        self.assertEqual(log.operation_desc, '用户登录')
        self.assertEqual(log.status_code, 200)
        self.assertTrue(log.created_at)
    
    def test_operation_log_str(self):
        """测试操作日志字符串表示"""
        log = OperationLog.objects.create(
            user=self.user,
            operation_type='CREATE',
            operation_desc='创建用户',
            method='POST',
            path='/api/users/',
            ip_address='127.0.0.1',
            status_code=201,
            response_time=150
        )
        
        expected_str = f"{self.user.nickname} - 创建用户 ({log.created_at.strftime('%Y-%m-%d %H:%M')})"
        self.assertEqual(str(log), expected_str)
    
    def test_cleanup_old_logs(self):
        """测试清理过期日志"""
        # 创建新日志
        OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='最近登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            status_code=200,
            response_time=100
        )
        
        # 创建旧日志
        old_log = OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='旧登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            status_code=200,
            response_time=100
        )
        
        # 手动设置创建时间为91天前
        old_time = timezone.now() - timedelta(days=91)
        OperationLog.objects.filter(id=old_log.id).update(created_at=old_time)
        
        # 执行清理
        deleted_count, _ = OperationLog.cleanup_old_logs(90)
        
        self.assertEqual(deleted_count, 1)
        self.assertEqual(OperationLog.objects.count(), 1)


class ExceptionLogServiceTest(TestCase):
    """异常日志服务测试"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
    
    def test_log_exception_with_request(self):
        """测试记录带请求信息的异常"""
        request = self.factory.post('/api/test/', {'data': 'test'})
        request.user = self.user
        
        try:
            raise ValueError("测试异常")
        except ValueError as e:
            ExceptionLogService.log_exception(e, request=request)
        
        # 检查是否创建了错误日志
        error_logs = OperationLog.objects.filter(operation_type='ERROR')
        self.assertEqual(error_logs.count(), 1)
        
        error_log = error_logs.first()
        self.assertEqual(error_log.user, self.user)
        self.assertIn('ValueError', error_log.operation_desc)
        self.assertEqual(error_log.path, '/api/test/')
        self.assertEqual(error_log.method, 'POST')
    
    def test_log_exception_without_request(self):
        """测试记录不带请求信息的异常"""
        try:
            raise RuntimeError("系统错误")
        except RuntimeError as e:
            ExceptionLogService.log_exception(e)
        
        # 不应该创建操作日志（因为没有请求信息）
        error_logs = OperationLog.objects.filter(operation_type='ERROR')
        self.assertEqual(error_logs.count(), 0)
    
    def test_safe_serialize_request_data(self):
        """测试安全序列化请求数据"""
        request = self.factory.post('/api/test/', {
            'username': 'testuser',
            'password': 'secret123',
            'normal_field': 'normal_value'
        })
        # 模拟DRF的request.data
        request.data = {
            'username': 'testuser',
            'password': 'secret123',
            'normal_field': 'normal_value'
        }

        serialized = ExceptionLogService._safe_serialize_request_data(request)

        self.assertIn('POST', serialized)
        self.assertEqual(serialized['POST']['username'], 'testuser')
        self.assertEqual(serialized['POST']['password'], '***FILTERED***')
        self.assertEqual(serialized['POST']['normal_field'], 'normal_value')


class AuditLogServiceTest(TestCase):
    """审计日志服务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        self.factory = RequestFactory()
    
    def test_create_operation_log(self):
        """测试创建操作日志"""
        request = self.factory.get('/api/users/')
        request.user = self.user
        
        log = AuditLogService.create_operation_log(
            user=self.user,
            operation_type='QUERY',
            operation_desc='查询用户列表',
            request=request,
            status_code=200,
            response_time=50
        )
        
        self.assertIsNotNone(log)
        self.assertEqual(log.user, self.user)
        self.assertEqual(log.operation_type, 'QUERY')
        self.assertEqual(log.method, 'GET')
        self.assertEqual(log.path, '/api/users/')
        self.assertEqual(log.status_code, 200)
        self.assertEqual(log.response_time, 50)
    
    def test_get_user_operation_stats(self):
        """测试获取用户操作统计"""
        # 创建测试数据
        for i in range(5):
            OperationLog.objects.create(
                user=self.user,
                operation_type='LOGIN',
                operation_desc=f'登录{i}',
                method='POST',
                path='/api/auth/login',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=100
            )
        
        for i in range(3):
            OperationLog.objects.create(
                user=self.user,
                operation_type='QUERY',
                operation_desc=f'查询{i}',
                method='GET',
                path='/api/users/',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=50
            )
        
        stats = AuditLogService.get_user_operation_stats(self.user, days=30)
        
        self.assertEqual(stats['total_operations'], 8)
        self.assertEqual(stats['operation_type_stats']['LOGIN'], 5)
        self.assertEqual(stats['operation_type_stats']['QUERY'], 3)
        self.assertEqual(stats['period_days'], 30)
        self.assertIn('daily_stats', stats)
    
    def test_get_system_health_stats(self):
        """测试获取系统健康统计"""
        # 创建测试数据
        now = timezone.now()
        
        # 正常请求
        for i in range(10):
            OperationLog.objects.create(
                user=self.user,
                operation_type='QUERY',
                operation_desc=f'正常请求{i}',
                method='GET',
                path='/api/test/',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=100,
                created_at=now - timedelta(minutes=30)
            )
        
        # 错误请求
        for i in range(2):
            OperationLog.objects.create(
                user=self.user,
                operation_type='ERROR',
                operation_desc=f'错误请求{i}',
                method='GET',
                path='/api/test/',
                ip_address='127.0.0.1',
                status_code=500,
                response_time=200,
                created_at=now - timedelta(minutes=30)
            )
        
        stats = AuditLogService.get_system_health_stats()
        
        self.assertIn('error_rate', stats)
        self.assertIn('avg_response_time', stats)
        self.assertIn('active_users_24h', stats)
        self.assertIn('total_requests_1h', stats)
        self.assertIn('error_requests_1h', stats)


class AuditLogMiddlewareTest(TestCase):
    """审计日志中间件测试"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        self.middleware = AuditLogMiddleware(lambda request: None)
    
    def test_process_request(self):
        """测试请求处理"""
        request = self.factory.get('/api/users/')
        
        response = self.middleware.process_request(request)
        
        self.assertIsNone(response)
        self.assertTrue(hasattr(request, '_start_time'))
    
    def test_should_log_operation(self):
        """测试是否应该记录操作"""
        request = self.factory.post('/api/users/')
        response = MagicMock()
        response.status_code = 201
        
        should_log = self.middleware._should_log_operation(request, response)
        self.assertTrue(should_log)
        
        # 测试跳过的路径
        request = self.factory.get('/static/css/style.css')
        should_log = self.middleware._should_log_operation(request, response)
        self.assertFalse(should_log)
    
    def test_get_operation_desc(self):
        """测试获取操作描述"""
        request = self.factory.post('/api/auth/login')
        response = MagicMock()
        response.status_code = 200
        
        desc = self.middleware._get_operation_desc(request, response)
        self.assertEqual(desc, '用户登录成功')
        
        response.status_code = 400
        desc = self.middleware._get_operation_desc(request, response)
        self.assertEqual(desc, '用户登录失败')


class OperationLogViewSetTest(APITestCase):
    """操作日志视图集测试"""

    def setUp(self):
        # 在测试中禁用权限中间件，但保留审计中间件
        from django.test.utils import override_settings
        self.settings_override = override_settings(
            MIDDLEWARE=[
                'django.middleware.security.SecurityMiddleware',
                'corsheaders.middleware.CorsMiddleware',
                'django.contrib.sessions.middleware.SessionMiddleware',
                'django.middleware.common.CommonMiddleware',
                'django.middleware.csrf.CsrfViewMiddleware',
                'django.contrib.auth.middleware.AuthenticationMiddleware',
                'django.contrib.messages.middleware.MessageMiddleware',
                'django.middleware.clickjacking.XFrameOptionsMiddleware',
                'apps.common.middleware.JWTAuthenticationMiddleware',
                # 'apps.common.middleware.PermissionMiddleware',  # 测试时禁用权限中间件
                'apps.common.middleware.AuditLogMiddleware',  # 保留审计中间件
            ]
        )
        self.settings_override.enable()

        self.user = User.objects.create_superuser(
            username='testuser',
            password='testpass123',
            nickname='测试用户',
            email='<EMAIL>'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 创建测试数据
        for i in range(5):
            OperationLog.objects.create(
                user=self.user,
                operation_type='LOGIN',
                operation_desc=f'登录{i}',
                method='POST',
                path='/api/auth/login',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=100
            )

    def tearDown(self):
        """清理测试设置"""
        if hasattr(self, 'settings_override'):
            self.settings_override.disable()
    
    def test_list_operation_logs(self):
        """测试获取操作日志列表"""
        response = self.client.get('/api/audit/logs/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 检查响应结构
        if 'data' in response.data:
            if 'results' in response.data['data']:
                self.assertEqual(len(response.data['data']['results']), 5)
            else:
                self.assertEqual(len(response.data['data']), 5)
        else:
            # DRF默认分页响应
            if 'results' in response.data:
                self.assertEqual(len(response.data['results']), 5)
            else:
                self.assertEqual(len(response.data), 5)
    
    def test_filter_operation_logs(self):
        """测试过滤操作日志"""
        response = self.client.get('/api/audit/logs/', {
            'operation_type': 'LOGIN',
            'user_id': self.user.id
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 检查响应结构
        if 'data' in response.data and 'results' in response.data['data']:
            self.assertEqual(len(response.data['data']['results']), 5)
        elif 'results' in response.data:
            self.assertEqual(len(response.data['results']), 5)
    
    def test_get_operation_log_detail(self):
        """测试获取操作日志详情"""
        log = OperationLog.objects.first()
        response = self.client.get(f'/api/audit/logs/{log.id}/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 检查响应结构
        if 'data' in response.data:
            self.assertEqual(response.data['data']['id'], log.id)
        else:
            self.assertEqual(response.data['id'], log.id)
    
    def test_get_stats(self):
        """测试获取统计信息"""
        response = self.client.get('/api/audit/logs/stats/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 检查响应结构
        data = response.data['data'] if 'data' in response.data else response.data
        self.assertIn('total_count', data)
        self.assertIn('operation_type_stats', data)
    
    def test_export_logs(self):
        """测试导出日志"""
        response = self.client.post('/api/audit/logs/export/', {
            'export_format': 'excel'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response['Content-Type'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )


class AuditLogMiddlewareIntegrationTest(TestCase):
    """审计日志中间件集成测试"""

    def setUp(self):
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        self.middleware = AuditLogMiddleware(lambda request: None)

    def test_middleware_with_authenticated_user(self):
        """测试中间件处理认证用户请求"""
        request = self.factory.post('/api/users/', {'name': 'test'})
        request.user = self.user
        request._start_time = timezone.now().timestamp()

        # 模拟响应
        response = MagicMock()
        response.status_code = 201

        # 处理响应
        result = self.middleware.process_response(request, response)

        # 验证日志是否创建
        logs = OperationLog.objects.filter(user=self.user)
        self.assertEqual(logs.count(), 1)

        log = logs.first()
        self.assertEqual(log.operation_type, 'CREATE')
        self.assertEqual(log.method, 'POST')
        self.assertEqual(log.path, '/api/users/')
        self.assertEqual(log.status_code, 201)

    def test_middleware_skip_static_paths(self):
        """测试中间件跳过静态文件路径"""
        request = self.factory.get('/static/css/style.css')
        request.user = self.user
        request._start_time = timezone.now().timestamp()

        response = MagicMock()
        response.status_code = 200

        # 处理响应
        result = self.middleware.process_response(request, response)

        # 验证没有创建日志
        logs = OperationLog.objects.filter(path='/static/css/style.css')
        self.assertEqual(logs.count(), 0)
