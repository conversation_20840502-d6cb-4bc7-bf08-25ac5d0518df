"""
审计日志模块 - 任务测试
"""
import os
import tempfile
from datetime import timedelta
from django.test import TestCase, override_settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from unittest.mock import patch, MagicMock

from .models import OperationLog
from .tasks import (
    cleanup_old_operation_logs,
    cleanup_large_operation_logs,
    generate_audit_statistics,
    archive_old_operation_logs,
    comprehensive_audit_cleanup
)

User = get_user_model()


class AuditTasksTest(TestCase):
    """审计任务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        
        # 清理缓存
        cache.clear()
    
    def test_cleanup_old_operation_logs(self):
        """测试清理过期操作日志任务"""
        # 创建新日志
        recent_log = OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='最近登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            status_code=200,
            response_time=100
        )
        
        # 创建旧日志
        old_log = OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='旧登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            status_code=200,
            response_time=100
        )
        
        # 手动设置创建时间为91天前
        old_time = timezone.now() - timedelta(days=91)
        OperationLog.objects.filter(id=old_log.id).update(created_at=old_time)
        
        # 执行任务
        result = cleanup_old_operation_logs(days=90)
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['deleted_count'], 1)
        self.assertEqual(result['retention_days'], 90)
        
        # 验证只剩下新日志
        remaining_logs = OperationLog.objects.all()
        self.assertEqual(remaining_logs.count(), 1)
        self.assertEqual(remaining_logs.first().id, recent_log.id)
    
    def test_cleanup_large_operation_logs(self):
        """测试清理过多操作日志任务"""
        # 创建10条日志
        logs = []
        for i in range(10):
            log = OperationLog.objects.create(
                user=self.user,
                operation_type='QUERY',
                operation_desc=f'查询{i}',
                method='GET',
                path='/api/test/',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=100
            )
            logs.append(log)
        
        # 设置最大记录数为5
        result = cleanup_large_operation_logs(max_records=5)
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['deleted_count'], 5)
        self.assertEqual(result['current_count'], 5)
        self.assertEqual(result['max_records'], 5)
        
        # 验证只剩下5条最新的日志
        remaining_logs = OperationLog.objects.all().order_by('created_at')
        self.assertEqual(remaining_logs.count(), 5)
    
    def test_cleanup_large_operation_logs_no_cleanup_needed(self):
        """测试不需要清理的情况"""
        # 创建3条日志
        for i in range(3):
            OperationLog.objects.create(
                user=self.user,
                operation_type='QUERY',
                operation_desc=f'查询{i}',
                method='GET',
                path='/api/test/',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=100
            )
        
        # 设置最大记录数为5
        result = cleanup_large_operation_logs(max_records=5)
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['deleted_count'], 0)
        self.assertEqual(result['current_count'], 3)
        self.assertEqual(result['max_records'], 5)
    
    def test_generate_audit_statistics(self):
        """测试生成审计统计任务"""
        # 创建测试数据
        now = timezone.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 今日登录日志
        for i in range(3):
            OperationLog.objects.create(
                user=self.user,
                operation_type='LOGIN',
                operation_desc=f'今日登录{i}',
                method='POST',
                path='/api/auth/login',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=100,
                created_at=today_start + timedelta(hours=i)
            )
        
        # 今日查询日志
        for i in range(2):
            OperationLog.objects.create(
                user=self.user,
                operation_type='QUERY',
                operation_desc=f'今日查询{i}',
                method='GET',
                path='/api/users/',
                ip_address='127.0.0.1',
                status_code=200,
                response_time=50,
                created_at=today_start + timedelta(hours=i + 10)
            )
        
        # 错误日志
        OperationLog.objects.create(
            user=self.user,
            operation_type='ERROR',
            operation_desc='系统错误',
            method='GET',
            path='/api/test/',
            ip_address='127.0.0.1',
            status_code=500,
            response_time=0,
            created_at=today_start + timedelta(hours=12)
        )
        
        # 执行任务
        result = generate_audit_statistics()
        
        self.assertEqual(result['status'], 'success')
        
        stats = result['stats']
        self.assertEqual(stats['total_logs'], 6)
        self.assertEqual(stats['today_logs'], 6)
        self.assertEqual(stats['operation_type_stats']['LOGIN'], 3)
        self.assertEqual(stats['operation_type_stats']['QUERY'], 2)
        self.assertEqual(stats['operation_type_stats']['ERROR'], 1)
        self.assertEqual(stats['error_stats'][500], 1)
        self.assertEqual(len(stats['active_users']), 1)
        # 检查hourly_stats是否存在
        if 'hourly_stats' in stats:
            self.assertEqual(len(stats['hourly_stats']), 24)
        
        # 验证缓存
        cached_stats = cache.get('audit_statistics')
        self.assertIsNotNone(cached_stats)
        self.assertEqual(cached_stats['total_logs'], 6)
    
    @override_settings(AUDIT_LOG_ARCHIVE_DIR=tempfile.gettempdir())
    def test_archive_old_operation_logs_json(self):
        """测试归档旧日志任务（JSON格式）"""
        # 创建新日志
        recent_log = OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='最近登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            status_code=200,
            response_time=100
        )
        
        # 创建旧日志
        old_log = OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='旧登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            status_code=200,
            response_time=100
        )
        
        # 手动设置创建时间为181天前
        old_time = timezone.now() - timedelta(days=181)
        OperationLog.objects.filter(id=old_log.id).update(created_at=old_time)
        
        # 执行归档任务
        result = archive_old_operation_logs(days=180, archive_format='json')
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['archived_count'], 1)
        self.assertEqual(result['deleted_count'], 1)
        self.assertEqual(result['archive_days'], 180)
        
        # 验证归档文件存在
        archive_file = result['archive_file']
        self.assertTrue(os.path.exists(archive_file))
        
        # 验证归档文件内容
        import json
        with open(archive_file, 'r', encoding='utf-8') as f:
            archived_data = json.load(f)
        
        self.assertEqual(len(archived_data), 1)
        self.assertEqual(archived_data[0]['operation_desc'], '旧登录')
        self.assertEqual(archived_data[0]['user_nickname'], self.user.nickname)
        
        # 清理归档文件
        os.remove(archive_file)
        
        # 验证只剩下新日志
        remaining_logs = OperationLog.objects.all()
        self.assertEqual(remaining_logs.count(), 1)
        self.assertEqual(remaining_logs.first().id, recent_log.id)
    
    @override_settings(AUDIT_LOG_ARCHIVE_DIR=tempfile.gettempdir())
    def test_archive_old_operation_logs_csv(self):
        """测试归档旧日志任务（CSV格式）"""
        # 创建旧日志
        old_log = OperationLog.objects.create(
            user=self.user,
            operation_type='QUERY',
            operation_desc='旧查询',
            method='GET',
            path='/api/users/',
            ip_address='***********',
            status_code=200,
            response_time=150
        )
        
        # 手动设置创建时间为181天前
        old_time = timezone.now() - timedelta(days=181)
        OperationLog.objects.filter(id=old_log.id).update(created_at=old_time)
        
        # 执行归档任务
        result = archive_old_operation_logs(days=180, archive_format='csv')
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['archived_count'], 1)
        
        # 验证归档文件存在
        archive_file = result['archive_file']
        self.assertTrue(os.path.exists(archive_file))
        
        # 验证归档文件内容
        import csv
        with open(archive_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            rows = list(reader)
        
        self.assertEqual(len(rows), 2)  # 表头 + 1条数据
        self.assertEqual(rows[0][0], 'ID')  # 表头
        self.assertEqual(rows[1][4], '旧查询')  # 操作描述
        
        # 清理归档文件
        os.remove(archive_file)
    
    def test_archive_old_operation_logs_no_data(self):
        """测试没有需要归档的数据"""
        # 创建新日志
        OperationLog.objects.create(
            user=self.user,
            operation_type='LOGIN',
            operation_desc='最近登录',
            method='POST',
            path='/api/auth/login',
            ip_address='127.0.0.1',
            status_code=200,
            response_time=100
        )
        
        # 执行归档任务
        result = archive_old_operation_logs(days=180)
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['archived_count'], 0)
    
    @patch('apps.audit.tasks.cleanup_old_operation_logs.delay')
    @patch('apps.audit.tasks.cleanup_large_operation_logs.delay')
    @patch('apps.audit.tasks.generate_audit_statistics.delay')
    def test_comprehensive_audit_cleanup(self, mock_stats, mock_count_cleanup, mock_time_cleanup):
        """测试综合审计清理任务"""
        # 模拟任务结果
        mock_time_cleanup.return_value.get.return_value = {
            'status': 'success',
            'deleted_count': 10
        }
        mock_count_cleanup.return_value.get.return_value = {
            'status': 'success',
            'deleted_count': 5
        }
        mock_stats.return_value.get.return_value = {
            'status': 'success',
            'stats': {'total_logs': 100}
        }
        
        # 执行综合清理任务
        result = comprehensive_audit_cleanup()
        
        # 验证所有子任务都被调用
        mock_time_cleanup.assert_called_once()
        mock_count_cleanup.assert_called_once()
        mock_stats.assert_called_once()
        
        # 验证结果
        self.assertIn('time_cleanup', result)
        self.assertIn('count_cleanup', result)
        self.assertIn('statistics', result)
        self.assertEqual(result['time_cleanup']['status'], 'success')
        self.assertEqual(result['count_cleanup']['status'], 'success')
        self.assertEqual(result['statistics']['status'], 'success')
