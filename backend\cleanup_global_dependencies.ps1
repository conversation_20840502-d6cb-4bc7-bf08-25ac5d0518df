# HEIM项目全局依赖清理脚本
# 安全清理全局Python环境中的HEIM项目相关依赖

Write-Host "=== HEIM项目全局依赖清理 ===" -ForegroundColor Yellow
Write-Host "警告: 此脚本将从全局Python环境中移除HEIM项目相关的依赖包" -ForegroundColor Red
Write-Host "请确保这些包不被其他项目使用" -ForegroundColor Red

# 需要清理的HEIM项目相关依赖
$heimDependencies = @(
    "Django",
    "djangorestframework", 
    "djangorestframework-simplejwt",
    "django-cors-headers",
    "django-mptt",
    "django-guardian", 
    "django-extensions",
    "django-debug-toolbar",
    "django-simple-captcha",
    "django-ranged-response",
    "django-js-asset",
    "celery",
    "redis",
    "python-decouple",
    "python-magic",
    "python-magic-bin",
    "pillow",
    "reportlab",
    "xlsxwriter"
)

# 检查当前全局环境中的相关包
Write-Host "检查全局环境中的HEIM相关依赖..." -ForegroundColor Blue
$installedPackages = python -m pip list --format=freeze | ForEach-Object { $_.Split('==')[0] }

$toRemove = @()
foreach ($package in $heimDependencies) {
    if ($installedPackages -contains $package) {
        $toRemove += $package
        Write-Host "发现: $package" -ForegroundColor Yellow
    }
}

if ($toRemove.Count -eq 0) {
    Write-Host "✅ 全局环境中未发现HEIM项目相关依赖" -ForegroundColor Green
    exit 0
}

Write-Host "发现 $($toRemove.Count) 个需要清理的包:" -ForegroundColor Yellow
$toRemove | ForEach-Object { Write-Host "  - $_" -ForegroundColor Yellow }

# 确认清理
$confirmation = Read-Host "是否继续清理这些包？(y/N)"
if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
    Write-Host "清理操作已取消" -ForegroundColor Green
    exit 0
}

# 备份当前全局环境包列表
Write-Host "备份当前全局环境包列表..." -ForegroundColor Blue
python -m pip freeze > "global_packages_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

# 执行清理
Write-Host "开始清理全局依赖..." -ForegroundColor Blue
foreach ($package in $toRemove) {
    Write-Host "移除: $package" -ForegroundColor Yellow
    try {
        python -m pip uninstall $package -y
        Write-Host "✅ 成功移除: $package" -ForegroundColor Green
    } catch {
        Write-Host "❌ 移除失败: $package - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 验证清理结果
Write-Host "验证清理结果..." -ForegroundColor Blue
$remainingPackages = python -m pip list --format=freeze | ForEach-Object { $_.Split('==')[0] }
$stillPresent = @()
foreach ($package in $toRemove) {
    if ($remainingPackages -contains $package) {
        $stillPresent += $package
    }
}

if ($stillPresent.Count -eq 0) {
    Write-Host "✅ 所有HEIM项目相关依赖已成功清理" -ForegroundColor Green
} else {
    Write-Host "⚠️ 以下包仍然存在:" -ForegroundColor Yellow
    $stillPresent | ForEach-Object { Write-Host "  - $_" -ForegroundColor Yellow }
}

# 验证不会影响其他项目
Write-Host "验证Python基础功能..." -ForegroundColor Blue
try {
    python -c "import sys, os, json; print('✅ Python基础功能正常')"
    Write-Host "✅ Python基础功能验证通过" -ForegroundColor Green
} catch {
    Write-Host "❌ Python基础功能验证失败" -ForegroundColor Red
}

Write-Host "=== 清理完成 ===" -ForegroundColor Green
Write-Host "建议: 如果其他项目需要这些依赖，请在各自的虚拟环境中安装" -ForegroundColor Blue
