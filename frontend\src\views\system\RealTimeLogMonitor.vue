<template>
  <div class="realtime-log-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">实时日志监控</h1>
          <p class="page-description">实时查看系统操作日志和异常告警</p>
        </div>
        
        <!-- 实时状态指示器 -->
        <div class="status-indicators">
          <div class="status-item">
            <div class="status-dot" :class="{ active: isConnected }"></div>
            <span class="status-text">{{ isConnected ? '已连接' : '未连接' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">在线用户:</span>
            <span class="status-value">{{ realTimeStats?.online_users || 0 }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">每分钟操作:</span>
            <span class="status-value">{{ realTimeStats?.operations_per_minute || 0 }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">错误率:</span>
            <span class="status-value error-rate">{{ (realTimeStats?.error_rate || 0).toFixed(2) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-left">
        <n-button
          :type="isMonitoring ? 'error' : 'primary'"
          @click="toggleMonitoring"
        >
          <template #icon>
            <n-icon>
              <PlayIcon v-if="!isMonitoring" />
              <PauseIcon v-else />
            </n-icon>
          </template>
          {{ isMonitoring ? '暂停监控' : '开始监控' }}
        </n-button>
        
        <n-button @click="clearLogs">
          <template #icon>
            <n-icon><TrashIcon /></n-icon>
          </template>
          清空日志
        </n-button>
        
        <n-select
          v-model:value="filterLevel"
          placeholder="日志级别"
          style="width: 120px"
          @update:value="handleFilterChange"
        >
          <n-option value="" label="全部" />
          <n-option value="info" label="信息" />
          <n-option value="warning" label="警告" />
          <n-option value="error" label="错误" />
        </n-select>
        
        <n-input
          v-model:value="filterKeyword"
          placeholder="关键词过滤..."
          style="width: 200px"
          clearable
          @update:value="handleFilterChange"
        >
          <template #prefix>
            <n-icon><SearchIcon /></n-icon>
          </template>
        </n-input>
      </div>
      
      <div class="control-right">
        <n-switch
          v-model:value="autoScroll"
          size="small"
        >
          <template #checked>自动滚动</template>
          <template #unchecked>自动滚动</template>
        </n-switch>
        
        <n-select
          v-model:value="refreshInterval"
          style="width: 120px"
          @update:value="handleIntervalChange"
        >
          <n-option :value="1000" label="1秒" />
          <n-option :value="3000" label="3秒" />
          <n-option :value="5000" label="5秒" />
          <n-option :value="10000" label="10秒" />
        </n-select>
        
        <n-button @click="showSettingsDialog = true">
          <template #icon>
            <n-icon><SettingsIcon /></n-icon>
          </template>
          设置
        </n-button>
      </div>
    </div>

    <!-- 实时日志列表 -->
    <div class="log-container">
      <div
        ref="logListRef"
        class="log-list"
        :class="{ 'auto-scroll': autoScroll }"
      >
        <div
          v-for="log in filteredLogs"
          :key="log.id"
          class="log-item"
          :class="getLogItemClass(log)"
          @click="handleLogClick(log)"
        >
          <div class="log-time">{{ formatTime(log.created_at_formatted) }}</div>
          <div class="log-user">{{ log.user_nickname || '系统' }}</div>
          <div class="log-type">
            <n-tag :type="getOperationTypeColor(log.operation_type)" size="small">
              {{ log.operation_type_display }}
            </n-tag>
          </div>
          <div class="log-desc">{{ log.operation_desc }}</div>
          <div class="log-method">
            <n-tag :type="getMethodColor(log.method)" size="small">
              {{ log.method }}
            </n-tag>
          </div>
          <div class="log-ip">{{ log.ip_address }}</div>
          <div class="log-status">
            <n-tag :type="getStatusColor(log.status_code)" size="small">
              {{ log.status_code }}
            </n-tag>
          </div>
          <div class="log-response-time">
            <span :class="getResponseTimeClass(log.response_time)">
              {{ formatResponseTime(log.response_time) }}
            </span>
          </div>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="empty-logs">
          <n-empty description="暂无日志数据">
            <template #extra>
              <n-button v-if="!isMonitoring" type="primary" @click="toggleMonitoring">
                开始监控
              </n-button>
            </template>
          </n-empty>
        </div>
      </div>
    </div>

    <!-- 告警通知 -->
    <div v-if="alerts.length > 0" class="alert-panel">
      <div class="alert-header">
        <h3>实时告警</h3>
        <n-button size="small" text @click="clearAlerts">
          清空告警
        </n-button>
      </div>
      <div class="alert-list">
        <div
          v-for="alert in alerts"
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
        >
          <div class="alert-icon">
            <n-icon>
              <AlertTriangleIcon v-if="alert.level === 'error'" />
              <AlertCircleIcon v-else />
            </n-icon>
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-actions">
            <n-button size="small" text @click="dismissAlert(alert.id)">
              忽略
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <LogDetailDialog
      v-model:visible="detailDialogVisible"
      :log-id="selectedLogId"
    />

    <!-- 设置对话框 -->
    <MonitorSettingsDialog
      v-model:visible="showSettingsDialog"
      :settings="monitorSettings"
      @update:settings="handleSettingsUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { 
  PlayIcon, 
  PauseIcon, 
  TrashIcon, 
  SearchIcon, 
  SettingsIcon,
  AlertTriangleIcon,
  AlertCircleIcon
} from '@vicons/tabler'
import { useAuditStore } from '@/stores/audit'
import { auditApi } from '@/api/audit'
import type { OperationLogListItem, RealTimeStatsData } from '@/types/audit'
import LogDetailDialog from '@/components/audit/LogDetailDialog.vue'
import MonitorSettingsDialog from '@/components/audit/MonitorSettingsDialog.vue'

// 状态管理
const auditStore = useAuditStore()
const message = useMessage()

// 响应式数据
const isMonitoring = ref(false)
const isConnected = ref(false)
const autoScroll = ref(true)
const refreshInterval = ref(3000)
const filterLevel = ref('')
const filterKeyword = ref('')
const logListRef = ref<HTMLElement>()
const detailDialogVisible = ref(false)
const selectedLogId = ref<number | null>(null)
const showSettingsDialog = ref(false)

// 实时数据
const realTimeLogs = ref<OperationLogListItem[]>([])
const realTimeStats = ref<RealTimeStatsData | null>(null)
const alerts = ref<Array<{
  id: string
  level: 'info' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
}>>([])

// 监控设置
const monitorSettings = ref({
  maxLogs: 1000,
  alertThreshold: 10,
  errorRateThreshold: 5,
  responseTimeThreshold: 3000,
  enableSound: true,
  enableDesktopNotification: false
})

// WebSocket连接
let websocket: WebSocket | null = null
let refreshTimer: number | null = null

// 计算属性
const filteredLogs = computed(() => {
  let logs = realTimeLogs.value
  
  // 按级别过滤
  if (filterLevel.value) {
    logs = logs.filter(log => {
      switch (filterLevel.value) {
        case 'error':
          return log.status_code >= 400
        case 'warning':
          return log.status_code >= 300 && log.status_code < 400
        case 'info':
          return log.status_code < 300
        default:
          return true
      }
    })
  }
  
  // 按关键词过滤
  if (filterKeyword.value) {
    const keyword = filterKeyword.value.toLowerCase()
    logs = logs.filter(log => 
      log.operation_desc.toLowerCase().includes(keyword) ||
      log.user_nickname?.toLowerCase().includes(keyword) ||
      log.ip_address.includes(keyword)
    )
  }
  
  return logs
})

// 方法
const toggleMonitoring = () => {
  if (isMonitoring.value) {
    stopMonitoring()
  } else {
    startMonitoring()
  }
}

const startMonitoring = () => {
  isMonitoring.value = true
  connectWebSocket()
  startRefreshTimer()
  message.success('实时监控已开启')
}

const stopMonitoring = () => {
  isMonitoring.value = false
  disconnectWebSocket()
  stopRefreshTimer()
  message.info('实时监控已停止')
}

const connectWebSocket = () => {
  try {
    // 这里应该使用实际的WebSocket URL
    const wsUrl = `ws://${window.location.host}/ws/audit/logs/`
    websocket = new WebSocket(wsUrl)
    
    websocket.onopen = () => {
      isConnected.value = true
      console.log('WebSocket连接已建立')
    }
    
    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleWebSocketMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }
    
    websocket.onclose = () => {
      isConnected.value = false
      console.log('WebSocket连接已关闭')
      
      // 如果还在监控中，尝试重连
      if (isMonitoring.value) {
        setTimeout(connectWebSocket, 5000)
      }
    }
    
    websocket.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      isConnected.value = false
    }
  } catch (error) {
    console.error('创建WebSocket连接失败:', error)
    // 如果WebSocket不可用，使用轮询方式
    startPolling()
  }
}

const disconnectWebSocket = () => {
  if (websocket) {
    websocket.close()
    websocket = null
  }
  isConnected.value = false
}

const startPolling = () => {
  // 使用HTTP轮询作为WebSocket的备选方案
  const poll = async () => {
    if (!isMonitoring.value) return
    
    try {
      const response = await auditApi.getRealTimeLogs(50)
      if (response.code === 200) {
        realTimeLogs.value = response.data
        isConnected.value = true
      }
    } catch (error) {
      console.error('获取实时日志失败:', error)
      isConnected.value = false
    }
    
    if (isMonitoring.value) {
      setTimeout(poll, refreshInterval.value)
    }
  }
  
  poll()
}

const handleWebSocketMessage = (data: any) => {
  switch (data.type) {
    case 'log':
      addNewLog(data.data)
      break
    case 'stats':
      realTimeStats.value = data.data
      break
    case 'alert':
      addAlert(data.data)
      break
    default:
      console.log('未知的WebSocket消息类型:', data.type)
  }
}

const addNewLog = (log: OperationLogListItem) => {
  realTimeLogs.value.unshift(log)
  
  // 限制日志数量
  if (realTimeLogs.value.length > monitorSettings.value.maxLogs) {
    realTimeLogs.value = realTimeLogs.value.slice(0, monitorSettings.value.maxLogs)
  }
  
  // 检查是否需要告警
  checkLogForAlert(log)
  
  // 自动滚动到顶部
  if (autoScroll.value) {
    nextTick(() => {
      if (logListRef.value) {
        logListRef.value.scrollTop = 0
      }
    })
  }
}

const checkLogForAlert = (log: OperationLogListItem) => {
  // 检查错误状态码
  if (log.status_code >= 500) {
    addAlert({
      level: 'error',
      title: '服务器错误',
      message: `${log.operation_desc} (状态码: ${log.status_code})`,
      timestamp: new Date().toISOString()
    })
  }
  
  // 检查响应时间
  if (log.response_time > monitorSettings.value.responseTimeThreshold) {
    addAlert({
      level: 'warning',
      title: '响应时间过长',
      message: `${log.operation_desc} (响应时间: ${log.response_time}ms)`,
      timestamp: new Date().toISOString()
    })
  }
}

const addAlert = (alertData: any) => {
  const alert = {
    id: Date.now().toString(),
    level: alertData.level || 'info',
    title: alertData.title,
    message: alertData.message,
    timestamp: alertData.timestamp || new Date().toISOString()
  }
  
  alerts.value.unshift(alert)
  
  // 限制告警数量
  if (alerts.value.length > 50) {
    alerts.value = alerts.value.slice(0, 50)
  }
  
  // 播放提示音
  if (monitorSettings.value.enableSound) {
    playAlertSound(alert.level)
  }
  
  // 桌面通知
  if (monitorSettings.value.enableDesktopNotification) {
    showDesktopNotification(alert)
  }
}

const playAlertSound = (level: string) => {
  // 播放不同级别的提示音
  try {
    const audio = new Audio()
    switch (level) {
      case 'error':
        audio.src = '/sounds/error.mp3'
        break
      case 'warning':
        audio.src = '/sounds/warning.mp3'
        break
      default:
        audio.src = '/sounds/info.mp3'
    }
    audio.play().catch(() => {
      // 忽略播放失败
    })
  } catch (error) {
    // 忽略音频播放错误
  }
}

const showDesktopNotification = (alert: any) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(alert.title, {
      body: alert.message,
      icon: '/favicon.ico'
    })
  }
}

const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = window.setInterval(async () => {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      // 如果WebSocket不可用，使用HTTP请求获取数据
      try {
        const response = await auditApi.getRealTimeStats()
        if (response.code === 200) {
          realTimeStats.value = response.data
        }
      } catch (error) {
        console.error('获取实时统计失败:', error)
      }
    }
  }, refreshInterval.value)
}

const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const clearLogs = () => {
  realTimeLogs.value = []
  message.success('日志已清空')
}

const clearAlerts = () => {
  alerts.value = []
  message.success('告警已清空')
}

const dismissAlert = (alertId: string) => {
  alerts.value = alerts.value.filter(alert => alert.id !== alertId)
}

const handleFilterChange = () => {
  // 过滤条件变化时的处理
}

const handleIntervalChange = (interval: number) => {
  refreshInterval.value = interval
  if (isMonitoring.value) {
    startRefreshTimer()
  }
}

const handleLogClick = (log: OperationLogListItem) => {
  selectedLogId.value = log.id
  detailDialogVisible.value = true
}

const handleSettingsUpdate = (newSettings: any) => {
  monitorSettings.value = { ...newSettings }
  
  // 保存设置到本地存储
  localStorage.setItem('realtime_monitor_settings', JSON.stringify(monitorSettings.value))
}

const getLogItemClass = (log: OperationLogListItem) => {
  const classes = []
  
  if (log.status_code >= 500) {
    classes.push('log-error')
  } else if (log.status_code >= 400) {
    classes.push('log-warning')
  }
  
  if (log.response_time > monitorSettings.value.responseTimeThreshold) {
    classes.push('log-slow')
  }
  
  return classes
}

const getOperationTypeColor = (type: string) => {
  switch (type) {
    case 'LOGIN':
      return 'success'
    case 'LOGOUT':
      return 'info'
    case 'CREATE':
      return 'success'
    case 'UPDATE':
      return 'warning'
    case 'DELETE':
      return 'error'
    case 'QUERY':
      return 'info'
    case 'ERROR':
      return 'error'
    default:
      return 'default'
  }
}

const getMethodColor = (method: string) => {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'info'
    case 'POST':
      return 'success'
    case 'PUT':
      return 'warning'
    case 'PATCH':
      return 'warning'
    case 'DELETE':
      return 'error'
    default:
      return 'default'
  }
}

const getStatusColor = (code: number) => {
  if (code >= 200 && code < 300) {
    return 'success'
  } else if (code >= 400 && code < 500) {
    return 'warning'
  } else if (code >= 500) {
    return 'error'
  } else {
    return 'default'
  }
}

const getResponseTimeClass = (time: number) => {
  if (time < 100) {
    return 'response-time-fast'
  } else if (time < 500) {
    return 'response-time-normal'
  } else if (time < 1000) {
    return 'response-time-slow'
  } else {
    return 'response-time-very-slow'
  }
}

const formatTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleTimeString('zh-CN')
}

const formatResponseTime = (time: number) => {
  return auditApi.formatResponseTime(time)
}

// 生命周期
onMounted(() => {
  // 加载设置
  const savedSettings = localStorage.getItem('realtime_monitor_settings')
  if (savedSettings) {
    try {
      monitorSettings.value = { ...monitorSettings.value, ...JSON.parse(savedSettings) }
    } catch (error) {
      console.error('加载监控设置失败:', error)
    }
  }
  
  // 请求桌面通知权限
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }
})

onUnmounted(() => {
  stopMonitoring()
})
</script>

<style scoped>
.realtime-log-monitor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #dc2626;
  transition: background-color 0.3s;
}

.status-dot.active {
  background: #16a34a;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.status-text {
  color: #6b7280;
}

.status-label {
  color: #6b7280;
  font-size: 12px;
}

.status-value {
  color: #1f2937;
  font-weight: 500;
}

.error-rate {
  color: #dc2626;
}

.control-panel {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.control-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-container {
  flex: 1;
  padding: 16px 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.log-list {
  flex: 1;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow-y: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.log-item {
  display: grid;
  grid-template-columns: 80px 100px 80px 1fr 60px 120px 60px 80px;
  gap: 12px;
  padding: 8px 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
  align-items: center;
}

.log-item:hover {
  background: #f9fafb;
}

.log-item.log-error {
  background: #fef2f2;
  border-left: 3px solid #dc2626;
}

.log-item.log-warning {
  background: #fffbeb;
  border-left: 3px solid #d97706;
}

.log-item.log-slow {
  background: #f0f9ff;
  border-left: 3px solid #0284c7;
}

.log-time {
  color: #6b7280;
  font-size: 11px;
}

.log-user {
  color: #1f2937;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.log-type {
  display: flex;
  justify-content: center;
}

.log-desc {
  color: #1f2937;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.log-method {
  display: flex;
  justify-content: center;
}

.log-ip {
  color: #6b7280;
  font-size: 11px;
}

.log-status {
  display: flex;
  justify-content: center;
}

.log-response-time {
  text-align: right;
}

.response-time-fast {
  color: #16a34a;
}

.response-time-normal {
  color: #2563eb;
}

.response-time-slow {
  color: #d97706;
}

.response-time-very-slow {
  color: #dc2626;
  font-weight: 600;
}

.empty-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.alert-panel {
  position: fixed;
  top: 80px;
  right: 24px;
  width: 350px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 1000;
  overflow: hidden;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.alert-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.alert-list {
  max-height: 320px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-item.error {
  background: #fef2f2;
  border-left: 3px solid #dc2626;
}

.alert-item.warning {
  background: #fffbeb;
  border-left: 3px solid #d97706;
}

.alert-item.info {
  background: #f0f9ff;
  border-left: 3px solid #0284c7;
}

.alert-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-title {
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 11px;
  color: #9ca3af;
}

.alert-actions {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .status-indicators {
    flex-wrap: wrap;
    gap: 16px;
  }

  .control-panel {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .control-left,
  .control-right {
    justify-content: center;
    flex-wrap: wrap;
  }

  .log-item {
    grid-template-columns: 1fr;
    gap: 4px;
    padding: 12px 16px;
  }

  .log-item > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .log-item > div::before {
    content: attr(data-label);
    font-weight: 600;
    color: #6b7280;
    font-size: 11px;
  }

  .alert-panel {
    position: relative;
    top: auto;
    right: auto;
    width: 100%;
    margin: 16px 0;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
  }

  .control-panel {
    padding: 8px 16px;
  }

  .log-container {
    padding: 12px 16px;
  }

  .status-indicators {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 滚动条样式 */
.log-list::-webkit-scrollbar,
.alert-list::-webkit-scrollbar {
  width: 6px;
}

.log-list::-webkit-scrollbar-track,
.alert-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.log-list::-webkit-scrollbar-thumb,
.alert-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.log-list::-webkit-scrollbar-thumb:hover,
.alert-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
</script>
