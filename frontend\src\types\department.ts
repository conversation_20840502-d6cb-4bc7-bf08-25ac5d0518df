/**
 * 部门管理相关类型定义
 */

// 部门基础信息
export interface Department {
  id: number
  name: string
  code: string
  parent?: number | null
  parent_name?: string
  description: string
  sort_order: number
  is_active: boolean
  level: number
  lft: number
  rght: number
  tree_id: number
  children_count: number
  member_count: number
  manager_count: number
  created_at: string
  updated_at: string
}

// 部门树形结构
export interface DepartmentTree {
  id: number
  name: string
  code: string
  description: string
  sort_order: number
  is_active: boolean
  level: number
  member_count: number
  manager_info: ManagerInfo[]
  children: DepartmentTree[]
}

// 部门列表项
export interface DepartmentListItem {
  id: number
  name: string
  code: string
  parent?: number | null
  parent_name?: string
  is_active: boolean
  level: number
  member_count: number
  created_at: string
}

// 部门路径信息
export interface DepartmentPath {
  id: number
  name: string
  code: string
  level: number
  path: string
  ancestors: DepartmentAncestor[]
}

// 祖先部门信息
export interface DepartmentAncestor {
  id: number
  name: string
  code: string
  level: number
}

// 主管信息
export interface ManagerInfo {
  user_id: number
  user_name: string
  nickname: string
  manager_level: number
  weight: number
  position?: string
}

// 用户部门关联
export interface UserDepartment {
  id: number
  user: number
  department: number
  user_info: UserInfo
  department_info: DepartmentInfo
  is_primary: boolean
  is_manager: boolean
  position: string
  manager_level?: number | null
  weight: number
  effective_date: string
  expiry_date?: string | null
  is_effective_now: boolean
  created_at: string
  updated_at: string
}

// 用户信息（简化）
export interface UserInfo {
  id: number
  username: string
  nickname: string
  email?: string
  avatar?: string
  is_active: boolean
}

// 部门信息（简化）
export interface DepartmentInfo {
  id: number
  name: string
  code: string
  level: number
}

// 部门创建表单
export interface DepartmentCreateForm {
  name: string
  code: string
  parent?: number | null
  description: string
  sort_order: number
  is_active: boolean
}

// 部门编辑表单
export interface DepartmentEditForm {
  name: string
  code: string
  parent?: number | null
  description: string
  sort_order: number
  is_active: boolean
}

// 部门成员添加表单
export interface DepartmentMemberForm {
  user_id: number
  is_primary: boolean
  is_manager: boolean
  position: string
  manager_level?: number | null
  weight: number
  effective_date: string
  expiry_date?: string | null
}

// 部门搜索参数
export interface DepartmentSearchParams {
  search?: string
  is_active?: boolean
  parent_id?: number | null
  level?: number
  ordering?: string
  page?: number
  page_size?: number
}

// 部门成员搜索参数
export interface DepartmentMemberSearchParams {
  is_manager?: boolean
  is_primary?: boolean
}

// 部门统计信息
export interface DepartmentStats {
  total_departments: number
  active_departments: number
  inactive_departments: number
  total_members: number
  total_managers: number
  max_level: number
}

// 部门移动操作
export interface DepartmentMoveOperation {
  department_id: number
  target_parent_id?: number | null
  position: 'before' | 'after' | 'inside'
  target_id?: number
}

// 批量操作类型
export type DepartmentBatchOperationType = 'activate' | 'deactivate' | 'delete'

// 批量操作参数
export interface DepartmentBatchOperationParams {
  department_ids: number[]
  operation: DepartmentBatchOperationType
}

// 部门权限
export interface DepartmentPermissions {
  canView: boolean
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canManageMembers: boolean
  canSetManagers: boolean
  canMove: boolean
}

// 主管级别选项
export interface ManagerLevelOption {
  value: number
  label: string
}

// 部门树节点
export interface DepartmentTreeNode {
  id: number
  name: string
  code: string
  level: number
  is_active: boolean
  member_count: number
  manager_count: number
  children?: DepartmentTreeNode[]
  parent?: DepartmentTreeNode | null
  expanded?: boolean
  selected?: boolean
  loading?: boolean
}

// 拖拽操作信息
export interface DragDropInfo {
  dragNode: DepartmentTreeNode
  dropNode: DepartmentTreeNode
  dropPosition: 'before' | 'after' | 'inside'
}

// 部门详情
export interface DepartmentDetail extends Department {
  path: string
  ancestors: DepartmentAncestor[]
  children: Department[]
  members: UserDepartment[]
  managers: UserDepartment[]
  statistics: {
    direct_member_count: number
    total_member_count: number
    manager_count: number
    children_count: number
  }
}

// API响应类型
export interface DepartmentListResponse {
  results: DepartmentListItem[]
  count: number
  next?: string
  previous?: string
}

export interface DepartmentTreeResponse {
  data: DepartmentTree[]
}

export interface DepartmentMemberResponse {
  data: UserDepartment[]
}

// 表单验证规则
export interface DepartmentFormRule {
  required?: boolean
  message?: string
  pattern?: RegExp
  min?: number
  max?: number
  validator?: (value: any) => boolean | string
}

// 部门状态枚举
export enum DepartmentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

// 主管级别枚举
export enum ManagerLevel {
  LEVEL_1 = 1,
  LEVEL_2 = 2,
  LEVEL_3 = 3
}

// 部门操作类型
export enum DepartmentOperationType {
  CREATE = 'create',
  EDIT = 'edit',
  DELETE = 'delete',
  MOVE = 'move',
  ADD_MEMBER = 'add_member',
  REMOVE_MEMBER = 'remove_member',
  SET_MANAGER = 'set_manager'
}
