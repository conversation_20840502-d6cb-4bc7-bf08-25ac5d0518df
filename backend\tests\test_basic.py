"""
基础功能测试 - 验证测试框架是否正常工作
"""
import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from tests.factories import UserFactory, UserProfileFactory, DepartmentFactory

User = get_user_model()


@pytest.mark.django_db
class TestBasicFunctionality(TestCase):
    """基础功能测试"""
    
    def test_user_factory(self):
        """测试用户工厂"""
        user = UserFactory()
        
        self.assertIsNotNone(user)
        self.assertIsNotNone(user.username)
        self.assertIsNotNone(user.email)
        self.assertTrue(user.is_active)
    
    def test_user_profile_factory(self):
        """测试用户档案工厂"""
        profile = UserProfileFactory()

        self.assertIsNotNone(profile)
        self.assertIsNotNone(profile.username)
        self.assertIsNotNone(profile.nickname)
        self.assertTrue(profile.is_active)
    
    def test_department_factory(self):
        """测试部门工厂"""
        department = DepartmentFactory()
        
        self.assertIsNotNone(department)
        self.assertIsNotNone(department.name)
        self.assertIsNotNone(department.code)
        self.assertTrue(department.is_active)
    
    def test_user_creation(self):
        """测试用户创建"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('testpass123'))
    
    def test_user_authentication(self):
        """测试用户认证"""
        user = UserFactory()
        user.set_password('testpass123')
        user.save()
        
        # 测试正确密码
        self.assertTrue(user.check_password('testpass123'))
        
        # 测试错误密码
        self.assertFalse(user.check_password('wrongpass'))
    
    def test_database_connection(self):
        """测试数据库连接"""
        # 创建一个用户并保存
        user = UserFactory()
        user_id = user.id
        
        # 从数据库重新获取
        retrieved_user = User.objects.get(id=user_id)
        
        self.assertEqual(user.username, retrieved_user.username)
        self.assertEqual(user.email, retrieved_user.email)


@pytest.mark.unit
class TestFactoryBoy(TestCase):
    """Factory Boy测试"""
    
    def test_create_multiple_users(self):
        """测试批量创建用户"""
        users = UserFactory.create_batch(5)
        
        self.assertEqual(len(users), 5)
        
        # 验证用户名唯一性
        usernames = [user.username for user in users]
        self.assertEqual(len(usernames), len(set(usernames)))
    
    def test_user_with_profile(self):
        """测试创建用户档案"""
        profile = UserProfileFactory()

        # UserProfile就是用户模型本身
        self.assertIsNotNone(profile.username)
        self.assertIsNotNone(profile.nickname)
    
    def test_department_hierarchy(self):
        """测试部门层级"""
        parent = DepartmentFactory(name='父部门')
        child = DepartmentFactory(name='子部门', parent=parent)
        
        self.assertEqual(child.parent, parent)
        self.assertIn(child, parent.children.all())


@pytest.mark.integration
class TestModelIntegration(TestCase):
    """模型集成测试"""
    
    def test_user_profile_relationship(self):
        """测试用户档案功能"""
        profile = UserProfileFactory()

        # UserProfile就是用户模型，测试基本功能
        self.assertIsNotNone(profile.username)
        self.assertIsNotNone(profile.nickname)
        self.assertTrue(profile.is_active)
    
    def test_department_user_relationship(self):
        """测试部门和用户关系"""
        department = DepartmentFactory()
        user = UserProfileFactory()

        # 通过UserDepartment建立关系
        from apps.users.models import UserDepartment
        user_dept = UserDepartment.objects.create(
            user=user, department=department, is_primary=True, is_active=True
        )

        # 测试关系建立
        self.assertEqual(user_dept.user, user)
        self.assertEqual(user_dept.department, department)


class TestPytestMarkers(TestCase):
    """测试pytest标记功能"""
    
    @pytest.mark.unit
    def test_unit_marker(self):
        """单元测试标记"""
        self.assertTrue(True)
    
    @pytest.mark.integration
    def test_integration_marker(self):
        """集成测试标记"""
        self.assertTrue(True)
    
    @pytest.mark.slow
    def test_slow_marker(self):
        """慢速测试标记"""
        import time
        time.sleep(0.1)  # 模拟慢速操作
        self.assertTrue(True)
