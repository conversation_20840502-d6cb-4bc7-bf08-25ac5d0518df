"""
系统监控数据模型
"""
from django.db import models
from django.utils import timezone
from apps.common.models import BaseModel
import json


class SystemMetrics(BaseModel):
    """系统性能指标"""
    
    # CPU使用率
    cpu_usage = models.FloatField(verbose_name="CPU使用率(%)")
    cpu_cores = models.IntegerField(verbose_name="CPU核心数")
    
    # 内存使用情况
    memory_total = models.BigIntegerField(verbose_name="总内存(bytes)")
    memory_used = models.BigIntegerField(verbose_name="已用内存(bytes)")
    memory_usage = models.FloatField(verbose_name="内存使用率(%)")
    
    # 磁盘使用情况
    disk_total = models.BigIntegerField(verbose_name="总磁盘空间(bytes)")
    disk_used = models.BigIntegerField(verbose_name="已用磁盘空间(bytes)")
    disk_usage = models.FloatField(verbose_name="磁盘使用率(%)")
    
    # 网络统计
    network_bytes_sent = models.BigIntegerField(default=0, verbose_name="网络发送字节数")
    network_bytes_recv = models.BigIntegerField(default=0, verbose_name="网络接收字节数")
    
    # 系统负载
    load_average_1m = models.FloatField(null=True, blank=True, verbose_name="1分钟平均负载")
    load_average_5m = models.FloatField(null=True, blank=True, verbose_name="5分钟平均负载")
    load_average_15m = models.FloatField(null=True, blank=True, verbose_name="15分钟平均负载")
    
    # 进程信息
    process_count = models.IntegerField(default=0, verbose_name="进程数量")
    
    # 采集时间戳
    collected_at = models.DateTimeField(default=timezone.now, verbose_name="采集时间")
    
    class Meta:
        db_table = 'monitoring_system_metrics'
        verbose_name = "系统性能指标"
        verbose_name_plural = "系统性能指标"
        indexes = [
            models.Index(fields=['collected_at']),
            models.Index(fields=['cpu_usage']),
            models.Index(fields=['memory_usage']),
            models.Index(fields=['disk_usage']),
        ]
        ordering = ['-collected_at']
    
    def __str__(self):
        return f"系统指标 - {self.collected_at.strftime('%Y-%m-%d %H:%M:%S')}"


class ApplicationMetrics(BaseModel):
    """应用性能指标"""
    
    # 请求统计
    total_requests = models.BigIntegerField(default=0, verbose_name="总请求数")
    successful_requests = models.BigIntegerField(default=0, verbose_name="成功请求数")
    failed_requests = models.BigIntegerField(default=0, verbose_name="失败请求数")
    
    # 响应时间统计
    avg_response_time = models.FloatField(default=0, verbose_name="平均响应时间(ms)")
    min_response_time = models.FloatField(default=0, verbose_name="最小响应时间(ms)")
    max_response_time = models.FloatField(default=0, verbose_name="最大响应时间(ms)")
    p95_response_time = models.FloatField(default=0, verbose_name="95%响应时间(ms)")
    p99_response_time = models.FloatField(default=0, verbose_name="99%响应时间(ms)")
    
    # 错误率
    error_rate = models.FloatField(default=0, verbose_name="错误率(%)")
    
    # 活跃用户数
    active_users = models.IntegerField(default=0, verbose_name="活跃用户数")
    
    # 数据库连接数
    db_connections = models.IntegerField(default=0, verbose_name="数据库连接数")
    
    # 缓存命中率
    cache_hit_rate = models.FloatField(default=0, verbose_name="缓存命中率(%)")
    
    # 队列任务数
    queue_pending_tasks = models.IntegerField(default=0, verbose_name="队列待处理任务数")
    queue_failed_tasks = models.IntegerField(default=0, verbose_name="队列失败任务数")
    
    # 采集时间戳
    collected_at = models.DateTimeField(default=timezone.now, verbose_name="采集时间")
    
    class Meta:
        db_table = 'monitoring_application_metrics'
        verbose_name = "应用性能指标"
        verbose_name_plural = "应用性能指标"
        indexes = [
            models.Index(fields=['collected_at']),
            models.Index(fields=['error_rate']),
            models.Index(fields=['avg_response_time']),
        ]
        ordering = ['-collected_at']
    
    def __str__(self):
        return f"应用指标 - {self.collected_at.strftime('%Y-%m-%d %H:%M:%S')}"


class ServiceHealthCheck(BaseModel):
    """服务健康检查记录"""
    
    SERVICE_TYPES = [
        ('database', '数据库'),
        ('cache', '缓存'),
        ('queue', '消息队列'),
        ('storage', '存储'),
        ('external_api', '外部API'),
        ('email', '邮件服务'),
    ]
    
    STATUS_CHOICES = [
        ('healthy', '健康'),
        ('warning', '警告'),
        ('critical', '严重'),
        ('down', '宕机'),
    ]
    
    service_name = models.CharField(max_length=100, verbose_name="服务名称")
    service_type = models.CharField(max_length=20, choices=SERVICE_TYPES, verbose_name="服务类型")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name="健康状态")
    
    # 检查详情
    response_time = models.FloatField(null=True, blank=True, verbose_name="响应时间(ms)")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    details = models.JSONField(default=dict, verbose_name="检查详情")
    
    # 检查时间
    checked_at = models.DateTimeField(default=timezone.now, verbose_name="检查时间")
    
    class Meta:
        db_table = 'monitoring_service_health'
        verbose_name = "服务健康检查"
        verbose_name_plural = "服务健康检查"
        indexes = [
            models.Index(fields=['service_name', 'checked_at']),
            models.Index(fields=['service_type', 'status']),
            models.Index(fields=['status', 'checked_at']),
        ]
        ordering = ['-checked_at']
    
    def __str__(self):
        return f"{self.service_name} - {self.get_status_display()}"


class AlertRule(BaseModel):
    """监控告警规则"""
    
    METRIC_TYPES = [
        ('cpu_usage', 'CPU使用率'),
        ('memory_usage', '内存使用率'),
        ('disk_usage', '磁盘使用率'),
        ('error_rate', '错误率'),
        ('response_time', '响应时间'),
        ('active_users', '活跃用户数'),
        ('queue_tasks', '队列任务数'),
    ]
    
    OPERATORS = [
        ('gt', '大于'),
        ('gte', '大于等于'),
        ('lt', '小于'),
        ('lte', '小于等于'),
        ('eq', '等于'),
        ('ne', '不等于'),
    ]
    
    SEVERITY_LEVELS = [
        ('info', '信息'),
        ('warning', '警告'),
        ('error', '错误'),
        ('critical', '严重'),
    ]
    
    name = models.CharField(max_length=100, verbose_name="规则名称")
    description = models.TextField(blank=True, verbose_name="规则描述")
    
    # 监控指标
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES, verbose_name="监控指标")
    operator = models.CharField(max_length=10, choices=OPERATORS, verbose_name="比较操作符")
    threshold = models.FloatField(verbose_name="阈值")
    
    # 告警配置
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, verbose_name="严重程度")
    duration = models.IntegerField(default=300, verbose_name="持续时间(秒)")  # 持续多长时间才触发告警
    
    # 通知配置
    enable_email = models.BooleanField(default=True, verbose_name="启用邮件通知")
    enable_webhook = models.BooleanField(default=False, verbose_name="启用Webhook通知")
    webhook_url = models.URLField(blank=True, verbose_name="Webhook地址")
    
    # 规则状态
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    
    class Meta:
        db_table = 'monitoring_alert_rule'
        verbose_name = "告警规则"
        verbose_name_plural = "告警规则"
        indexes = [
            models.Index(fields=['metric_type', 'is_active']),
            models.Index(fields=['severity']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.get_metric_type_display()}"


class AlertRecord(BaseModel):
    """告警记录"""
    
    STATUS_CHOICES = [
        ('triggered', '已触发'),
        ('acknowledged', '已确认'),
        ('resolved', '已解决'),
        ('suppressed', '已抑制'),
    ]
    
    rule = models.ForeignKey(AlertRule, on_delete=models.CASCADE, verbose_name="告警规则")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='triggered', verbose_name="告警状态")
    
    # 告警详情
    metric_value = models.FloatField(verbose_name="指标值")
    message = models.TextField(verbose_name="告警消息")
    details = models.JSONField(default=dict, verbose_name="告警详情")
    
    # 时间信息
    triggered_at = models.DateTimeField(default=timezone.now, verbose_name="触发时间")
    acknowledged_at = models.DateTimeField(null=True, blank=True, verbose_name="确认时间")
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name="解决时间")
    
    # 处理人员
    acknowledged_by = models.ForeignKey(
        'users.UserProfile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='monitoring_acknowledged_alerts',
        verbose_name="确认人"
    )
    resolved_by = models.ForeignKey(
        'users.UserProfile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='monitoring_resolved_alerts',
        verbose_name="解决人"
    )
    
    # 处理备注
    resolution_notes = models.TextField(blank=True, verbose_name="解决备注")
    
    class Meta:
        db_table = 'monitoring_alert_record'
        verbose_name = "告警记录"
        verbose_name_plural = "告警记录"
        indexes = [
            models.Index(fields=['rule', 'triggered_at']),
            models.Index(fields=['status', 'triggered_at']),
            models.Index(fields=['triggered_at']),
        ]
        ordering = ['-triggered_at']
    
    def __str__(self):
        return f"{self.rule.name} - {self.get_status_display()}"
