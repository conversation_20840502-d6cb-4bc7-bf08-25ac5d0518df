{"tests/test_authentication.py::TestCase": true, "tests/test_authentication.py::APITestCase": true, "tests/test_authentication.py::TestAuthenticationAPI": true, "tests/test_authentication.py::TestAuthenticationService": true, "tests/test_authentication.py::TestUserSessionModel": true, "tests/test_authentication.py::TestLoginAttemptModel": true, "tests/test_department_management.py": true, "tests/test_user_management.py": true, "tests/test_audit_logging.py::TestAuditLogAPI::test_filter_logs_by_user": true, "tests/test_audit_logging.py::TestAuditLogAPI::test_get_operation_log_detail": true, "tests/test_audit_logging.py::TestAuditLogAPI::test_logs_pagination": true, "tests/test_audit_logging.py::TestAuditService::test_cleanup_old_logs": true, "tests/test_audit_logging.py::TestAuditService::test_export_logs_to_csv": true, "tests/test_role_permission_management.py::TestRoleManagementAPI::test_create_role_success": true, "tests/test_role_permission_management.py::TestRoleManagementAPI::test_delete_role_soft_delete": true, "tests/test_role_permission_management.py::TestRoleManagementAPI::test_insufficient_permission": true, "tests/test_role_permission_management.py::TestRoleManagementAPI::test_role_permission_revocation": true, "tests/test_role_permission_management.py::TestRoleManagementAPI::test_role_user_assignment": true, "tests/test_role_permission_management.py::TestPermissionManagementAPI::test_create_permission_hierarchy": true, "tests/test_role_permission_management.py::TestPermissionManagementAPI::test_create_permission_success": true, "tests/test_role_permission_management.py::TestPermissionManagementAPI::test_get_permission_tree": true, "tests/test_role_permission_management.py::TestPermissionDecorator::test_permission_decorator_failure": true, "tests/test_role_permission_management.py::TestPermissionDecorator::test_permission_decorator_success": true, "tests/test_role_permission_management.py::TestPermissionDecorator::test_superuser_bypass": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_all_data_scope_access": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_cross_department_access_denied": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_data_scope_boundary_conditions": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_data_scope_permission_cache_invalidation": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_dept_and_sub_scope_access": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_dept_only_scope_access": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_multiple_roles_data_scope": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_operation_log_data_scope_filtering": true, "tests/test_data_scope_permissions.py::TestDataScopePermissions::test_self_only_scope_access": true, "tests/test_data_scope_permissions.py::TestDataScopeIntegrationScenarios::test_cross_department_collaboration_scenario": true, "tests/test_data_scope_permissions.py::TestDataScopeIntegrationScenarios::test_hierarchical_data_access_scenario": true, "tests/test_data_scope_permissions.py::TestDataScopeIntegrationScenarios::test_temporary_permission_elevation_scenario": true}