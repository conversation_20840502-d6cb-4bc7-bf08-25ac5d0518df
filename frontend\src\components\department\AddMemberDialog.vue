<template>
  <n-modal
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="添加成员"
    class="add-member-dialog"
    style="width: 600px"
    @update:show="$emit('update:visible', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="选择用户" path="user_id">
        <n-select
          v-model:value="formData.user_id"
          :options="userOptions"
          placeholder="请选择用户"
          filterable
          remote
          :loading="userLoading"
          @search="handleUserSearch"
          clearable
        />
      </n-form-item>
      
      <n-form-item label="部门类型" path="is_primary">
        <n-radio-group v-model:value="formData.is_primary">
          <n-radio :value="true">主部门</n-radio>
          <n-radio :value="false">兼职部门</n-radio>
        </n-radio-group>
      </n-form-item>
      
      <n-form-item label="职位" path="position">
        <n-input
          v-model:value="formData.position"
          placeholder="请输入职位"
        />
      </n-form-item>
      
      <n-form-item label="是否主管" path="is_manager">
        <n-switch
          v-model:value="formData.is_manager"
          @update:value="handleManagerChange"
        >
          <template #checked>是</template>
          <template #unchecked>否</template>
        </n-switch>
      </n-form-item>
      
      <n-form-item
        v-if="formData.is_manager"
        label="主管级别"
        path="manager_level"
      >
        <n-select
          v-model:value="formData.manager_level"
          :options="managerLevelOptions"
          placeholder="请选择主管级别"
        />
      </n-form-item>
      
      <n-form-item
        v-if="formData.is_manager"
        label="管理权重"
        path="weight"
      >
        <n-input-number
          v-model:value="formData.weight"
          placeholder="请输入管理权重"
          :min="1"
          :max="100"
          class="w-full"
        />
      </n-form-item>
      
      <n-form-item label="生效时间" path="effective_date">
        <n-date-picker
          v-model:value="formData.effective_date"
          type="date"
          placeholder="请选择生效时间"
          class="w-full"
        />
      </n-form-item>
      
      <n-form-item
        v-if="!formData.is_primary"
        label="到期时间"
        path="expiry_date"
      >
        <n-date-picker
          v-model:value="formData.expiry_date"
          type="date"
          placeholder="请选择到期时间（可选）"
          class="w-full"
          clearable
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="dialog-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          添加
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules, type SelectOption } from 'naive-ui'
import { departmentApi } from '@/api/department'
import type { DepartmentMemberForm } from '@/types/department'

// 临时用户API接口
const userApi = {
  async getUserList(params: any) {
    // 这里应该调用实际的用户API
    return {
      code: 200,
      data: {
        results: []
      }
    }
  }
}

// Props
interface Props {
  visible: boolean
  departmentId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const formRef = ref<FormInst>()
const submitting = ref(false)
const userLoading = ref(false)
const userOptions = ref<SelectOption[]>([])

// 表单数据
const formData = ref<DepartmentMemberForm & { effective_date: number | null; expiry_date: number | null }>({
  user_id: 0,
  is_primary: true,
  is_manager: false,
  position: '',
  manager_level: undefined,
  weight: 1,
  effective_date: Date.now(),
  expiry_date: null
})

// 主管级别选项
const managerLevelOptions: SelectOption[] = [
  { label: '一级主管', value: 1 },
  { label: '二级主管', value: 2 },
  { label: '三级主管', value: 3 }
]

// 表单验证规则
const formRules: FormRules = {
  user_id: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  position: [
    { max: 50, message: '职位名称不能超过50个字符', trigger: 'blur' }
  ],
  manager_level: [
    {
      validator: (rule, value) => {
        if (formData.value.is_manager && !value) {
          return false
        }
        return true
      },
      message: '请选择主管级别',
      trigger: 'change'
    }
  ],
  weight: [
    {
      validator: (rule, value) => {
        if (formData.value.is_manager && (!value || value < 1 || value > 100)) {
          return false
        }
        return true
      },
      message: '管理权重范围为1-100',
      trigger: 'blur'
    }
  ],
  effective_date: [
    { required: true, message: '请选择生效时间', trigger: 'change' }
  ],
  expiry_date: [
    {
      validator: (rule, value) => {
        if (value && formData.value.effective_date && value <= formData.value.effective_date) {
          return false
        }
        return true
      },
      message: '到期时间必须晚于生效时间',
      trigger: 'change'
    }
  ]
}

// 方法
const resetForm = () => {
  formData.value = {
    user_id: 0,
    is_primary: true,
    is_manager: false,
    position: '',
    manager_level: undefined,
    weight: 1,
    effective_date: Date.now(),
    expiry_date: null
  }
  userOptions.value = []
}

const handleUserSearch = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  
  try {
    userLoading.value = true
    const response = await userApi.getUserList({ search: query, page_size: 20 })
    
    if (response.code === 200) {
      userOptions.value = response.data.results.map(user => ({
        label: `${user.nickname || user.username} (${user.email || user.username})`,
        value: user.id
      }))
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

const handleManagerChange = (isManager: boolean) => {
  if (!isManager) {
    formData.value.manager_level = undefined
    formData.value.weight = 1
  } else {
    formData.value.manager_level = 1
    formData.value.weight = 10
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    const submitData: DepartmentMemberForm = {
      user_id: formData.value.user_id,
      is_primary: formData.value.is_primary,
      is_manager: formData.value.is_manager,
      position: formData.value.position,
      manager_level: formData.value.manager_level,
      weight: formData.value.weight,
      effective_date: new Date(formData.value.effective_date!).toISOString().split('T')[0],
      expiry_date: formData.value.expiry_date ? new Date(formData.value.expiry_date).toISOString().split('T')[0] : undefined
    }
    
    await departmentApi.addDepartmentMember(props.departmentId, submitData)
    emit('success')
  } catch (error) {
    console.error('添加成员失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      resetForm()
    })
  }
})
</script>

<style scoped>
.add-member-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.w-full {
  width: 100%;
}
</style>
