# -*- coding: utf-8 -*-
"""
公共模块URL配置
"""
from django.urls import path
from .test_views import TestExceptionView, test_response_helper
from .example_views import ExceptionDemoView, create_user_demo, paginated_demo
from . import file_views
from . import config_views

app_name = 'common'

urlpatterns = [
    # 测试异常处理
    path('test/exception/', TestExceptionView.as_view(), name='test_exception'),
    # 测试响应助手
    path('test/response/', test_response_helper, name='test_response'),
    
    # 异常处理演示
    path('demo/exception/', ExceptionDemoView.as_view(), name='exception_demo'),
    path('demo/create-user/', create_user_demo, name='create_user_demo'),
    path('demo/paginated/', paginated_demo, name='paginated_demo'),

    # 文件上传接口
    path('upload/', file_views.upload_file, name='upload_file'),
    path('upload/validate/', file_views.validate_file, name='validate_file'),
    path('upload/config/', file_views.get_upload_config, name='upload_config'),
    path('upload/batch/', file_views.batch_upload, name='batch_upload'),
    path('files/', file_views.list_uploaded_files, name='list_files'),
    path('files/delete/', file_views.delete_file, name='delete_file'),

    # 配置管理接口
    path('config/categories/', config_views.get_config_categories, name='config_categories'),
    path('config/', config_views.get_configs, name='get_configs'),
    path('config/<str:key>/', config_views.get_config, name='get_config'),
    path('config/<str:key>/update/', config_views.update_config, name='update_config'),
    path('config/<str:key>/reset/', config_views.reset_config, name='reset_config'),
    path('config/batch/update/', config_views.batch_update_configs, name='batch_update_configs'),
    path('config/validate/', config_views.validate_configs, name='validate_configs'),
    path('config/export/', config_views.export_configs, name='export_configs'),
    path('config/import/', config_views.import_configs, name='import_configs'),
]