"""
测试数据工厂类
使用Factory Boy创建测试数据
"""
import factory
from factory.django import DjangoModelFactory
from factory import Faker, SubFactory, LazyAttribute, LazyFunction
from django.contrib.auth import get_user_model
from django.utils import timezone
from faker import Faker as FakerInstance

from apps.users.models import UserProfile, UserDepartment
from apps.departments.models import Department
from apps.permissions.models import Role, Permission
from apps.authentication.models import UserSession, LoginAttempt
from apps.audit.models import OperationLog

# 创建中文Faker实例
fake = FakerInstance('zh_CN')

User = get_user_model()


class UserFactory(DjangoModelFactory):
    """用户工厂类"""
    
    class Meta:
        model = User
        django_get_or_create = ('username',)
    
    username = factory.Sequence(lambda n: f'testuser{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = Faker('first_name', locale='zh_CN')
    last_name = Faker('last_name', locale='zh_CN')
    is_active = True
    is_staff = False
    is_superuser = False
    date_joined = LazyFunction(timezone.now)


class SuperUserFactory(UserFactory):
    """超级用户工厂类"""
    
    username = factory.Sequence(lambda n: f'admin{n}')
    is_staff = True
    is_superuser = True


class DepartmentFactory(DjangoModelFactory):
    """部门工厂类"""

    class Meta:
        model = Department
        django_get_or_create = ('name',)

    name = factory.Sequence(lambda n: f'测试部门{n}')
    code = factory.Sequence(lambda n: f'DEPT{n:03d}')
    description = Faker('text', max_nb_chars=200, locale='zh_CN')
    sort_order = factory.Sequence(lambda n: n)
    is_active = True

    # 可选的父部门
    parent = None


# UserDepartmentFactory将在UserProfileFactory之后定义


class RoleFactory(DjangoModelFactory):
    """角色工厂类"""
    
    class Meta:
        model = Role
        django_get_or_create = ('name',)
    
    name = factory.Sequence(lambda n: f'测试角色{n}')
    code = factory.Sequence(lambda n: f'ROLE{n:03d}')
    data_scope = factory.Iterator(['ALL', 'DEPT_AND_SUB', 'DEPT_ONLY', 'SELF_ONLY'])
    description = Faker('text', max_nb_chars=200, locale='zh_CN')
    is_active = True
    sort_order = factory.Sequence(lambda n: n)


class PermissionFactory(DjangoModelFactory):
    """权限工厂类"""

    class Meta:
        model = Permission
        django_get_or_create = ('code',)

    name = factory.Sequence(lambda n: f'测试权限{n}')
    code = factory.Sequence(lambda n: f'test:permission{n}')
    permission_type = factory.Iterator(['MENU', 'BUTTON', 'API'])
    path = factory.Sequence(lambda n: f'/api/test{n}/')
    http_method = factory.Iterator(['GET', 'POST', 'PUT', 'DELETE'])
    sort_order = factory.Sequence(lambda n: n)
    is_active = True


# DataScope模型暂时不存在，注释掉相关工厂
# class DataScopeFactory(DjangoModelFactory):
#     """数据范围权限工厂类"""
#     pass


class UserProfileFactory(DjangoModelFactory):
    """用户档案工厂类 - UserProfile是扩展的用户模型"""

    class Meta:
        model = UserProfile
        django_get_or_create = ('username',)

    username = factory.Sequence(lambda n: f'testuser{n}')
    nickname = factory.Sequence(lambda n: f'测试用户{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = Faker('first_name', locale='zh_CN')
    last_name = Faker('last_name', locale='zh_CN')
    phone = factory.Sequence(lambda n: f'1380000{n:04d}')
    avatar = ''
    is_active = True
    is_staff = False
    is_superuser = False
    date_joined = LazyFunction(timezone.now)


class UserDepartmentFactory(DjangoModelFactory):
    """用户部门关联工厂类"""

    class Meta:
        model = UserDepartment
        django_get_or_create = ('user', 'department')

    user = SubFactory(UserProfileFactory)
    department = SubFactory(DepartmentFactory)
    is_primary = True
    is_manager = False
    position = factory.Sequence(lambda n: f'职位{n}')
    is_active = True


class UserSessionFactory(DjangoModelFactory):
    """用户会话工厂类"""
    
    class Meta:
        model = UserSession
    
    user = SubFactory(UserFactory)
    session_key = factory.Faker('uuid4')
    ip_address = Faker('ipv4')
    user_agent = Faker('user_agent')
    device_type = factory.Iterator(['desktop', 'mobile', 'tablet'])
    browser = factory.Iterator(['Chrome', 'Firefox', 'Safari', 'Edge'])
    os = factory.Iterator(['Windows', 'macOS', 'Linux', 'iOS', 'Android'])
    location = Faker('city', locale='zh_CN')
    is_active = True
    login_time = LazyFunction(timezone.now)
    last_activity = LazyFunction(timezone.now)


class LoginAttemptFactory(DjangoModelFactory):
    """登录尝试工厂类"""
    
    class Meta:
        model = LoginAttempt
    
    username = factory.Sequence(lambda n: f'testuser{n}')
    ip_address = Faker('ipv4')
    user_agent = Faker('user_agent')
    success = True
    failure_reason = ''
    attempted_at = LazyFunction(timezone.now)


class OperationLogFactory(DjangoModelFactory):
    """操作日志工厂类"""

    class Meta:
        model = OperationLog

    user = SubFactory(UserProfileFactory)
    operation_type = factory.Iterator(['LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'QUERY', 'ERROR'])
    operation_desc = factory.Sequence(lambda n: f'测试操作{n}')
    method = factory.Iterator(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
    path = factory.Sequence(lambda n: f'/api/test/{n}/')
    ip_address = Faker('ipv4')
    user_agent = Faker('user_agent')
    status_code = factory.Iterator([200, 201, 204, 400, 401, 403, 404, 500])
    response_time = factory.Faker('pyint', min_value=10, max_value=5000)


# 便捷的工厂方法
def create_user_with_profile(**kwargs):
    """创建用户档案（UserProfile就是用户模型）"""
    profile = UserProfileFactory(**kwargs)
    return profile, profile  # 返回相同的对象，保持接口兼容


def create_department_hierarchy(levels=3):
    """创建部门层级结构"""
    departments = []
    parent = None
    
    for i in range(levels):
        dept = DepartmentFactory(
            name=f'部门层级{i+1}',
            parent=parent
        )
        departments.append(dept)
        parent = dept
    
    return departments


def create_role_with_permissions(permission_count=3):
    """创建带权限的角色"""
    role = RoleFactory()
    permissions = PermissionFactory.create_batch(permission_count)
    role.permissions.set(permissions)
    return role, permissions


def create_user_role_assignment(user=None, role=None, department=None):
    """创建用户角色分配"""
    if user is None:
        user = UserProfileFactory()
    if role is None:
        role = RoleFactory()
    if department is None:
        department = DepartmentFactory()

    # 创建用户部门关联
    user_dept = UserDepartmentFactory(user=user, department=department)

    # 创建用户角色关联
    from apps.permissions.models import UserRole
    user_role = UserRole.objects.create(
        user=user,
        role=role,
        department=department
    )
    return user_role, user_dept


def create_permission_hierarchy(levels=3):
    """创建权限层级结构"""
    permissions = []
    parent = None

    for i in range(levels):
        permission = PermissionFactory(
            name=f'权限层级{i+1}',
            code=f'level{i+1}:permission',
            parent=parent
        )
        permissions.append(permission)
        parent = permission

    return permissions


def create_user_with_roles_and_permissions():
    """创建完整的用户权限体系"""
    # 创建用户档案
    user, profile = create_user_with_profile()

    # 创建部门层级
    departments = create_department_hierarchy()
    # 注意：需要检查UserProfile是否有department字段
    # profile.department = departments[-1]  # 分配到最底层部门
    # profile.save()

    # 创建角色和权限
    role, permissions = create_role_with_permissions()
    # 注意：需要检查UserProfile是否有roles字段
    # profile.roles.add(role)

    # 创建数据范围权限（暂时注释）
    data_scope = None

    return {
        'user': user,
        'profile': profile,
        'departments': departments,
        'role': role,
        'permissions': permissions,
        'data_scope': data_scope
    }


def create_test_session(user=None):
    """创建测试会话"""
    if user is None:
        user = UserFactory()
    
    session = UserSessionFactory(user=user)
    return session


def create_login_attempts(user=None, success_count=3, failure_count=2):
    """创建登录尝试记录"""
    if user is None:
        user = UserFactory()
    
    attempts = []
    
    # 创建成功的登录尝试
    for _ in range(success_count):
        attempt = LoginAttemptFactory(
            username=user.username,
            success=True
        )
        attempts.append(attempt)
    
    # 创建失败的登录尝试
    for _ in range(failure_count):
        attempt = LoginAttemptFactory(
            username=user.username,
            success=False,
            failure_reason='密码错误'
        )
        attempts.append(attempt)
    
    return attempts


def create_operation_logs(user=None, count=10):
    """创建操作日志"""
    if user is None:
        user = UserFactory()
    
    logs = []
    for _ in range(count):
        log = OperationLogFactory(user=user)
        logs.append(log)
    
    return logs
