"""
文件上传相关视图
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings
import os
import logging

from .file_security import SecureFileUploadHand<PERSON>, FileSecurityValidator
from .response import ApiResponse
from .exceptions import BusinessException, ErrorCode

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_file(request):
    """
    安全文件上传接口
    
    支持的文件类型:
    - image: 图片文件 (jpg, png, gif等)
    - document: 文档文件 (pdf, doc, xls等)
    - archive: 压缩文件 (zip, rar等)
    """
    try:
        # 检查是否有文件上传
        if 'file' not in request.FILES:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请选择要上传的文件")
        
        uploaded_file = request.FILES['file']
        file_type = request.data.get('file_type', 'image')
        
        # 验证文件类型参数
        allowed_types = ['image', 'document', 'archive']
        if file_type not in allowed_types:
            raise BusinessException(
                ErrorCode.VALIDATION_ERROR, 
                f"不支持的文件类型参数: {file_type}，支持的类型: {', '.join(allowed_types)}"
            )
        
        # 处理文件上传
        handler = SecureFileUploadHandler()
        result = handler.handle_upload(uploaded_file, file_type)
        
        if not result['is_valid']:
            raise BusinessException(
                ErrorCode.VALIDATION_ERROR,
                f"文件验证失败: {'; '.join(result['errors'])}"
            )
        
        # 记录上传日志
        logger.info(f"用户 {request.user.username} 上传文件: {result['file_info']['name']}")
        
        # 构造返回数据
        response_data = {
            'file_info': {
                'original_name': result['file_info']['name'],
                'safe_filename': result['file_info']['safe_filename'],
                'file_path': result['file_info']['saved_path'],
                'file_size': result['file_info']['size'],
                'file_type': file_type,
                'mime_type': result['file_info']['mime_type'],
                'file_hash': result['file_info']['hash'],
                'file_url': f"{settings.MEDIA_URL}{result['file_info']['saved_path']}"
            },
            'security_info': result['security_info']
        }
        
        return ApiResponse.success(data=response_data, message="文件上传成功")
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"文件上传异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "文件上传失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_file(request):
    """
    文件验证接口（不保存文件）
    用于在上传前验证文件是否符合安全要求
    """
    try:
        if 'file' not in request.FILES:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请选择要验证的文件")
        
        uploaded_file = request.FILES['file']
        file_type = request.data.get('file_type', 'image')
        
        # 验证文件
        validator = FileSecurityValidator(file_type)
        result = validator.validate_file(uploaded_file)
        
        # 构造返回数据
        response_data = {
            'is_valid': result['is_valid'],
            'file_info': result['file_info'],
            'security_info': result['security_info'],
            'errors': result['errors']
        }
        
        if result['is_valid']:
            return ApiResponse.success(data=response_data, message="文件验证通过")
        else:
            return ApiResponse.error(
                code=ErrorCode.VALIDATION_ERROR,
                message="文件验证失败",
                data=response_data
            )
            
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"文件验证异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "文件验证失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_upload_config(request):
    """
    获取文件上传配置信息
    """
    try:
        config = {
            'allowed_types': {
                'image': {
                    'extensions': FileSecurityValidator.ALLOWED_FILE_TYPES['image']['extensions'],
                    'max_size': FileSecurityValidator.ALLOWED_FILE_TYPES['image']['max_size'],
                    'max_size_mb': FileSecurityValidator.ALLOWED_FILE_TYPES['image']['max_size'] / (1024 * 1024)
                },
                'document': {
                    'extensions': FileSecurityValidator.ALLOWED_FILE_TYPES['document']['extensions'],
                    'max_size': FileSecurityValidator.ALLOWED_FILE_TYPES['document']['max_size'],
                    'max_size_mb': FileSecurityValidator.ALLOWED_FILE_TYPES['document']['max_size'] / (1024 * 1024)
                },
                'archive': {
                    'extensions': FileSecurityValidator.ALLOWED_FILE_TYPES['archive']['extensions'],
                    'max_size': FileSecurityValidator.ALLOWED_FILE_TYPES['archive']['max_size'],
                    'max_size_mb': FileSecurityValidator.ALLOWED_FILE_TYPES['archive']['max_size'] / (1024 * 1024)
                }
            },
            'dangerous_extensions': FileSecurityValidator.DANGEROUS_EXTENSIONS,
            'security_features': [
                '文件类型白名单验证',
                '文件大小限制',
                '文件头魔数验证',
                'MIME类型检测',
                '恶意内容扫描',
                '安全文件名生成'
            ]
        }
        
        return ApiResponse.success(data=config, message="获取上传配置成功")
        
    except Exception as e:
        logger.error(f"获取上传配置异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取上传配置失败")

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_file(request):
    """
    删除已上传的文件
    """
    try:
        file_path = request.data.get('file_path')
        if not file_path:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请提供要删除的文件路径")
        
        # 构造完整文件路径
        full_path = os.path.join(settings.MEDIA_ROOT, file_path)
        
        # 安全检查：确保文件路径在MEDIA_ROOT内
        if not os.path.abspath(full_path).startswith(os.path.abspath(settings.MEDIA_ROOT)):
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "非法的文件路径")
        
        # 检查文件是否存在
        if not os.path.exists(full_path):
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "文件不存在")
        
        # 删除文件
        os.remove(full_path)
        
        # 记录删除日志
        logger.info(f"用户 {request.user.username} 删除文件: {file_path}")
        
        return ApiResponse.success(message="文件删除成功")
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"文件删除异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "文件删除失败")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_uploaded_files(request):
    """
    列出用户上传的文件
    """
    try:
        # 这里可以扩展为从数据库查询用户上传的文件记录
        # 目前简单返回上传目录的文件列表
        
        upload_path = os.path.join(settings.MEDIA_ROOT, 'uploads')
        files = []
        
        if os.path.exists(upload_path):
            for root, dirs, filenames in os.walk(upload_path):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(file_path, settings.MEDIA_ROOT)
                    
                    # 获取文件信息
                    stat = os.stat(file_path)
                    files.append({
                        'filename': filename,
                        'file_path': relative_path.replace('\\', '/'),
                        'file_size': stat.st_size,
                        'created_time': stat.st_ctime,
                        'file_url': f"{settings.MEDIA_URL}{relative_path.replace(os.sep, '/')}"
                    })
        
        # 按创建时间倒序排列
        files.sort(key=lambda x: x['created_time'], reverse=True)
        
        return ApiResponse.success(data={'files': files}, message="获取文件列表成功")
        
    except Exception as e:
        logger.error(f"获取文件列表异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取文件列表失败")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def batch_upload(request):
    """
    批量文件上传接口
    """
    try:
        files = request.FILES.getlist('files')
        if not files:
            raise BusinessException(ErrorCode.VALIDATION_ERROR, "请选择要上传的文件")
        
        file_type = request.data.get('file_type', 'image')
        max_files = int(request.data.get('max_files', 10))
        
        # 检查文件数量限制
        if len(files) > max_files:
            raise BusinessException(
                ErrorCode.VALIDATION_ERROR, 
                f"一次最多只能上传 {max_files} 个文件"
            )
        
        handler = SecureFileUploadHandler()
        results = []
        success_count = 0
        
        for uploaded_file in files:
            try:
                result = handler.handle_upload(uploaded_file, file_type)
                
                if result['is_valid']:
                    success_count += 1
                    results.append({
                        'filename': uploaded_file.name,
                        'status': 'success',
                        'file_info': result['file_info'],
                        'security_info': result['security_info']
                    })
                else:
                    results.append({
                        'filename': uploaded_file.name,
                        'status': 'failed',
                        'errors': result['errors']
                    })
                    
            except Exception as e:
                results.append({
                    'filename': uploaded_file.name,
                    'status': 'failed',
                    'errors': [str(e)]
                })
        
        # 记录批量上传日志
        logger.info(f"用户 {request.user.username} 批量上传文件: 成功 {success_count}/{len(files)}")
        
        response_data = {
            'total_files': len(files),
            'success_count': success_count,
            'failed_count': len(files) - success_count,
            'results': results
        }
        
        return ApiResponse.success(data=response_data, message=f"批量上传完成，成功 {success_count} 个文件")
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"批量文件上传异常: {str(e)}")
        raise BusinessException(ErrorCode.SYSTEM_ERROR, "批量文件上传失败")
