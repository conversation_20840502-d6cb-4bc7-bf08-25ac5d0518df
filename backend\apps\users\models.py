"""
用户管理模块 - 用户模型
"""
from django.contrib.auth.models import AbstractUser, UserManager
from django.db import models
from apps.common.models import BaseModel, ActiveManager


class ActiveUserManager(UserManager):
    """活跃用户管理器 - 继承UserManager并过滤已删除对象"""
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)


class UserProfile(AbstractUser, BaseModel):
    """扩展用户模型"""
    # 基础字段
    username = models.CharField(max_length=150, unique=True, verbose_name="用户名")
    nickname = models.CharField(max_length=100, verbose_name="昵称")
    email = models.EmailField(blank=True, verbose_name="邮箱")
    
    # 预留扩展字段
    phone = models.CharField(max_length=11, blank=True, null=True, verbose_name="手机号")
    wechat_work_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="企业微信ID")
    
    # 用户信息
    avatar = models.URLField(blank=True, verbose_name="头像")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 状态管理
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name="最后登录IP")
    last_login_time = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")
    login_fail_count = models.IntegerField(default=0, verbose_name="登录失败次数")
    locked_until = models.DateTimeField(null=True, blank=True, verbose_name="锁定到期时间")
    
    # 管理器
    objects = ActiveUserManager()
    all_objects = UserManager()  # 包含已删除对象
    
    class Meta:
        db_table = 'auth_user_profile'
        verbose_name = "用户"
        verbose_name_plural = "用户"
    
    def __str__(self):
        return f"{self.nickname} ({self.username})"


class UserDepartment(BaseModel):
    """用户部门关联模型 - 支持用户在多个部门的关联"""

    user = models.ForeignKey(
        UserProfile,
        on_delete=models.CASCADE,
        related_name='user_departments',
        verbose_name="用户"
    )
    department = models.ForeignKey(
        'departments.Department',
        on_delete=models.CASCADE,
        related_name='department_users',
        verbose_name="部门"
    )

    # 关联属性
    is_primary = models.BooleanField(default=False, verbose_name="是否主部门")
    is_manager = models.BooleanField(default=False, verbose_name="是否部门负责人")
    position = models.CharField(max_length=100, blank=True, verbose_name="职位")

    # 有效期管理
    start_date = models.DateField(null=True, blank=True, verbose_name="开始日期")
    end_date = models.DateField(null=True, blank=True, verbose_name="结束日期")

    # 状态管理
    is_active = models.BooleanField(default=True, verbose_name="是否激活")

    objects = ActiveManager()

    class Meta:
        db_table = 'user_department'
        verbose_name = "用户部门关联"
        verbose_name_plural = "用户部门关联"
        unique_together = ['user', 'department']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['department', 'is_active']),
            models.Index(fields=['is_primary', 'is_active']),
        ]

    def __str__(self):
        return f"{self.user.nickname} - {self.department.name}"

    @classmethod
    def get_effective_relations(cls, user=None, department=None):
        """获取有效的用户部门关联"""
        from django.utils import timezone

        queryset = cls.objects.filter(is_active=True)

        if user:
            queryset = queryset.filter(user=user)
        if department:
            queryset = queryset.filter(department=department)

        # 过滤有效期
        now = timezone.now().date()
        queryset = queryset.filter(
            models.Q(start_date__isnull=True) | models.Q(start_date__lte=now)
        ).filter(
            models.Q(end_date__isnull=True) | models.Q(end_date__gte=now)
        )

        return queryset

    @classmethod
    def get_user_primary_department(cls, user):
        """获取用户主部门"""
        relation = cls.get_effective_relations(user=user).filter(is_primary=True).first()
        return relation.department if relation else None

    @classmethod
    def get_user_departments(cls, user):
        """获取用户所有部门"""
        relations = cls.get_effective_relations(user=user)
        return [rel.department for rel in relations]

    @classmethod
    def get_department_users(cls, department):
        """获取部门所有用户"""
        relations = cls.get_effective_relations(department=department)
        return [rel.user for rel in relations]