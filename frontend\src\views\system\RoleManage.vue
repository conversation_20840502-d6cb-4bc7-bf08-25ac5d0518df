<template>
  <div class="role-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">角色管理</h1>
          <p class="page-description">管理系统角色和权限分配</p>
        </div>

        <!-- 统计卡片 -->
        <div v-if="roleStats" class="stats-cards">
          <div class="stat-card">
            <div class="stat-value">{{ roleStats.total_roles }}</div>
            <div class="stat-label">总角色数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ roleStats.active_roles }}</div>
            <div class="stat-label">活跃角色</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ roleStats.total_permissions }}</div>
            <div class="stat-label">总权限数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ roleStats.total_user_roles }}</div>
            <div class="stat-label">用户角色关联</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 操作栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <!-- 搜索框 -->
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索角色名称、编码..."
            class="search-input"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
          </n-input>

          <!-- 筛选器 -->
          <n-select
            v-model:value="filterDataScope"
            placeholder="数据范围"
            class="filter-select"
            clearable
            @update:value="handleFilter"
          >
            <n-option
              v-for="option in dataScopeOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-select>

          <n-select
            v-model:value="filterStatus"
            placeholder="角色状态"
            class="filter-select"
            clearable
            @update:value="handleFilter"
          >
            <n-option value="true" label="已激活" />
            <n-option value="false" label="已禁用" />
          </n-select>
        </div>

        <div class="toolbar-right">
          <!-- 批量操作 -->
          <n-dropdown
            v-if="hasSelectedRoles"
            :options="batchOptions"
            @select="handleBatchOperation"
          >
            <n-button>
              批量操作 ({{ selectedRolesCount }})
              <template #icon>
                <n-icon><ChevronDownIcon /></n-icon>
              </template>
            </n-button>
          </n-dropdown>

          <!-- 权限管理按钮 -->
          <n-button
            v-permission="'permission:manage'"
            @click="handleManagePermissions"
          >
            <template #icon>
              <n-icon><KeyIcon /></n-icon>
            </template>
            权限管理
          </n-button>

          <!-- 新增角色按钮 -->
          <n-button
            v-permission="'role:create'"
            type="primary"
            @click="handleCreate"
          >
            <template #icon>
              <n-icon><PlusIcon /></n-icon>
            </template>
            新增角色
          </n-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <n-data-table
          :columns="tableColumns"
          :data="roleList"
          :loading="loading"
          :pagination="paginationConfig"
          :row-key="getRowKey"
          :checked-row-keys="selectedRoleIds"
          @update:checked-row-keys="handleSelectionChange"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
          @update:sorter="handleSort"
          striped
          flex-height
          class="role-table"
        />
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <RoleFormDialog
      v-model:visible="formDialogVisible"
      :role="editingRole"
      :mode="formMode"
      @success="handleFormSuccess"
    />

    <!-- 角色详情对话框 -->
    <RoleDetailDialog
      v-model:visible="detailDialogVisible"
      :role-id="viewingRoleId"
    />

    <!-- 权限分配对话框 -->
    <PermissionAssignDialog
      v-model:visible="permissionDialogVisible"
      :role-id="assigningRoleId"
      @success="handlePermissionAssignSuccess"
    />

    <!-- 用户分配对话框 -->
    <UserAssignDialog
      v-model:visible="userDialogVisible"
      :role-id="assigningRoleId"
      @success="handleUserAssignSuccess"
    />

    <!-- 角色复制对话框 -->
    <RoleCopyDialog
      v-model:visible="copyDialogVisible"
      :source-role="copyingRole"
      @success="handleCopySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage, useDialog, type DataTableColumns, type DropdownOption } from 'naive-ui'
import { useRouter } from 'vue-router'
import {
  SearchIcon,
  PlusIcon,
  ChevronDownIcon,
  KeyIcon,
  EditIcon,
  DeleteIcon,
  EyeIcon,
  UsersIcon,
  CopyIcon,
  SettingsIcon,
  CheckIcon,
  CloseIcon
} from '@vicons/tabler'
import { useRoleStore } from '@/stores/role'
import { hasPermission } from '@/utils/auth'
import type { Role, DataScope } from '@/types/role'
import RoleFormDialog from '@/components/role/RoleFormDialog.vue'
import RoleDetailDialog from '@/components/role/RoleDetailDialog.vue'
import PermissionAssignDialog from '@/components/role/PermissionAssignDialog.vue'
import UserAssignDialog from '@/components/role/UserAssignDialog.vue'
import RoleCopyDialog from '@/components/role/RoleCopyDialog.vue'

// 状态管理
const roleStore = useRoleStore()
const message = useMessage()
const dialog = useDialog()
const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const filterDataScope = ref<DataScope | null>(null)
const filterStatus = ref<string | null>(null)

// 对话框状态
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const userDialogVisible = ref(false)
const copyDialogVisible = ref(false)

const editingRole = ref<Role | null>(null)
const formMode = ref<'create' | 'edit'>('create')
const viewingRoleId = ref<number | null>(null)
const assigningRoleId = ref<number | null>(null)
const copyingRole = ref<Role | null>(null)

// 计算属性
const roleList = computed(() => roleStore.roleList)
const loading = computed(() => roleStore.roleLoading)
const selectedRoleIds = computed(() => roleStore.selectedRoleIds)
const hasSelectedRoles = computed(() => roleStore.hasSelectedRoles)
const selectedRolesCount = computed(() => roleStore.selectedRolesCount)
const roleStats = computed(() => roleStore.roleStats)
const dataScopeOptions = computed(() => roleStore.dataScopeOptions)

// 分页配置
const paginationConfig = computed(() => ({
  page: roleStore.rolePagination.page,
  pageSize: roleStore.rolePagination.page_size,
  itemCount: roleStore.rolePagination.total,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}))

// 表格行键
const getRowKey = (row: Role) => row.id

// 批量操作选项
const batchOptions: DropdownOption[] = [
  {
    label: '批量激活',
    key: 'activate',
    disabled: !hasPermission('role:update')
  },
  {
    label: '批量禁用',
    key: 'deactivate',
    disabled: !hasPermission('role:update')
  },
  {
    label: '批量删除',
    key: 'delete',
    disabled: !hasPermission('role:delete')
  }
]

// 数据范围标签映射
const dataScopeLabels: Record<DataScope, string> = {
  'ALL': '全部数据',
  'DEPT_AND_SUB': '本部门及子部门',
  'DEPT_ONLY': '仅本部门',
  'SELF_ONLY': '仅本人',
  'CUSTOM': '自定义'
}

// 表格列配置
const tableColumns: DataTableColumns<Role> = [
  {
    type: 'selection'
  },
  {
    title: '角色名称',
    key: 'name',
    width: 150,
    sorter: true,
    render: (row: Role) => h('span', { class: 'font-medium' }, row.name)
  },
  {
    title: '角色编码',
    key: 'code',
    width: 120,
    sorter: true,
    render: (row: Role) => h('code', { class: 'text-sm bg-gray-100 px-2 py-1 rounded' }, row.code)
  },
  {
    title: '数据范围',
    key: 'data_scope',
    width: 140,
    render: (row: Role) => h(
      'n-tag',
      { size: 'small', type: 'info' },
      dataScopeLabels[row.data_scope] || row.data_scope
    )
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    },
    render: (row: Role) => row.description || '-'
  },
  {
    title: '关联用户',
    key: 'user_count',
    width: 100,
    render: (row: Role) => h('span', { class: 'text-blue-600' }, `${row.user_count}人`)
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row: Role) => h(
      'n-tag',
      {
        type: row.is_active ? 'success' : 'error',
        size: 'small'
      },
      row.is_active ? '正常' : '禁用'
    )
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80,
    sorter: true
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    sorter: true,
    render: (row: Role) => new Date(row.created_at).toLocaleString('zh-CN')
  },
  {
    title: '操作',
    key: 'actions',
    width: 280,
    fixed: 'right',
    render: (row: Role) => {
      const actions = []

      // 查看详情
      if (hasPermission('role:view')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleView(row.id)
          }, { default: () => '查看', icon: () => h(EyeIcon) })
        )
      }

      // 编辑
      if (hasPermission('role:update')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleEdit(row)
          }, { default: () => '编辑', icon: () => h(EditIcon) })
        )
      }

      // 权限分配
      if (hasPermission('role:assign_permissions')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'warning',
            text: true,
            onClick: () => handleAssignPermissions(row.id)
          }, { default: () => '权限', icon: () => h(KeyIcon) })
        )
      }

      // 用户分配
      if (hasPermission('role:assign_users')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'info',
            text: true,
            onClick: () => handleAssignUsers(row.id)
          }, { default: () => '用户', icon: () => h(UsersIcon) })
        )
      }

      // 复制角色
      if (hasPermission('role:create')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'default',
            text: true,
            onClick: () => handleCopy(row)
          }, { default: () => '复制', icon: () => h(CopyIcon) })
        )
      }

      // 切换状态
      if (hasPermission('role:update')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: row.is_active ? 'error' : 'success',
            text: true,
            onClick: () => handleToggleStatus(row)
          }, {
            default: () => row.is_active ? '禁用' : '启用',
            icon: () => h(row.is_active ? CloseIcon : CheckIcon)
          })
        )
      }

      // 删除
      if (hasPermission('role:delete')) {
        actions.push(
          h('n-button', {
            size: 'small',
            type: 'error',
            text: true,
            onClick: () => handleDelete(row)
          }, { default: () => '删除', icon: () => h(DeleteIcon) })
        )
      }

      return h('div', { class: 'flex gap-1 flex-wrap' }, actions)
    }
  }
]

// 事件处理方法
const handleSearch = () => {
  roleStore.fetchRoleList({ ...roleStore.roleSearchParams, search: searchKeyword.value, page: 1 })
}

const handleFilter = () => {
  const filters: any = {}

  if (filterDataScope.value) {
    filters.data_scope = filterDataScope.value
  }

  if (filterStatus.value !== null) {
    filters.is_active = filterStatus.value === 'true'
  }

  roleStore.fetchRoleList({ ...roleStore.roleSearchParams, ...filters, page: 1 })
}

const handleCreate = () => {
  editingRole.value = null
  formMode.value = 'create'
  formDialogVisible.value = true
}

const handleEdit = (role: Role) => {
  editingRole.value = role
  formMode.value = 'edit'
  formDialogVisible.value = true
}

const handleView = (roleId: number) => {
  viewingRoleId.value = roleId
  detailDialogVisible.value = true
}

const handleAssignPermissions = (roleId: number) => {
  assigningRoleId.value = roleId
  permissionDialogVisible.value = true
}

const handleAssignUsers = (roleId: number) => {
  assigningRoleId.value = roleId
  userDialogVisible.value = true
}

const handleCopy = (role: Role) => {
  copyingRole.value = role
  copyDialogVisible.value = true
}

const handleToggleStatus = async (role: Role) => {
  const action = role.is_active ? '禁用' : '启用'

  dialog.warning({
    title: `确认${action}角色`,
    content: `确定要${action}角色 "${role.name}" 吗？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await roleStore.toggleRoleActive(role.id)
        message.success(`角色${action}成功`)
      } catch (error) {
        message.error(`角色${action}失败`)
      }
    }
  })
}

const handleDelete = (role: Role) => {
  dialog.error({
    title: '确认删除角色',
    content: `确定要删除角色 "${role.name}" 吗？此操作不可恢复！`,
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await roleStore.deleteRole(role.id)
        message.success('角色删除成功')
      } catch (error) {
        message.error('角色删除失败')
      }
    }
  })
}

const handleManagePermissions = () => {
  router.push('/system/permissions')
}

const handleSelectionChange = (keys: Array<string | number>) => {
  roleStore.selectedRoleIds = keys as number[]
}

const handlePageChange = (page: number) => {
  roleStore.fetchRoleList({ ...roleStore.roleSearchParams, page })
}

const handlePageSizeChange = (pageSize: number) => {
  roleStore.fetchRoleList({ ...roleStore.roleSearchParams, page_size: pageSize, page: 1 })
}

const handleSort = (sorter: any) => {
  if (sorter) {
    const ordering = sorter.order === 'descend' ? `-${sorter.columnKey}` : sorter.columnKey
    roleStore.fetchRoleList({ ...roleStore.roleSearchParams, ordering, page: 1 })
  }
}

const handleBatchOperation = async (key: string) => {
  const selectedIds = roleStore.selectedRoleIds

  if (selectedIds.length === 0) {
    message.warning('请先选择要操作的角色')
    return
  }

  let title = ''
  let content = ''
  let operation = key

  switch (key) {
    case 'activate':
      title = '批量激活角色'
      content = `确定要激活选中的 ${selectedIds.length} 个角色吗？`
      break
    case 'deactivate':
      title = '批量禁用角色'
      content = `确定要禁用选中的 ${selectedIds.length} 个角色吗？`
      break
    case 'delete':
      title = '批量删除角色'
      content = `确定要删除选中的 ${selectedIds.length} 个角色吗？此操作不可恢复！`
      break
  }

  dialog.warning({
    title,
    content,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await roleStore.batchOperateRoles({
          role_ids: selectedIds,
          operation: operation as any
        })
        message.success('批量操作成功')
      } catch (error) {
        message.error('批量操作失败')
      }
    }
  })
}

const handleFormSuccess = () => {
  formDialogVisible.value = false
  message.success(formMode.value === 'create' ? '角色创建成功' : '角色更新成功')
}

const handlePermissionAssignSuccess = () => {
  permissionDialogVisible.value = false
  message.success('权限分配成功')
}

const handleUserAssignSuccess = () => {
  userDialogVisible.value = false
  message.success('用户分配成功')
}

const handleCopySuccess = () => {
  copyDialogVisible.value = false
  message.success('角色复制成功')
}

// 生命周期
onMounted(async () => {
  // 获取角色列表
  await roleStore.fetchRoleList()

  // 获取角色统计
  await roleStore.fetchRoleStats()

  // 获取数据范围选项
  await roleStore.fetchDataScopeOptions()
})
</script>

<style scoped>
.role-manage-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px 20px;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 150px;
}

.table-container {
  flex: 1;
  padding: 0 24px 24px;
}

.role-table {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-right {
    justify-content: flex-end;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }

  .stats-cards {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-right {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
