/**
 * 权限事件系统
 * 用于实时更新权限状态
 */

// 权限事件类型
export enum PermissionEventType {
  PERMISSION_CHANGED = 'permission_changed',
  DEPARTMENT_CHANGED = 'department_changed',
  USER_ROLE_CHANGED = 'user_role_changed',
  LOGIN_STATUS_CHANGED = 'login_status_changed'
}

// 权限事件数据接口
export interface PermissionEventData {
  type: PermissionEventType
  payload: any
  timestamp: number
}

// 事件监听器类型
export type PermissionEventListener = (data: PermissionEventData) => void

// 权限事件管理器
class PermissionEventManager {
  private listeners: Map<PermissionEventType, Set<PermissionEventListener>> = new Map()
  private eventHistory: PermissionEventData[] = []
  private maxHistorySize = 100

  /**
   * 添加事件监听器
   */
  addEventListener(type: PermissionEventType, listener: PermissionEventListener): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set())
    }
    this.listeners.get(type)!.add(listener)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: PermissionEventType, listener: PermissionEventListener): void {
    const listeners = this.listeners.get(type)
    if (listeners) {
      listeners.delete(listener)
      if (listeners.size === 0) {
        this.listeners.delete(type)
      }
    }
  }

  /**
   * 触发事件
   */
  dispatchEvent(type: PermissionEventType, payload: any): void {
    const eventData: PermissionEventData = {
      type,
      payload,
      timestamp: Date.now()
    }

    // 记录事件历史
    this.eventHistory.push(eventData)
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift()
    }

    // 通知监听器
    const listeners = this.listeners.get(type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(eventData)
        } catch (error) {
          console.error('权限事件监听器执行失败:', error)
        }
      })
    }

    // 记录日志
    console.log(`权限事件触发: ${type}`, payload)
  }

  /**
   * 获取事件历史
   */
  getEventHistory(type?: PermissionEventType): PermissionEventData[] {
    if (type) {
      return this.eventHistory.filter(event => event.type === type)
    }
    return [...this.eventHistory]
  }

  /**
   * 清除事件历史
   */
  clearEventHistory(): void {
    this.eventHistory = []
  }

  /**
   * 获取所有监听器数量
   */
  getListenerCount(): number {
    let count = 0
    this.listeners.forEach(listeners => {
      count += listeners.size
    })
    return count
  }

  /**
   * 清除所有监听器
   */
  clearAllListeners(): void {
    this.listeners.clear()
  }
}

// 全局权限事件管理器实例
export const permissionEventManager = new PermissionEventManager()

// 便捷方法
export const onPermissionChange = (listener: PermissionEventListener) => {
  permissionEventManager.addEventListener(PermissionEventType.PERMISSION_CHANGED, listener)
}

export const onDepartmentChange = (listener: PermissionEventListener) => {
  permissionEventManager.addEventListener(PermissionEventType.DEPARTMENT_CHANGED, listener)
}

export const onUserRoleChange = (listener: PermissionEventListener) => {
  permissionEventManager.addEventListener(PermissionEventType.USER_ROLE_CHANGED, listener)
}

export const onLoginStatusChange = (listener: PermissionEventListener) => {
  permissionEventManager.addEventListener(PermissionEventType.LOGIN_STATUS_CHANGED, listener)
}

export const offPermissionChange = (listener: PermissionEventListener) => {
  permissionEventManager.removeEventListener(PermissionEventType.PERMISSION_CHANGED, listener)
}

export const offDepartmentChange = (listener: PermissionEventListener) => {
  permissionEventManager.removeEventListener(PermissionEventType.DEPARTMENT_CHANGED, listener)
}

export const offUserRoleChange = (listener: PermissionEventListener) => {
  permissionEventManager.removeEventListener(PermissionEventType.USER_ROLE_CHANGED, listener)
}

export const offLoginStatusChange = (listener: PermissionEventListener) => {
  permissionEventManager.removeEventListener(PermissionEventType.LOGIN_STATUS_CHANGED, listener)
}

// 触发事件的便捷方法
export const emitPermissionChange = (permissions: string[]) => {
  permissionEventManager.dispatchEvent(PermissionEventType.PERMISSION_CHANGED, { permissions })
}

export const emitDepartmentChange = (departmentId: number, departmentInfo: any) => {
  permissionEventManager.dispatchEvent(PermissionEventType.DEPARTMENT_CHANGED, { 
    departmentId, 
    departmentInfo 
  })
}

export const emitUserRoleChange = (roles: string[]) => {
  permissionEventManager.dispatchEvent(PermissionEventType.USER_ROLE_CHANGED, { roles })
}

export const emitLoginStatusChange = (isLoggedIn: boolean, userInfo?: any) => {
  permissionEventManager.dispatchEvent(PermissionEventType.LOGIN_STATUS_CHANGED, { 
    isLoggedIn, 
    userInfo 
  })
}

// Vue 3 组合式API钩子
export function usePermissionEvents() {
  return {
    onPermissionChange,
    onDepartmentChange,
    onUserRoleChange,
    onLoginStatusChange,
    offPermissionChange,
    offDepartmentChange,
    offUserRoleChange,
    offLoginStatusChange,
    emitPermissionChange,
    emitDepartmentChange,
    emitUserRoleChange,
    emitLoginStatusChange,
    getEventHistory: () => permissionEventManager.getEventHistory(),
    clearEventHistory: () => permissionEventManager.clearEventHistory()
  }
}
