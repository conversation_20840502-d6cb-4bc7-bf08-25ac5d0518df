"""
异常登录检测模块
基于多维度分析检测可疑登录行为
"""
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import get_user_model
from django.db.models import Count, Q
import logging

from .models import UserSession
from apps.audit.models import OperationLog

logger = logging.getLogger(__name__)
User = get_user_model()

class LoginAnomalyDetector:
    """登录异常检测器"""
    
    def __init__(self):
        self.cache_prefix = 'anomaly_detector'
        self.cache_timeout = 3600  # 1小时缓存
        
        # 异常检测阈值配置
        self.thresholds = {
            'max_failed_attempts': 5,           # 最大失败尝试次数
            'failed_attempts_window': 300,     # 失败尝试时间窗口（秒）
            'max_locations_per_day': 3,        # 每天最大登录地点数
            'max_devices_per_day': 5,          # 每天最大设备数
            'unusual_time_threshold': 0.1,     # 异常时间阈值
            'velocity_threshold': 1000,        # 地理位置速度阈值（公里/小时）
            'session_overlap_threshold': 3,    # 并发会话阈值
        }
    
    def detect_login_anomaly(self, user_id: int, ip_address: str, user_agent: str, 
                           location_info: Optional[Dict] = None) -> Dict[str, any]:
        """
        检测登录异常
        
        Args:
            user_id: 用户ID
            ip_address: IP地址
            user_agent: 用户代理
            location_info: 地理位置信息
            
        Returns:
            检测结果
        """
        result = {
            'is_anomalous': False,
            'risk_score': 0,
            'anomaly_types': [],
            'details': {},
            'recommendations': []
        }
        
        try:
            # 获取用户信息
            user = User.objects.get(id=user_id)
            
            # 生成设备指纹
            device_fingerprint = self._generate_device_fingerprint(user_agent)
            
            # 各种异常检测
            anomalies = []
            
            # 1. 失败登录尝试检测
            failed_attempts_anomaly = self._detect_failed_attempts(user_id, ip_address)
            if failed_attempts_anomaly['is_anomalous']:
                anomalies.append(failed_attempts_anomaly)
            
            # 2. 新设备检测
            new_device_anomaly = self._detect_new_device(user_id, device_fingerprint)
            if new_device_anomaly['is_anomalous']:
                anomalies.append(new_device_anomaly)
            
            # 3. 新IP地址检测
            new_ip_anomaly = self._detect_new_ip(user_id, ip_address)
            if new_ip_anomaly['is_anomalous']:
                anomalies.append(new_ip_anomaly)
            
            # 4. 异常时间检测
            time_anomaly = self._detect_unusual_time(user_id)
            if time_anomaly['is_anomalous']:
                anomalies.append(time_anomaly)
            
            # 5. 地理位置异常检测
            if location_info:
                location_anomaly = self._detect_location_anomaly(user_id, location_info)
                if location_anomaly['is_anomalous']:
                    anomalies.append(location_anomaly)
            
            # 6. 并发会话检测
            session_anomaly = self._detect_concurrent_sessions(user_id)
            if session_anomaly['is_anomalous']:
                anomalies.append(session_anomaly)
            
            # 7. 登录频率检测
            frequency_anomaly = self._detect_login_frequency(user_id)
            if frequency_anomaly['is_anomalous']:
                anomalies.append(frequency_anomaly)
            
            # 计算综合风险评分
            if anomalies:
                result['is_anomalous'] = True
                result['anomaly_types'] = [a['type'] for a in anomalies]
                result['details'] = {a['type']: a['details'] for a in anomalies}
                result['risk_score'] = self._calculate_risk_score(anomalies)
                result['recommendations'] = self._generate_recommendations(anomalies)
            
            # 记录检测结果
            self._log_detection_result(user_id, ip_address, result)
            
            return result
            
        except Exception as e:
            logger.error(f"登录异常检测失败: user_id={user_id}, error={e}")
            return result
    
    def _detect_failed_attempts(self, user_id: int, ip_address: str) -> Dict[str, any]:
        """检测失败登录尝试"""
        result = {'is_anomalous': False, 'type': 'failed_attempts', 'details': {}}
        
        try:
            # 检查最近时间窗口内的失败尝试
            since_time = timezone.now() - timedelta(seconds=self.thresholds['failed_attempts_window'])
            
            failed_count = OperationLog.objects.filter(
                user_id=user_id,
                operation_type='LOGIN',
                status_code__gte=400,
                created_at__gte=since_time
            ).count()
            
            # 检查来自同一IP的失败尝试
            ip_failed_count = OperationLog.objects.filter(
                ip_address=ip_address,
                operation_type='LOGIN',
                status_code__gte=400,
                created_at__gte=since_time
            ).count()
            
            if failed_count >= self.thresholds['max_failed_attempts'] or ip_failed_count >= self.thresholds['max_failed_attempts']:
                result['is_anomalous'] = True
                result['details'] = {
                    'user_failed_count': failed_count,
                    'ip_failed_count': ip_failed_count,
                    'threshold': self.thresholds['max_failed_attempts'],
                    'time_window': self.thresholds['failed_attempts_window']
                }
                
        except Exception as e:
            logger.error(f"检测失败登录尝试异常: {e}")
            
        return result
    
    def _detect_new_device(self, user_id: int, device_fingerprint: str) -> Dict[str, any]:
        """检测新设备登录"""
        result = {'is_anomalous': False, 'type': 'new_device', 'details': {}}
        
        try:
            # 检查用户历史设备
            cache_key = f"{self.cache_prefix}:devices:{user_id}"
            known_devices = cache.get(cache_key)
            
            if known_devices is None:
                # 从数据库获取历史设备
                recent_sessions = UserSession.objects.filter(
                    user_id=user_id,
                    created_at__gte=timezone.now() - timedelta(days=30)
                ).values_list('device_fingerprint', flat=True).distinct()
                
                known_devices = set(recent_sessions)
                cache.set(cache_key, known_devices, self.cache_timeout)
            
            if device_fingerprint not in known_devices:
                result['is_anomalous'] = True
                result['details'] = {
                    'device_fingerprint': device_fingerprint,
                    'known_devices_count': len(known_devices)
                }
                
                # 更新已知设备列表
                known_devices.add(device_fingerprint)
                cache.set(cache_key, known_devices, self.cache_timeout)
                
        except Exception as e:
            logger.error(f"检测新设备异常: {e}")
            
        return result
    
    def _detect_new_ip(self, user_id: int, ip_address: str) -> Dict[str, any]:
        """检测新IP地址登录"""
        result = {'is_anomalous': False, 'type': 'new_ip', 'details': {}}
        
        try:
            # 检查用户历史IP
            cache_key = f"{self.cache_prefix}:ips:{user_id}"
            known_ips = cache.get(cache_key)
            
            if known_ips is None:
                # 从数据库获取历史IP
                recent_logs = OperationLog.objects.filter(
                    user_id=user_id,
                    operation_type='LOGIN',
                    status_code=200,
                    created_at__gte=timezone.now() - timedelta(days=30)
                ).values_list('ip_address', flat=True).distinct()
                
                known_ips = set(recent_logs)
                cache.set(cache_key, known_ips, self.cache_timeout)
            
            if ip_address not in known_ips:
                result['is_anomalous'] = True
                result['details'] = {
                    'ip_address': ip_address,
                    'known_ips_count': len(known_ips)
                }
                
                # 更新已知IP列表
                known_ips.add(ip_address)
                cache.set(cache_key, known_ips, self.cache_timeout)
                
        except Exception as e:
            logger.error(f"检测新IP异常: {e}")
            
        return result
    
    def _detect_unusual_time(self, user_id: int) -> Dict[str, any]:
        """检测异常时间登录"""
        result = {'is_anomalous': False, 'type': 'unusual_time', 'details': {}}
        
        try:
            current_hour = timezone.now().hour
            
            # 获取用户历史登录时间分布
            cache_key = f"{self.cache_prefix}:time_pattern:{user_id}"
            time_pattern = cache.get(cache_key)
            
            if time_pattern is None:
                # 分析最近30天的登录时间
                recent_logs = OperationLog.objects.filter(
                    user_id=user_id,
                    operation_type='LOGIN',
                    status_code=200,
                    created_at__gte=timezone.now() - timedelta(days=30)
                )
                
                hour_counts = {}
                for log in recent_logs:
                    hour = log.created_at.hour
                    hour_counts[hour] = hour_counts.get(hour, 0) + 1
                
                total_logins = sum(hour_counts.values())
                time_pattern = {hour: count / total_logins for hour, count in hour_counts.items()}
                cache.set(cache_key, time_pattern, self.cache_timeout)
            
            # 检查当前时间是否异常
            current_probability = time_pattern.get(current_hour, 0)
            if current_probability < self.thresholds['unusual_time_threshold']:
                result['is_anomalous'] = True
                result['details'] = {
                    'current_hour': current_hour,
                    'probability': current_probability,
                    'threshold': self.thresholds['unusual_time_threshold']
                }
                
        except Exception as e:
            logger.error(f"检测异常时间异常: {e}")
            
        return result
    
    def _detect_location_anomaly(self, user_id: int, location_info: Dict) -> Dict[str, any]:
        """检测地理位置异常"""
        result = {'is_anomalous': False, 'type': 'location_anomaly', 'details': {}}
        
        try:
            current_lat = location_info.get('latitude')
            current_lon = location_info.get('longitude')
            
            if not (current_lat and current_lon):
                return result
            
            # 获取最近的登录位置
            recent_log = OperationLog.objects.filter(
                user_id=user_id,
                operation_type='LOGIN',
                status_code=200,
                created_at__gte=timezone.now() - timedelta(hours=1)
            ).exclude(
                request_data__isnull=True
            ).order_by('-created_at').first()
            
            if recent_log and recent_log.request_data:
                try:
                    request_data = json.loads(recent_log.request_data)
                    last_location = request_data.get('location_info', {})
                    last_lat = last_location.get('latitude')
                    last_lon = last_location.get('longitude')
                    
                    if last_lat and last_lon:
                        # 计算距离和速度
                        distance = self._calculate_distance(last_lat, last_lon, current_lat, current_lon)
                        time_diff = (timezone.now() - recent_log.created_at).total_seconds() / 3600  # 小时
                        
                        if time_diff > 0:
                            velocity = distance / time_diff  # 公里/小时
                            
                            if velocity > self.thresholds['velocity_threshold']:
                                result['is_anomalous'] = True
                                result['details'] = {
                                    'distance_km': distance,
                                    'time_hours': time_diff,
                                    'velocity_kmh': velocity,
                                    'threshold': self.thresholds['velocity_threshold']
                                }
                                
                except json.JSONDecodeError:
                    pass
                    
        except Exception as e:
            logger.error(f"检测地理位置异常: {e}")
            
        return result
    
    def _detect_concurrent_sessions(self, user_id: int) -> Dict[str, any]:
        """检测并发会话异常"""
        result = {'is_anomalous': False, 'type': 'concurrent_sessions', 'details': {}}
        
        try:
            active_sessions = UserSession.objects.filter(
                user_id=user_id,
                is_active=True
            ).count()
            
            if active_sessions >= self.thresholds['session_overlap_threshold']:
                result['is_anomalous'] = True
                result['details'] = {
                    'active_sessions': active_sessions,
                    'threshold': self.thresholds['session_overlap_threshold']
                }
                
        except Exception as e:
            logger.error(f"检测并发会话异常: {e}")
            
        return result
    
    def _detect_login_frequency(self, user_id: int) -> Dict[str, any]:
        """检测登录频率异常"""
        result = {'is_anomalous': False, 'type': 'login_frequency', 'details': {}}
        
        try:
            # 检查最近1小时的登录次数
            recent_logins = OperationLog.objects.filter(
                user_id=user_id,
                operation_type='LOGIN',
                created_at__gte=timezone.now() - timedelta(hours=1)
            ).count()
            
            # 异常频率阈值（1小时内超过10次登录）
            if recent_logins > 10:
                result['is_anomalous'] = True
                result['details'] = {
                    'recent_logins': recent_logins,
                    'time_window': '1 hour',
                    'threshold': 10
                }
                
        except Exception as e:
            logger.error(f"检测登录频率异常: {e}")
            
        return result
    
    def _generate_device_fingerprint(self, user_agent: str) -> str:
        """生成设备指纹"""
        return hashlib.md5(user_agent.encode()).hexdigest()
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间距离（公里）"""
        import math
        
        # 使用Haversine公式
        R = 6371  # 地球半径（公里）
        
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        
        a = (math.sin(dlat / 2) * math.sin(dlat / 2) +
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) *
             math.sin(dlon / 2) * math.sin(dlon / 2))
        
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        distance = R * c
        
        return distance
    
    def _calculate_risk_score(self, anomalies: List[Dict]) -> int:
        """计算风险评分（0-100）"""
        risk_weights = {
            'failed_attempts': 30,
            'new_device': 15,
            'new_ip': 10,
            'unusual_time': 5,
            'location_anomaly': 25,
            'concurrent_sessions': 10,
            'login_frequency': 20
        }
        
        total_score = 0
        for anomaly in anomalies:
            weight = risk_weights.get(anomaly['type'], 5)
            total_score += weight
        
        return min(total_score, 100)
    
    def _generate_recommendations(self, anomalies: List[Dict]) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        anomaly_types = [a['type'] for a in anomalies]
        
        if 'failed_attempts' in anomaly_types:
            recommendations.append("建议启用账户临时锁定")
            recommendations.append("考虑启用验证码验证")
        
        if 'new_device' in anomaly_types or 'new_ip' in anomaly_types:
            recommendations.append("建议启用双因素认证")
            recommendations.append("发送邮件通知用户")
        
        if 'location_anomaly' in anomaly_types:
            recommendations.append("建议人工审核此次登录")
            recommendations.append("考虑要求额外身份验证")
        
        if 'concurrent_sessions' in anomaly_types:
            recommendations.append("建议限制并发会话数量")
            recommendations.append("强制下线其他会话")
        
        if 'login_frequency' in anomaly_types:
            recommendations.append("建议启用登录频率限制")
            recommendations.append("检查是否存在自动化攻击")
        
        return recommendations
    
    def _log_detection_result(self, user_id: int, ip_address: str, result: Dict):
        """记录检测结果"""
        try:
            if result['is_anomalous']:
                logger.warning(
                    f"检测到异常登录: user_id={user_id}, ip={ip_address}, "
                    f"risk_score={result['risk_score']}, types={result['anomaly_types']}"
                )
                
                # 可以在这里触发告警通知
                self._trigger_security_alert(user_id, ip_address, result)
                
        except Exception as e:
            logger.error(f"记录检测结果失败: {e}")
    
    def _trigger_security_alert(self, user_id: int, ip_address: str, result: Dict):
        """触发安全告警"""
        try:
            # 这里可以集成邮件、短信、钉钉等告警通知
            # 暂时只记录日志
            logger.critical(
                f"安全告警: 用户 {user_id} 从 {ip_address} 的登录存在异常, "
                f"风险评分: {result['risk_score']}, 异常类型: {result['anomaly_types']}"
            )
            
        except Exception as e:
            logger.error(f"触发安全告警失败: {e}")

# 全局异常检测器实例
anomaly_detector = LoginAnomalyDetector()
