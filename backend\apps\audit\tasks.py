"""
审计日志模块 - 异步任务
"""
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from datetime import timedelta
import logging

from .models import OperationLog

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def cleanup_old_operation_logs(self, days=None):
    """
    清理过期操作日志的异步任务
    
    Args:
        days: 保留天数，默认从配置读取
    """
    try:
        # 获取保留天数配置
        if days is None:
            days = getattr(settings, 'AUDIT_LOG_RETENTION_DAYS', 90)
        
        # 执行清理
        deleted_count, _ = OperationLog.cleanup_old_logs(days)
        
        logger.info(f"清理过期操作日志完成，删除了 {deleted_count} 条记录，保留 {days} 天内的日志")
        
        return {
            'status': 'success',
            'deleted_count': deleted_count,
            'retention_days': days,
            'timestamp': timezone.now().isoformat()
        }
    except Exception as exc:
        logger.error(f"清理过期操作日志失败: {exc}")
        # 重试任务，每次间隔5分钟
        raise self.retry(exc=exc, countdown=300)


@shared_task(bind=True, max_retries=3)
def cleanup_large_operation_logs(self, max_records=None):
    """
    清理过多的操作日志，保持数据库性能
    
    Args:
        max_records: 最大保留记录数，默认从配置读取
    """
    try:
        # 获取最大记录数配置
        if max_records is None:
            max_records = getattr(settings, 'AUDIT_LOG_MAX_RECORDS', 1000000)  # 默认100万条
        
        # 检查当前记录数
        current_count = OperationLog.objects.count()
        
        if current_count <= max_records:
            logger.info(f"操作日志记录数 {current_count} 未超过限制 {max_records}，无需清理")
            return {
                'status': 'success',
                'deleted_count': 0,
                'current_count': current_count,
                'max_records': max_records,
                'timestamp': timezone.now().isoformat()
            }
        
        # 计算需要删除的记录数
        delete_count = current_count - max_records
        
        # 删除最旧的记录
        old_logs = OperationLog.objects.order_by('created_at')[:delete_count]
        old_log_ids = list(old_logs.values_list('id', flat=True))
        
        deleted_count, _ = OperationLog.objects.filter(id__in=old_log_ids).delete()
        
        logger.info(f"清理过多操作日志完成，删除了 {deleted_count} 条最旧记录，当前记录数: {current_count - deleted_count}")
        
        return {
            'status': 'success',
            'deleted_count': deleted_count,
            'current_count': current_count - deleted_count,
            'max_records': max_records,
            'timestamp': timezone.now().isoformat()
        }
    except Exception as exc:
        logger.error(f"清理过多操作日志失败: {exc}")
        # 重试任务，每次间隔10分钟
        raise self.retry(exc=exc, countdown=600)


@shared_task(bind=True, max_retries=3)
def generate_audit_statistics(self):
    """
    生成审计日志统计信息的异步任务
    """
    try:
        from django.db.models import Count
        from django.core.cache import cache
        
        now = timezone.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=7)
        month_start = today_start - timedelta(days=30)
        
        # 基础统计
        stats = {
            'total_logs': OperationLog.objects.count(),
            'today_logs': OperationLog.objects.filter(created_at__gte=today_start).count(),
            'week_logs': OperationLog.objects.filter(created_at__gte=week_start).count(),
            'month_logs': OperationLog.objects.filter(created_at__gte=month_start).count(),
        }
        
        # 操作类型统计
        operation_stats = dict(
            OperationLog.objects.values('operation_type').annotate(
                count=Count('id')
            ).values_list('operation_type', 'count')
        )
        stats['operation_type_stats'] = operation_stats
        
        # 用户活跃度统计（今日）
        user_stats = list(
            OperationLog.objects.filter(
                user__isnull=False,
                created_at__gte=today_start
            ).values('user__username', 'user__nickname').annotate(
                count=Count('id')
            ).order_by('-count')[:20]  # 前20名活跃用户
        )
        stats['active_users'] = user_stats
        
        # 错误统计（状态码>=400）
        error_stats = dict(
            OperationLog.objects.filter(
                status_code__gte=400,
                created_at__gte=today_start
            ).values('status_code').annotate(
                count=Count('id')
            ).values_list('status_code', 'count')
        )
        stats['error_stats'] = error_stats
        
        # 缓存统计信息（缓存1小时）
        cache_key = 'audit_statistics'
        cache.set(cache_key, stats, 3600)
        
        logger.info("审计日志统计信息生成完成")
        
        return {
            'status': 'success',
            'stats': stats,
            'timestamp': timezone.now().isoformat()
        }
    except Exception as exc:
        logger.error(f"生成审计日志统计信息失败: {exc}")
        raise self.retry(exc=exc, countdown=300)


@shared_task
def comprehensive_audit_cleanup():
    """
    综合审计日志清理任务，包含多种清理策略
    """
    results = {}
    
    # 按时间清理过期日志
    try:
        time_cleanup_result = cleanup_old_operation_logs.delay()
        results['time_cleanup'] = time_cleanup_result.get(timeout=300)
    except Exception as e:
        logger.error(f"按时间清理日志任务失败: {e}")
        results['time_cleanup'] = {'status': 'error', 'error': str(e)}
    
    # 按数量清理过多日志
    try:
        count_cleanup_result = cleanup_large_operation_logs.delay()
        results['count_cleanup'] = count_cleanup_result.get(timeout=300)
    except Exception as e:
        logger.error(f"按数量清理日志任务失败: {e}")
        results['count_cleanup'] = {'status': 'error', 'error': str(e)}
    
    # 生成统计信息
    try:
        stats_result = generate_audit_statistics.delay()
        results['statistics'] = stats_result.get(timeout=120)
    except Exception as e:
        logger.error(f"生成统计信息任务失败: {e}")
        results['statistics'] = {'status': 'error', 'error': str(e)}
    
    return results


@shared_task(bind=True, max_retries=3)
def archive_old_operation_logs(self, days=None, archive_format='json'):
    """
    归档旧的操作日志到文件
    
    Args:
        days: 归档多少天前的日志，默认180天
        archive_format: 归档格式，支持 'json', 'csv'
    """
    try:
        import json
        import csv
        import os
        from django.conf import settings
        
        # 获取归档天数配置
        if days is None:
            days = getattr(settings, 'AUDIT_LOG_ARCHIVE_DAYS', 180)
        
        # 计算归档截止日期
        archive_date = timezone.now() - timedelta(days=days)
        
        # 查询需要归档的日志
        logs_to_archive = OperationLog.objects.filter(
            created_at__lt=archive_date
        ).order_by('created_at')
        
        if not logs_to_archive.exists():
            logger.info(f"没有需要归档的日志（{days}天前）")
            return {
                'status': 'success',
                'archived_count': 0,
                'archive_days': days,
                'timestamp': timezone.now().isoformat()
            }
        
        # 创建归档目录
        archive_dir = getattr(settings, 'AUDIT_LOG_ARCHIVE_DIR', 
                             os.path.join(settings.BASE_DIR, 'archives'))
        os.makedirs(archive_dir, exist_ok=True)
        
        # 生成归档文件名
        archive_filename = f"operation_logs_{archive_date.strftime('%Y%m%d')}.{archive_format}"
        archive_path = os.path.join(archive_dir, archive_filename)
        
        # 归档数据
        archived_count = 0
        
        if archive_format == 'json':
            logs_data = []
            for log in logs_to_archive:
                logs_data.append({
                    'id': log.id,
                    'user_id': log.user_id,
                    'user_nickname': log.user.nickname if log.user else None,
                    'operation_type': log.operation_type,
                    'operation_desc': log.operation_desc,
                    'method': log.method,
                    'path': log.path,
                    'ip_address': log.ip_address,
                    'user_agent': log.user_agent,
                    'status_code': log.status_code,
                    'response_time': log.response_time,
                    'created_at': log.created_at.isoformat()
                })
            
            with open(archive_path, 'w', encoding='utf-8') as f:
                json.dump(logs_data, f, ensure_ascii=False, indent=2)
            
            archived_count = len(logs_data)
        
        elif archive_format == 'csv':
            with open(archive_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                # 写入表头
                writer.writerow([
                    'ID', '用户ID', '用户昵称', '操作类型', '操作描述',
                    '请求方法', '请求路径', 'IP地址', '用户代理',
                    '状态码', '响应时间', '创建时间'
                ])
                
                # 写入数据
                for log in logs_to_archive:
                    writer.writerow([
                        log.id,
                        log.user_id,
                        log.user.nickname if log.user else '',
                        log.operation_type,
                        log.operation_desc,
                        log.method,
                        log.path,
                        log.ip_address,
                        log.user_agent,
                        log.status_code,
                        log.response_time,
                        log.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    ])
                    archived_count += 1
        
        # 删除已归档的日志
        deleted_count, _ = logs_to_archive.delete()
        
        logger.info(f"操作日志归档完成，归档了 {archived_count} 条记录到 {archive_path}")
        
        return {
            'status': 'success',
            'archived_count': archived_count,
            'deleted_count': deleted_count,
            'archive_file': archive_path,
            'archive_days': days,
            'timestamp': timezone.now().isoformat()
        }
    except Exception as exc:
        logger.error(f"归档操作日志失败: {exc}")
        raise self.retry(exc=exc, countdown=600)
