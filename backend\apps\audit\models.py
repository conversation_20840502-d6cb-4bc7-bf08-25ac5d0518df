"""
审计日志模块 - 操作日志模型
"""
from django.db import models


class OperationLog(models.Model):
    """操作审计日志 - 不继承BaseModel，避免软删除"""
    OPERATION_TYPE_CHOICES = [
        ('LOGIN', '登录'),
        ('LOGOUT', '登出'),
        ('CREATE', '创建'),
        ('UPDATE', '更新'),
        ('DELETE', '删除'),
        ('QUERY', '查询'),
        ('ERROR', '系统异常'),
    ]
    
    # 操作信息
    user = models.ForeignKey('users.UserProfile', on_delete=models.SET_NULL, null=True, verbose_name="操作用户")
    operation_type = models.CharField(max_length=10, choices=OPERATION_TYPE_CHOICES, verbose_name="操作类型")
    operation_desc = models.CharField(max_length=500, verbose_name="操作描述")
    
    # 请求信息
    method = models.CharField(max_length=10, verbose_name="请求方法")
    path = models.CharField(max_length=500, verbose_name="请求路径")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(blank=True, verbose_name="用户代理")
    
    # 响应信息
    status_code = models.IntegerField(verbose_name="状态码")
    response_time = models.IntegerField(verbose_name="响应时间(ms)")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="操作时间")
    
    class Meta:
        db_table = 'sys_operation_log'
        verbose_name = "操作日志"
        verbose_name_plural = "操作日志"
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['operation_type', 'created_at']),
        ]
    
    @classmethod
    def cleanup_old_logs(cls, days=90):
        """清理过期日志"""
        from django.utils import timezone
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        return cls.objects.filter(created_at__lt=cutoff_date).delete()
    
    def __str__(self):
        user_str = self.user.nickname if self.user else "系统"
        return f"{user_str} - {self.operation_desc} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"