#!/usr/bin/env python
"""
测试HEIM项目任务16的三个核心功能模块
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
django.setup()

from apps.monitoring.services import system_monitor, health_checker
from apps.health.views import health_check


def test_system_monitoring():
    """测试系统监控功能"""
    print("🔍 测试系统监控功能")
    
    try:
        # 测试系统指标收集
        print("  📊 收集系统指标...")
        system_metrics = system_monitor.collect_system_metrics()
        print(f"    ✅ CPU: {system_metrics.cpu_usage}%, 内存: {system_metrics.memory_usage}%, 磁盘: {system_metrics.disk_usage:.1f}%")
        
        # 测试应用指标收集
        print("  📈 收集应用指标...")
        app_metrics = system_monitor.collect_application_metrics()
        print(f"    ✅ 请求: {app_metrics.total_requests}, 错误率: {app_metrics.error_rate:.2f}%, 活跃用户: {app_metrics.active_users}")
        
        # 测试健康检查
        print("  🏥 执行健康检查...")
        health_results = health_checker.check_all_services()
        healthy_count = sum(1 for r in health_results if r.status == 'healthy')
        print(f"    ✅ 检查了 {len(health_results)} 个服务，{healthy_count} 个健康")
        
        print("  ✅ 系统监控功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 系统监控功能测试失败: {e}")
        return False


def test_backup_and_restore():
    """测试数据备份和恢复功能"""
    print("\n🗄️ 测试数据备份和恢复功能")
    
    try:
        from apps.backup.models import BackupJob, BackupSchedule, BackupStorage
        from apps.backup.services import backup_service
        
        # 测试备份任务模型
        print("  📝 创建备份任务...")
        backup_job = BackupJob.objects.create(
            name="测试备份",
            description="功能测试备份",
            backup_type="full",
            compression="gzip",
            include_media=False,
            include_logs=False
        )
        print(f"    ✅ 备份任务创建成功: {backup_job.name}")
        
        # 测试备份计划模型
        print("  📅 创建备份计划...")
        backup_schedule = BackupSchedule.objects.create(
            name="每日备份计划",
            description="每天执行的备份计划",
            frequency="daily",
            backup_type="full"
        )
        print(f"    ✅ 备份计划创建成功: {backup_schedule.name}")
        
        # 测试备份存储配置
        print("  💾 创建存储配置...")
        backup_storage = BackupStorage.objects.create(
            name="本地存储",
            storage_type="local",
            config={"path": "/tmp/backups"},
            is_default=True
        )
        print(f"    ✅ 存储配置创建成功: {backup_storage.name}")
        
        print("  ✅ 数据备份和恢复功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 数据备份和恢复功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_health_check():
    """测试系统健康检查功能"""
    print("\n🏥 测试系统健康检查功能")
    
    try:
        from django.test import RequestFactory
        from apps.health.views import health_check, liveness_probe, readiness_probe, system_info
        
        factory = RequestFactory()
        
        # 测试综合健康检查
        print("  🔍 执行综合健康检查...")
        request = factory.get('/health/')
        response = health_check(request)
        health_data = json.loads(response.content)
        print(f"    ✅ 系统状态: {health_data['status']}")
        print(f"    ✅ 响应时间: {health_data['response_time_ms']}ms")
        
        # 测试存活性探针
        print("  💓 测试存活性探针...")
        request = factory.get('/health/live/')
        response = liveness_probe(request)
        live_data = json.loads(response.content)
        print(f"    ✅ 存活状态: {live_data['status']}")
        
        # 测试就绪性探针
        print("  🚀 测试就绪性探针...")
        request = factory.get('/health/ready/')
        response = readiness_probe(request)
        ready_data = json.loads(response.content)
        print(f"    ✅ 就绪状态: {ready_data['status']}")
        
        # 测试系统信息
        print("  📊 获取系统信息...")
        request = factory.get('/health/info/')
        response = system_info(request)
        info_data = json.loads(response.content)
        print(f"    ✅ 系统平台: {info_data['system']['platform']}")
        print(f"    ✅ CPU核心数: {info_data['cpu']['cpu_count']}")
        print(f"    ✅ 内存使用率: {info_data['memory']['percent']:.1f}%")
        
        print("  ✅ 系统健康检查功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 系统健康检查功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_migrations():
    """测试数据库迁移"""
    print("\n🗃️ 测试数据库迁移")
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # 检查迁移状态
        print("  📋 检查迁移状态...")
        out = StringIO()
        call_command('showmigrations', stdout=out)
        migrations_output = out.getvalue()
        
        # 检查是否有未应用的迁移
        if '[X]' in migrations_output:
            print("    ✅ 所有迁移已应用")
        else:
            print("    ⚠️ 存在未应用的迁移")
        
        # 检查模型
        print("  🔍 验证模型...")
        from apps.monitoring.models import SystemMetrics, ApplicationMetrics, ServiceHealthCheck
        from apps.backup.models import BackupJob, BackupSchedule, BackupStorage
        
        # 测试模型创建
        print("    ✅ 监控模型可用")
        print("    ✅ 备份模型可用")
        
        print("  ✅ 数据库迁移测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库迁移测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🎯 HEIM项目任务16功能模块测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试数据库迁移
    test_results.append(test_database_migrations())
    
    # 测试系统监控功能
    test_results.append(test_system_monitoring())
    
    # 测试数据备份和恢复功能
    test_results.append(test_backup_and_restore())
    
    # 测试系统健康检查功能
    test_results.append(test_health_check())
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    test_names = [
        "数据库迁移",
        "系统监控功能", 
        "数据备份和恢复功能",
        "系统健康检查功能"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    print(f"\n🎉 测试完成: {passed_tests}/{total_tests} 个功能模块通过测试")
    
    if passed_tests == total_tests:
        print("🎊 所有功能模块测试通过！HEIM项目任务16完成度良好。")
        return 0
    else:
        print("⚠️ 部分功能模块需要进一步完善。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
