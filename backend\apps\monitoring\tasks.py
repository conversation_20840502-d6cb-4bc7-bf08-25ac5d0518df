"""
系统监控定时任务
"""
from celery import shared_task
from django.utils import timezone
from datetime import timedelta
import logging

from .services import system_monitor, health_checker, alert_service
from .models import SystemMetrics, ApplicationMetrics, ServiceHealthCheck

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def collect_system_metrics_task(self):
    """
    定时收集系统性能指标
    """
    try:
        # 收集系统指标
        system_metrics = system_monitor.collect_system_metrics()
        app_metrics = system_monitor.collect_application_metrics()
        
        logger.info(f"定时任务：系统指标收集完成 - CPU: {system_metrics.cpu_usage}%, 内存: {system_metrics.memory_usage}%")
        
        return {
            'status': 'success',
            'system_metrics_id': system_metrics.id,
            'app_metrics_id': app_metrics.id,
            'collected_at': system_metrics.collected_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"定时任务：收集系统指标失败 - {str(e)}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"定时任务：重试收集系统指标 ({self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=60, exc=e)
        
        return {
            'status': 'failed',
            'error': str(e),
            'retries': self.request.retries
        }


@shared_task(bind=True, max_retries=3)
def health_check_task(self):
    """
    定时执行服务健康检查
    """
    try:
        # 执行健康检查
        results = health_checker.check_all_services()
        
        # 统计结果
        total_services = len(results)
        healthy_services = sum(1 for r in results if r.status == 'healthy')
        critical_services = sum(1 for r in results if r.status == 'critical')
        
        logger.info(f"定时任务：健康检查完成 - 总服务: {total_services}, 健康: {healthy_services}, 严重: {critical_services}")
        
        return {
            'status': 'success',
            'total_services': total_services,
            'healthy_services': healthy_services,
            'critical_services': critical_services,
            'checked_at': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"定时任务：健康检查失败 - {str(e)}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"定时任务：重试健康检查 ({self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=60, exc=e)
        
        return {
            'status': 'failed',
            'error': str(e),
            'retries': self.request.retries
        }


@shared_task(bind=True, max_retries=3)
def check_alert_rules_task(self):
    """
    定时检查告警规则
    """
    try:
        # 检查告警规则
        alert_service.check_alert_rules()
        
        logger.info("定时任务：告警规则检查完成")
        
        return {
            'status': 'success',
            'checked_at': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"定时任务：检查告警规则失败 - {str(e)}")
        
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"定时任务：重试检查告警规则 ({self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=60, exc=e)
        
        return {
            'status': 'failed',
            'error': str(e),
            'retries': self.request.retries
        }


@shared_task
def cleanup_old_metrics_task():
    """
    清理过期的监控数据
    """
    try:
        # 保留最近30天的数据
        cutoff_date = timezone.now() - timedelta(days=30)
        
        # 清理系统指标
        deleted_system = SystemMetrics.objects.filter(
            collected_at__lt=cutoff_date
        ).delete()
        
        # 清理应用指标
        deleted_app = ApplicationMetrics.objects.filter(
            collected_at__lt=cutoff_date
        ).delete()
        
        # 清理健康检查记录
        deleted_health = ServiceHealthCheck.objects.filter(
            checked_at__lt=cutoff_date
        ).delete()
        
        logger.info(f"定时任务：清理过期监控数据完成 - 系统指标: {deleted_system[0]}, 应用指标: {deleted_app[0]}, 健康检查: {deleted_health[0]}")
        
        return {
            'status': 'success',
            'deleted_system_metrics': deleted_system[0],
            'deleted_app_metrics': deleted_app[0],
            'deleted_health_checks': deleted_health[0],
            'cutoff_date': cutoff_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f"定时任务：清理过期监控数据失败 - {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


@shared_task
def generate_monitoring_report_task():
    """
    生成监控报告
    """
    try:
        # 计算时间范围（最近24小时）
        end_time = timezone.now()
        start_time = end_time - timedelta(hours=24)
        
        # 获取系统指标统计
        from django.db import models

        system_stats = SystemMetrics.objects.filter(
            collected_at__range=[start_time, end_time]
        ).aggregate(
            avg_cpu=models.Avg('cpu_usage'),
            max_cpu=models.Max('cpu_usage'),
            avg_memory=models.Avg('memory_usage'),
            max_memory=models.Max('memory_usage'),
            avg_disk=models.Avg('disk_usage'),
            max_disk=models.Max('disk_usage'),
        )

        # 获取应用指标统计
        app_stats = ApplicationMetrics.objects.filter(
            collected_at__range=[start_time, end_time]
        ).aggregate(
            total_requests=models.Max('total_requests'),
            avg_error_rate=models.Avg('error_rate'),
            avg_response_time=models.Avg('avg_response_time'),
            max_response_time=models.Max('max_response_time'),
        )

        # 获取健康检查统计
        health_stats = ServiceHealthCheck.objects.filter(
            checked_at__range=[start_time, end_time]
        ).values('status').annotate(count=models.Count('id'))
        
        report = {
            'period': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat(),
                'duration_hours': 24
            },
            'system_performance': system_stats,
            'application_performance': app_stats,
            'service_health': {stat['status']: stat['count'] for stat in health_stats},
            'generated_at': timezone.now().isoformat()
        }
        
        logger.info("定时任务：监控报告生成完成")
        
        # TODO: 可以将报告发送给管理员或保存到文件
        
        return {
            'status': 'success',
            'report': report
        }
        
    except Exception as e:
        logger.error(f"定时任务：生成监控报告失败 - {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }
