{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.3", "globals": "e387b342220b7c815305f290beb0417f", "files": {"z_9e189ee61a034fc9___init___py": {"hash": "62b6b2dd5569ab3e8499ebeccf9ece10", "index": {"url": "z_9e189ee61a034fc9___init___py.html", "file": "apps\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250___init___py": {"hash": "260117c302aaa884b84d84e3a13e4e6f", "index": {"url": "z_bf7680af20f2e250___init___py.html", "file": "apps\\audit\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_apps_py": {"hash": "5c79ea0a2becb68c8a5a1e92220c8142", "index": {"url": "z_bf7680af20f2e250_apps_py.html", "file": "apps\\audit\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f81546b0b2653676___init___py": {"hash": "b32b047c2e20fa5d156c820c7c57417a", "index": {"url": "z_f81546b0b2653676___init___py.html", "file": "apps\\audit\\management\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08b8a45b9d9a23b6___init___py": {"hash": "3f2d0dfbb5b25c9064d167de0b41083d", "index": {"url": "z_08b8a45b9d9a23b6___init___py.html", "file": "apps\\audit\\management\\commands\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08b8a45b9d9a23b6_cleanup_audit_logs_py": {"hash": "bd5590f6bf9c8b7dc203e377fe1e3944", "index": {"url": "z_08b8a45b9d9a23b6_cleanup_audit_logs_py.html", "file": "apps\\audit\\management\\commands\\cleanup_audit_logs.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1eec6a31ccdfc5bd_0001_initial_py": {"hash": "e7d44823d369210fac41d685d882428f", "index": {"url": "z_1eec6a31ccdfc5bd_0001_initial_py.html", "file": "apps\\audit\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1eec6a31ccdfc5bd_0002_initial_py": {"hash": "1a0e26e2e1c0fa3af638eb028d7c9bcc", "index": {"url": "z_1eec6a31ccdfc5bd_0002_initial_py.html", "file": "apps\\audit\\migrations\\0002_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py": {"hash": "055ad3e7de0b57a53b4bf8e0c40f4eba", "index": {"url": "z_1eec6a31ccdfc5bd_0003_add_error_operation_type_py.html", "file": "apps\\audit\\migrations\\0003_add_error_operation_type.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1eec6a31ccdfc5bd___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_1eec6a31ccdfc5bd___init___py.html", "file": "apps\\audit\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_models_py": {"hash": "eee0869a845a3dfa6a5241c7c45399d2", "index": {"url": "z_bf7680af20f2e250_models_py.html", "file": "apps\\audit\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_serializers_py": {"hash": "4b4bb3994a19f52c866a07cfaee1d7ad", "index": {"url": "z_bf7680af20f2e250_serializers_py.html", "file": "apps\\audit\\serializers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_services_py": {"hash": "a4392372200fbbaea7dbfe96cb1dd909", "index": {"url": "z_bf7680af20f2e250_services_py.html", "file": "apps\\audit\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_tasks_py": {"hash": "b682bc2441f5e6a401cbb0e18b20815d", "index": {"url": "z_bf7680af20f2e250_tasks_py.html", "file": "apps\\audit\\tasks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_test_tasks_py": {"hash": "3e8aeeae7c89fb7f506f766895bbc684", "index": {"url": "z_bf7680af20f2e250_test_tasks_py.html", "file": "apps\\audit\\test_tasks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_tests_py": {"hash": "a077dc165172b6563d7dfb97652b1351", "index": {"url": "z_bf7680af20f2e250_tests_py.html", "file": "apps\\audit\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 0, "n_missing": 211, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_urls_py": {"hash": "973374b053cf822458869acb7205dc8a", "index": {"url": "z_bf7680af20f2e250_urls_py.html", "file": "apps\\audit\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bf7680af20f2e250_views_py": {"hash": "a97758ec98607fd1abe599a949400f74", "index": {"url": "z_bf7680af20f2e250_views_py.html", "file": "apps\\audit\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 209, "n_excluded": 0, "n_missing": 209, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa___init___py": {"hash": "787053fc9f9d2ec5b9e5212cfa256dcf", "index": {"url": "z_4944ad59518994fa___init___py.html", "file": "apps\\authentication\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_anomaly_detector_py": {"hash": "e1ae229d69e0d5b48bc256901f8797df", "index": {"url": "z_4944ad59518994fa_anomaly_detector_py.html", "file": "apps\\authentication\\anomaly_detector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 222, "n_excluded": 0, "n_missing": 222, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_apps_py": {"hash": "85d8f702aaa1435434907127c1ca3bb3", "index": {"url": "z_4944ad59518994fa_apps_py.html", "file": "apps\\authentication\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_91a8e3ad6c12af09___init___py": {"hash": "8a821311fa0b7b57f9a63c936b2e2d7d", "index": {"url": "z_91a8e3ad6c12af09___init___py.html", "file": "apps\\authentication\\management\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0d354c5377fd6458___init___py": {"hash": "71d709d464012bee2f14b5aa2085c1ef", "index": {"url": "z_0d354c5377fd6458___init___py.html", "file": "apps\\authentication\\management\\commands\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0d354c5377fd6458_cleanup_auth_py": {"hash": "84ed3a6efd5953299b1273f21dd743af", "index": {"url": "z_0d354c5377fd6458_cleanup_auth_py.html", "file": "apps\\authentication\\management\\commands\\cleanup_auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b44c3f9d3a575f9_0001_initial_py": {"hash": "5d3cc23f31fece0e0e1f54418a09e5a3", "index": {"url": "z_9b44c3f9d3a575f9_0001_initial_py.html", "file": "apps\\authentication\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b44c3f9d3a575f9_0002_initial_py": {"hash": "1c8ce6ee5da0089e68c1a35b3854a865", "index": {"url": "z_9b44c3f9d3a575f9_0002_initial_py.html", "file": "apps\\authentication\\migrations\\0002_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b44c3f9d3a575f9_0003_simplecaptcha_py": {"hash": "fb9a7ce73822432f83650053ff0b01e2", "index": {"url": "z_9b44c3f9d3a575f9_0003_simplecaptcha_py.html", "file": "apps\\authentication\\migrations\\0003_simplecaptcha.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py": {"hash": "07c82656d24352a53bcc0c5d272651b1", "index": {"url": "z_9b44c3f9d3a575f9_0004_loginattempt_ipwhitelist_py.html", "file": "apps\\authentication\\migrations\\0004_loginattempt_ipwhitelist.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py": {"hash": "5265e3e7ba4f91fd52f397cf671f8106", "index": {"url": "z_9b44c3f9d3a575f9_0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more_py.html", "file": "apps\\authentication\\migrations\\0005_loginattempt_auth_login__usernam_a6fa1c_idx_and_more.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b44c3f9d3a575f9___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_9b44c3f9d3a575f9___init___py.html", "file": "apps\\authentication\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_models_py": {"hash": "6c7c2d3b1729f60a1bf2d2eb462153dd", "index": {"url": "z_4944ad59518994fa_models_py.html", "file": "apps\\authentication\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_security_py": {"hash": "6c59fd1f76246f4ee7aa7e17f019a21f", "index": {"url": "z_4944ad59518994fa_security_py.html", "file": "apps\\authentication\\security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_serializers_py": {"hash": "080582d0165be66c3d51a8199ecf225d", "index": {"url": "z_4944ad59518994fa_serializers_py.html", "file": "apps\\authentication\\serializers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_services_py": {"hash": "3c0c505e1142b33651c2397418249c75", "index": {"url": "z_4944ad59518994fa_services_py.html", "file": "apps\\authentication\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 126, "n_excluded": 0, "n_missing": 126, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_session_views_py": {"hash": "eb7e4172daf19db6cadb3bc40661716a", "index": {"url": "z_4944ad59518994fa_session_views_py.html", "file": "apps\\authentication\\session_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_tasks_py": {"hash": "dae8bb61beecbd70cc69040d85948401", "index": {"url": "z_4944ad59518994fa_tasks_py.html", "file": "apps\\authentication\\tasks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_tests_py": {"hash": "9a6d96bcde9e7892764c750872a4fd6a", "index": {"url": "z_4944ad59518994fa_tests_py.html", "file": "apps\\authentication\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_urls_py": {"hash": "c52b4fba7af177405d82a22081ed1224", "index": {"url": "z_4944ad59518994fa_urls_py.html", "file": "apps\\authentication\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4944ad59518994fa_views_py": {"hash": "7fe8726633d006c42b2d8fae38a4b468", "index": {"url": "z_4944ad59518994fa_views_py.html", "file": "apps\\authentication\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 169, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_747ca389d9c344c0___init___py": {"hash": "9804e3eb5861d1cd3126fa9207af9c84", "index": {"url": "z_747ca389d9c344c0___init___py.html", "file": "apps\\backup\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_747ca389d9c344c0_apps_py": {"hash": "fe6ea5ed0eb3545922751376165a9ee2", "index": {"url": "z_747ca389d9c344c0_apps_py.html", "file": "apps\\backup\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ac143cddc957a9a8_0001_initial_py": {"hash": "3600748a7991fe157d3bbf2c872c689d", "index": {"url": "z_ac143cddc957a9a8_0001_initial_py.html", "file": "apps\\backup\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ac143cddc957a9a8___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_ac143cddc957a9a8___init___py.html", "file": "apps\\backup\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_747ca389d9c344c0_models_py": {"hash": "b33ed26531e70d1178fcea2fae0ea262", "index": {"url": "z_747ca389d9c344c0_models_py.html", "file": "apps\\backup\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_747ca389d9c344c0_services_py": {"hash": "431415d490cfa326d360246c4f4879e0", "index": {"url": "z_747ca389d9c344c0_services_py.html", "file": "apps\\backup\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 266, "n_excluded": 0, "n_missing": 266, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5___init___py": {"hash": "ecc016e85f63b13e55a702c394d0770e", "index": {"url": "z_ecc233269ded7be5___init___py.html", "file": "apps\\common\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_apps_py": {"hash": "dfb03892ccbb20983b7a9a5460ff0dd8", "index": {"url": "z_ecc233269ded7be5_apps_py.html", "file": "apps\\common\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_config_manager_py": {"hash": "fe3c9e469fb40ca05b237fef967e7220", "index": {"url": "z_ecc233269ded7be5_config_manager_py.html", "file": "apps\\common\\config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_config_views_py": {"hash": "59a608e325a29bb697d8d27b059c112d", "index": {"url": "z_ecc233269ded7be5_config_views_py.html", "file": "apps\\common\\config_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 189, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_decorators_py": {"hash": "be10f4829e085af32d404ed9e78b67b7", "index": {"url": "z_ecc233269ded7be5_decorators_py.html", "file": "apps\\common\\decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_department_service_py": {"hash": "7a4f7e672b1c3b00e3f598d9175b7fd1", "index": {"url": "z_ecc233269ded7be5_department_service_py.html", "file": "apps\\common\\department_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_example_views_py": {"hash": "c5a7b81e0e74321a6b00804b2bde0806", "index": {"url": "z_ecc233269ded7be5_example_views_py.html", "file": "apps\\common\\example_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_examples_py": {"hash": "faf3505c1552bd53fb22e324bbd3f16b", "index": {"url": "z_ecc233269ded7be5_examples_py.html", "file": "apps\\common\\examples.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 94, "n_excluded": 0, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_exceptions_py": {"hash": "64484790d1b1ae152241ec943b388018", "index": {"url": "z_ecc233269ded7be5_exceptions_py.html", "file": "apps\\common\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 128, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_file_security_py": {"hash": "071cccdd52f52c726b51c528babdf53a", "index": {"url": "z_ecc233269ded7be5_file_security_py.html", "file": "apps\\common\\file_security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_file_views_py": {"hash": "9d2ed2a98c9a8d4ce9db851d3ff6571f", "index": {"url": "z_ecc233269ded7be5_file_views_py.html", "file": "apps\\common\\file_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 132, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_789b0fc39a74c14f___init___py": {"hash": "5bf8d434546c8ed8916ce893dfeae4da", "index": {"url": "z_789b0fc39a74c14f___init___py.html", "file": "apps\\common\\management\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e2b6b2d63c2fb0d8___init___py": {"hash": "d087b4176ba1ccefcab59ccd8e7013d7", "index": {"url": "z_e2b6b2d63c2fb0d8___init___py.html", "file": "apps\\common\\management\\commands\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e2b6b2d63c2fb0d8_test_data_scope_py": {"hash": "e0fe2c9738d1df1b399f86a7af0f768f", "index": {"url": "z_e2b6b2d63c2fb0d8_test_data_scope_py.html", "file": "apps\\common\\management\\commands\\test_data_scope.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 125, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_middleware_py": {"hash": "7e073a884f989e405ad527ff497996b4", "index": {"url": "z_ecc233269ded7be5_middleware_py.html", "file": "apps\\common\\middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 212, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_models_py": {"hash": "5be372191458d4cfc9dc6b385631cc09", "index": {"url": "z_ecc233269ded7be5_models_py.html", "file": "apps\\common\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_permissions_py": {"hash": "b3639a8d20b6a81999b3b72d39900c0f", "index": {"url": "z_ecc233269ded7be5_permissions_py.html", "file": "apps\\common\\permissions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 343, "n_excluded": 0, "n_missing": 343, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_response_py": {"hash": "9c17997f13bce3b75197b679cb14251d", "index": {"url": "z_ecc233269ded7be5_response_py.html", "file": "apps\\common\\response.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_test_exceptions_py": {"hash": "9c944ab9be52befd7866e246285d10ae", "index": {"url": "z_ecc233269ded7be5_test_exceptions_py.html", "file": "apps\\common\\test_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_test_file_security_py": {"hash": "4655acd7fafe617dd09b04d7c57eda80", "index": {"url": "z_ecc233269ded7be5_test_file_security_py.html", "file": "apps\\common\\test_file_security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 144, "n_excluded": 0, "n_missing": 144, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_test_views_py": {"hash": "046707f9776d3bd021387297a3aa222d", "index": {"url": "z_ecc233269ded7be5_test_views_py.html", "file": "apps\\common\\test_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_tests_py": {"hash": "3565ae5bcb30895316c3dcd34ac4933b", "index": {"url": "z_ecc233269ded7be5_tests_py.html", "file": "apps\\common\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 246, "n_excluded": 0, "n_missing": 246, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e2d79787564ab1b6___init___py": {"hash": "a251c511d55a8c708d78fe8ed22774af", "index": {"url": "z_e2d79787564ab1b6___init___py.html", "file": "apps\\common\\tests\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e2d79787564ab1b6_test_data_scope_py": {"hash": "5abddcb5dc6b320f13f9def151d8a631", "index": {"url": "z_e2d79787564ab1b6_test_data_scope_py.html", "file": "apps\\common\\tests\\test_data_scope.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 205, "n_excluded": 0, "n_missing": 205, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_urls_py": {"hash": "ef3e5812006114b92deae19810011901", "index": {"url": "z_ecc233269ded7be5_urls_py.html", "file": "apps\\common\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ecc233269ded7be5_utils_py": {"hash": "f35fc75b26d13a94533336945103f0a3", "index": {"url": "z_ecc233269ded7be5_utils_py.html", "file": "apps\\common\\utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c79fd428a38de31c___init___py": {"hash": "0b40242852d9d9d02641c474659c24bd", "index": {"url": "z_c79fd428a38de31c___init___py.html", "file": "apps\\departments\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c79fd428a38de31c_apps_py": {"hash": "b1d7fe64ae4f2bfa1c5ccff71b427a72", "index": {"url": "z_c79fd428a38de31c_apps_py.html", "file": "apps\\departments\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1fbaf518d27a7_0001_initial_py": {"hash": "5c5493a56a344b75a98de0a6c22a5099", "index": {"url": "z_93b1fbaf518d27a7_0001_initial_py.html", "file": "apps\\departments\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1fbaf518d27a7_0002_initial_py": {"hash": "e94168daf52c1a6952262ed7faf63a8a", "index": {"url": "z_93b1fbaf518d27a7_0002_initial_py.html", "file": "apps\\departments\\migrations\\0002_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1fbaf518d27a7___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_93b1fbaf518d27a7___init___py.html", "file": "apps\\departments\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c79fd428a38de31c_models_py": {"hash": "e7d3ac20e26679b0cab620c7994a7108", "index": {"url": "z_c79fd428a38de31c_models_py.html", "file": "apps\\departments\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c79fd428a38de31c_serializers_py": {"hash": "22b033fd73a513eeab80b824892a58eb", "index": {"url": "z_c79fd428a38de31c_serializers_py.html", "file": "apps\\departments\\serializers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 148, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c79fd428a38de31c_tests_py": {"hash": "a0a9a894b0b519c72718f3d797a9dba8", "index": {"url": "z_c79fd428a38de31c_tests_py.html", "file": "apps\\departments\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 254, "n_excluded": 0, "n_missing": 254, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c79fd428a38de31c_urls_py": {"hash": "9505f577913555bbafc4d2327d8da569", "index": {"url": "z_c79fd428a38de31c_urls_py.html", "file": "apps\\departments\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c79fd428a38de31c_views_py": {"hash": "e601e2d3eb26852c66419248a47409e3", "index": {"url": "z_c79fd428a38de31c_views_py.html", "file": "apps\\departments\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 235, "n_excluded": 0, "n_missing": 235, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b165ec8e0f80a358___init___py": {"hash": "5a7f667f739098bd188636f3a75414c6", "index": {"url": "z_b165ec8e0f80a358___init___py.html", "file": "apps\\health\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b165ec8e0f80a358_apps_py": {"hash": "459c35a8f27f68774eff1788bbc6c493", "index": {"url": "z_b165ec8e0f80a358_apps_py.html", "file": "apps\\health\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b165ec8e0f80a358_urls_py": {"hash": "e6e93aece3c3dab9e1026ac4affd8d03", "index": {"url": "z_b165ec8e0f80a358_urls_py.html", "file": "apps\\health\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b165ec8e0f80a358_views_py": {"hash": "d836bd632dfd48ef6ca23f751642d843", "index": {"url": "z_b165ec8e0f80a358_views_py.html", "file": "apps\\health\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 148, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56e7f2aa79f1a3bc___init___py": {"hash": "30f91972f624145b0bb32c2988ce6ed8", "index": {"url": "z_56e7f2aa79f1a3bc___init___py.html", "file": "apps\\monitoring\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56e7f2aa79f1a3bc_apps_py": {"hash": "11ad32b13179780016b3754d5d6bf381", "index": {"url": "z_56e7f2aa79f1a3bc_apps_py.html", "file": "apps\\monitoring\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_62e9cac5df6ba8e3_0001_initial_py": {"hash": "2ab37fd7f0b1aa1e9b3bbf55d13da462", "index": {"url": "z_62e9cac5df6ba8e3_0001_initial_py.html", "file": "apps\\monitoring\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_62e9cac5df6ba8e3___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_62e9cac5df6ba8e3___init___py.html", "file": "apps\\monitoring\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56e7f2aa79f1a3bc_models_py": {"hash": "ae5a359153dc82196526a2ff30315fd1", "index": {"url": "z_56e7f2aa79f1a3bc_models_py.html", "file": "apps\\monitoring\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56e7f2aa79f1a3bc_services_py": {"hash": "157d474e17abc9e6ea26386625ed0f71", "index": {"url": "z_56e7f2aa79f1a3bc_services_py.html", "file": "apps\\monitoring\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 255, "n_excluded": 0, "n_missing": 255, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56e7f2aa79f1a3bc_tasks_py": {"hash": "c31ab07d07e87e64b5e0b2a3914d5c36", "index": {"url": "z_56e7f2aa79f1a3bc_tasks_py.html", "file": "apps\\monitoring\\tasks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56e7f2aa79f1a3bc_urls_py": {"hash": "177209a348cbe5a695a93df6b55ed630", "index": {"url": "z_56e7f2aa79f1a3bc_urls_py.html", "file": "apps\\monitoring\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56e7f2aa79f1a3bc_views_py": {"hash": "c5852f5f09ebfd9bfcd86267bb2d0f3c", "index": {"url": "z_56e7f2aa79f1a3bc_views_py.html", "file": "apps\\monitoring\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f___init___py": {"hash": "e4161c808906cc8756f30a2f13aa4b4f", "index": {"url": "z_078d55d899ba2a2f___init___py.html", "file": "apps\\permissions\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_apps_py": {"hash": "ca8b124262497f7f151a13d1ebaf508c", "index": {"url": "z_078d55d899ba2a2f_apps_py.html", "file": "apps\\permissions\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_decorators_py": {"hash": "276b189989ac39d2cd49edd94659ad63", "index": {"url": "z_078d55d899ba2a2f_decorators_py.html", "file": "apps\\permissions\\decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e434bc180f346938___init___py": {"hash": "d858e2080c5352818363166e51093a9f", "index": {"url": "z_e434bc180f346938___init___py.html", "file": "apps\\permissions\\management\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_40492a24c6ed3a69___init___py": {"hash": "13e1ca2d960663f2563471dbed04bb43", "index": {"url": "z_40492a24c6ed3a69___init___py.html", "file": "apps\\permissions\\management\\commands\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_40492a24c6ed3a69_init_permissions_py": {"hash": "b87556320599ad24fbae3c3a082d8db4", "index": {"url": "z_40492a24c6ed3a69_init_permissions_py.html", "file": "apps\\permissions\\management\\commands\\init_permissions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_39828321a89c5c33_0001_initial_py": {"hash": "18e07d016dc1738688d3e5aa1c6a97f2", "index": {"url": "z_39828321a89c5c33_0001_initial_py.html", "file": "apps\\permissions\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_39828321a89c5c33_0002_initial_py": {"hash": "8d6711b530e6695e6d23dbf342dda98a", "index": {"url": "z_39828321a89c5c33_0002_initial_py.html", "file": "apps\\permissions\\migrations\\0002_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_39828321a89c5c33___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_39828321a89c5c33___init___py.html", "file": "apps\\permissions\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_models_py": {"hash": "4bb7937cc108d9d0a1d50ee741baa716", "index": {"url": "z_078d55d899ba2a2f_models_py.html", "file": "apps\\permissions\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_serializers_py": {"hash": "716c34746f3a9b79b0e12fbe4f5a80b1", "index": {"url": "z_078d55d899ba2a2f_serializers_py.html", "file": "apps\\permissions\\serializers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 143, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_services_py": {"hash": "91edca18c511badfaad33321c5d54759", "index": {"url": "z_078d55d899ba2a2f_services_py.html", "file": "apps\\permissions\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_tests_py": {"hash": "7648283b0933296c15fbd5c078719c76", "index": {"url": "z_078d55d899ba2a2f_tests_py.html", "file": "apps\\permissions\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 153, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_urls_py": {"hash": "6189d64da60374d5cc17a0f133c042ce", "index": {"url": "z_078d55d899ba2a2f_urls_py.html", "file": "apps\\permissions\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_078d55d899ba2a2f_views_py": {"hash": "1c132fd29fe3fff3ce8e4be76ae109cd", "index": {"url": "z_078d55d899ba2a2f_views_py.html", "file": "apps\\permissions\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 225, "n_excluded": 0, "n_missing": 225, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7a2fb5ce97bac5d___init___py": {"hash": "6756f3284249af8bde2e8d0ac212fb7a", "index": {"url": "z_e7a2fb5ce97bac5d___init___py.html", "file": "apps\\users\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7a2fb5ce97bac5d_apps_py": {"hash": "001ff91082c6feed2bb1ce591989735e", "index": {"url": "z_e7a2fb5ce97bac5d_apps_py.html", "file": "apps\\users\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a9905534d2b557cb_0001_initial_py": {"hash": "6100ce5cfe219882d970ad89bff4ec43", "index": {"url": "z_a9905534d2b557cb_0001_initial_py.html", "file": "apps\\users\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a9905534d2b557cb_0002_alter_userprofile_managers_py": {"hash": "07fbedfb8b777618f09eace67e7f5b35", "index": {"url": "z_a9905534d2b557cb_0002_alter_userprofile_managers_py.html", "file": "apps\\users\\migrations\\0002_alter_userprofile_managers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a9905534d2b557cb___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_a9905534d2b557cb___init___py.html", "file": "apps\\users\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7a2fb5ce97bac5d_models_py": {"hash": "9f350dd8b7c463ab142dceadf38f87cd", "index": {"url": "z_e7a2fb5ce97bac5d_models_py.html", "file": "apps\\users\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7a2fb5ce97bac5d_serializers_py": {"hash": "7998d2f1bb51125e94c6cbeb99bbe371", "index": {"url": "z_e7a2fb5ce97bac5d_serializers_py.html", "file": "apps\\users\\serializers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7a2fb5ce97bac5d_tests_py": {"hash": "71d0a47eb32c8f66dbc87e97f403d6d1", "index": {"url": "z_e7a2fb5ce97bac5d_tests_py.html", "file": "apps\\users\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 0, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7a2fb5ce97bac5d_urls_py": {"hash": "dd63ce797b4c43217848e098efa869c7", "index": {"url": "z_e7a2fb5ce97bac5d_urls_py.html", "file": "apps\\users\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7a2fb5ce97bac5d_views_py": {"hash": "7a3a747dc47bcec03dab42bad85d5781", "index": {"url": "z_e7a2fb5ce97bac5d_views_py.html", "file": "apps\\users\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 175, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}