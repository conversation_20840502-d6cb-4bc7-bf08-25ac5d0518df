<template>
  <div class="side-menu" :class="{ 'collapsed': menuStore.collapsed }">
    <!-- Logo区域 -->
    <div class="logo-container">
      <div class="logo">
        <img src="@/assets/logo.svg" alt="HEIM" class="logo-img" />
        <span v-show="!menuStore.collapsed" class="logo-text">HEIM</span>
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="menu-container">
      <n-menu
        :value="menuStore.activeMenu"
        :collapsed="menuStore.collapsed"
        :collapsed-width="64"
        :collapsed-icon-size="20"
        :options="menuOptions"
        :expanded-keys="menuStore.openKeys"
        @update:value="handleMenuSelect"
        @update:expanded-keys="handleMenuExpand"
      />
    </div>

    <!-- 折叠按钮 -->
    <div class="collapse-trigger" @click="menuStore.toggleCollapsed">
      <n-icon size="16">
        <svg viewBox="0 0 24 24">
          <path v-if="menuStore.collapsed" 
                d="M9 18l6-6-6-6" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="2" 
                stroke-linecap="round" 
                stroke-linejoin="round" />
          <path v-else 
                d="M15 18l-6-6 6-6" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="2" 
                stroke-linecap="round" 
                stroke-linejoin="round" />
        </svg>
      </n-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMenuStore } from '@/stores/menu'
import type { MenuOption } from 'naive-ui'
import type { MenuItem } from '@/stores/menu'

const router = useRouter()
const route = useRoute()
const menuStore = useMenuStore()

// 将菜单数据转换为Naive UI菜单选项
const menuOptions = computed(() => {
  return convertToMenuOptions(menuStore.visibleMenus)
})

// 转换菜单数据格式
function convertToMenuOptions(menus: MenuItem[]): MenuOption[] {
  return menus.map(menu => {
    const option: MenuOption = {
      label: menu.title,
      key: menu.path,
      icon: menu.icon ? renderIcon(menu.icon) : undefined
    }

    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      option.children = convertToMenuOptions(menu.children)
    }

    return option
  })
}

// 渲染图标
function renderIcon(iconName: string) {
  return () => {
    // 这里可以根据图标名称返回对应的图标组件
    // 简化处理，使用文本代替
    const iconMap: Record<string, string> = {
      'home': '🏠',
      'setting': '⚙️',
      'user': '👤',
      'team': '👥',
      'key': '🔑',
      'apartment': '🏢',
      'info': 'ℹ️'
    }
    
    return iconMap[iconName] || '📄'
  }
}

// 处理菜单选择
function handleMenuSelect(key: string) {
  if (key !== route.path) {
    router.push(key)
  }
}

// 处理菜单展开
function handleMenuExpand(keys: string[]) {
  menuStore.setOpenKeys(keys)
}

// 监听路由变化，更新激活菜单
watch(
  () => route.path,
  (newPath) => {
    menuStore.setActiveMenu(newPath)
  },
  { immediate: true }
)

// 组件挂载时初始化菜单
onMounted(() => {
  // 从路由配置生成菜单
  const routes = router.getRoutes()
  const menus = menuStore.generateMenuFromRoutes(routes)
  menuStore.setMenuList(menus)
  
  // 设置当前激活菜单
  menuStore.setActiveMenu(route.path)
})
</script>

<style scoped>
.side-menu {
  height: 100vh;
  background: #fff;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  width: 240px;
}

.side-menu.collapsed {
  width: 64px;
}

.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-img {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #1f2937;
  transition: opacity 0.3s ease;
}

.menu-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.collapse-trigger {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #e5e7eb;
  cursor: pointer;
  color: #6b7280;
  transition: color 0.3s ease;
}

.collapse-trigger:hover {
  color: #374151;
  background-color: #f9fafb;
}

/* 自定义滚动条 */
.menu-container::-webkit-scrollbar {
  width: 4px;
}

.menu-container::-webkit-scrollbar-track {
  background: transparent;
}

.menu-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.menu-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
