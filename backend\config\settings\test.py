"""
测试环境配置
"""
from .base import *

# 测试数据库配置 - 使用内存数据库提高测试速度
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}

# 禁用缓存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# 禁用Celery任务队列
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# 简化密码验证器
AUTH_PASSWORD_VALIDATORS = []

# 禁用邮件发送
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# 禁用日志输出
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
    'loggers': {
        'django': {
            'handlers': ['null'],
            'propagate': False,
        },
        'apps': {
            'handlers': ['null'],
            'propagate': False,
        },
    }
}

# 禁用静态文件收集
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# 测试媒体文件目录
MEDIA_ROOT = '/tmp/test_media'

# 禁用验证码
CAPTCHA_TEST_MODE = True

# JWT设置 - 使用较短的过期时间便于测试
from datetime import timedelta
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),
    'REFRESH_TOKEN_LIFETIME': timedelta(minutes=10),
    'ROTATE_REFRESH_TOKENS': True,
})

# 禁用CSRF保护
CSRF_COOKIE_SECURE = False
SESSION_COOKIE_SECURE = False

# 测试时禁用一些中间件
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 保留审计中间件用于测试
    'apps.audit.middleware.AuditLogMiddleware',
]

# 测试时的安全设置
SECRET_KEY = 'test-secret-key-for-testing-only-do-not-use-in-production'
DEBUG = True
ALLOWED_HOSTS = ['*']

# 禁用一些安全检查
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False

# 测试时的CORS设置
CORS_ALLOW_ALL_ORIGINS = True

# 禁用文件上传限制
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB

# 测试时的时区设置
USE_TZ = True
TIME_ZONE = 'UTC'

# 禁用国际化以提高测试速度
USE_I18N = False
USE_L10N = False

# 测试覆盖率配置
COVERAGE_REPORT_HTML_OUTPUT_DIR = 'htmlcov'
COVERAGE_MODULE_EXCLUDES = [
    'tests$', 'settings$', 'urls$', 'locale$',
    'migrations', 'fixtures', 'venv', '__pycache__',
    'node_modules', 'bower_components', '.tox',
]
