"""
审计日志模块 - 视图
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count
from django.http import HttpResponse
from datetime import datetime, timedelta
import io
import xlsxwriter
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib import colors

from apps.common.response import ApiResponse
from apps.common.exceptions import ErrorCode
from .models import OperationLog
from .serializers import (
    OperationLogSerializer, OperationLogListSerializer,
    OperationLogDetailSerializer, OperationLogExportSerializer,
    OperationLogStatsSerializer
)


class OperationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """操作日志视图集 - 只读"""
    
    queryset = OperationLog.objects.all().select_related('user').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return OperationLogListSerializer
        elif self.action == 'retrieve':
            return OperationLogDetailSerializer
        elif self.action == 'export':
            return OperationLogExportSerializer
        elif self.action == 'stats':
            return OperationLogStatsSerializer
        return OperationLogSerializer
    
    def get_queryset(self):
        """获取查询集，支持过滤"""
        queryset = self.queryset
        
        # 用户过滤
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # 操作类型过滤
        operation_type = self.request.query_params.get('operation_type')
        if operation_type:
            queryset = queryset.filter(operation_type=operation_type)
        
        # 时间范围过滤
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            try:
                start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                queryset = queryset.filter(created_at__gte=start_datetime)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                queryset = queryset.filter(created_at__lte=end_datetime)
            except ValueError:
                pass
        
        # IP地址过滤
        ip_address = self.request.query_params.get('ip_address')
        if ip_address:
            queryset = queryset.filter(ip_address__icontains=ip_address)
        
        # 状态码过滤
        status_code = self.request.query_params.get('status_code')
        if status_code:
            queryset = queryset.filter(status_code=status_code)
        
        # 关键词搜索
        keyword = self.request.query_params.get('keyword')
        if keyword:
            queryset = queryset.filter(
                Q(operation_desc__icontains=keyword) |
                Q(path__icontains=keyword) |
                Q(user__nickname__icontains=keyword) |
                Q(user__username__icontains=keyword)
            )
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """获取操作日志列表"""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            
            # 分页
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = self.get_serializer(queryset, many=True)
            return ApiResponse.success(serializer.data, "获取操作日志列表成功")
        except Exception as e:
            return ApiResponse.error(f"获取操作日志列表失败: {str(e)}")
    
    def retrieve(self, request, *args, **kwargs):
        """获取操作日志详情"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return ApiResponse.success(serializer.data, "获取操作日志详情成功")
        except Exception as e:
            return ApiResponse.error(f"获取操作日志详情失败: {str(e)}")
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取操作日志统计信息"""
        try:
            now = timezone.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 基础统计
            total_count = OperationLog.objects.count()
            today_count = OperationLog.objects.filter(created_at__gte=today_start).count()
            login_count = OperationLog.objects.filter(
                operation_type='LOGIN',
                created_at__gte=today_start
            ).count()
            
            # 操作类型统计
            operation_type_stats = dict(
                OperationLog.objects.values('operation_type').annotate(
                    count=Count('id')
                ).values_list('operation_type', 'count')
            )
            
            # 用户操作统计（前10名）
            user_stats = list(
                OperationLog.objects.filter(
                    user__isnull=False,
                    created_at__gte=today_start
                ).values('user__nickname', 'user__username').annotate(
                    count=Count('id')
                ).order_by('-count')[:10]
            )
            
            # 24小时统计
            hourly_stats = []
            for i in range(24):
                hour_start = today_start + timedelta(hours=i)
                hour_end = hour_start + timedelta(hours=1)
                count = OperationLog.objects.filter(
                    created_at__gte=hour_start,
                    created_at__lt=hour_end
                ).count()
                hourly_stats.append({
                    'hour': i,
                    'count': count
                })
            
            stats_data = {
                'total_count': total_count,
                'today_count': today_count,
                'login_count': login_count,
                'operation_type_stats': operation_type_stats,
                'user_stats': user_stats,
                'hourly_stats': hourly_stats
            }
            
            serializer = OperationLogStatsSerializer(stats_data)
            return ApiResponse.success(serializer.data, "获取统计信息成功")
        except Exception as e:
            return ApiResponse.error(f"获取统计信息失败: {str(e)}")
    
    @action(detail=False, methods=['post'])
    def export(self, request):
        """导出操作日志"""
        try:
            serializer = OperationLogExportSerializer(data=request.data)
            if not serializer.is_valid():
                return ApiResponse.error("参数验证失败", ErrorCode.VALIDATION_ERROR)
            
            data = serializer.validated_data
            export_format = data.get('export_format', 'excel')
            
            # 构建查询集
            queryset = self.get_queryset()
            
            # 应用过滤条件
            if data.get('start_date'):
                queryset = queryset.filter(created_at__gte=data['start_date'])
            if data.get('end_date'):
                queryset = queryset.filter(created_at__lte=data['end_date'])
            if data.get('user_id'):
                queryset = queryset.filter(user_id=data['user_id'])
            if data.get('operation_type'):
                queryset = queryset.filter(operation_type=data['operation_type'])
            
            # 限制导出数量
            queryset = queryset[:10000]  # 最多导出10000条
            
            if export_format == 'excel':
                return self._export_excel(queryset)
            elif export_format == 'pdf':
                return self._export_pdf(queryset)
            else:
                return ApiResponse.error("不支持的导出格式")
        except Exception as e:
            return ApiResponse.error(f"导出失败: {str(e)}")
    
    def _export_excel(self, queryset):
        """导出Excel格式"""
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('操作日志')
        
        # 设置表头
        headers = [
            '序号', '操作用户', '操作类型', '操作描述', '请求方法',
            '请求路径', 'IP地址', '状态码', '响应时间(ms)', '操作时间'
        ]
        
        # 写入表头
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D7E4BC',
            'border': 1
        })
        
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # 写入数据
        row_format = workbook.add_format({'border': 1})
        for row, log in enumerate(queryset, 1):
            worksheet.write(row, 0, row, row_format)
            worksheet.write(row, 1, log.user.nickname if log.user else '未知用户', row_format)
            worksheet.write(row, 2, log.get_operation_type_display(), row_format)
            worksheet.write(row, 3, log.operation_desc, row_format)
            worksheet.write(row, 4, log.method, row_format)
            worksheet.write(row, 5, log.path, row_format)
            worksheet.write(row, 6, log.ip_address, row_format)
            worksheet.write(row, 7, log.status_code, row_format)
            worksheet.write(row, 8, log.response_time, row_format)
            worksheet.write(row, 9, log.created_at.strftime('%Y-%m-%d %H:%M:%S'), row_format)
        
        workbook.close()
        output.seek(0)
        
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="operation_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
        return response

    def _export_pdf(self, queryset):
        """导出PDF格式"""
        output = io.BytesIO()

        # 创建PDF文档
        doc = SimpleDocTemplate(output, pagesize=A4)
        elements = []

        # 设置样式
        styles = getSampleStyleSheet()
        title_style = styles['Title']
        title_style.fontName = 'Helvetica-Bold'

        # 添加标题
        title = Paragraph("操作日志报告", title_style)
        elements.append(title)
        elements.append(Paragraph("<br/><br/>", styles['Normal']))

        # 准备表格数据
        data = [['序号', '操作用户', '操作类型', '操作描述', '请求方法', 'IP地址', '状态码', '操作时间']]

        for idx, log in enumerate(queryset[:100], 1):  # PDF限制100条记录
            data.append([
                str(idx),
                log.user.nickname if log.user else '未知用户',
                log.get_operation_type_display(),
                log.operation_desc[:30] + '...' if len(log.operation_desc) > 30 else log.operation_desc,
                log.method,
                log.ip_address,
                str(log.status_code),
                log.created_at.strftime('%Y-%m-%d %H:%M')
            ])

        # 创建表格
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(table)

        # 生成PDF
        doc.build(elements)
        output.seek(0)

        response = HttpResponse(output.read(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="operation_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
        return response

    @action(detail=False, methods=['post'])
    def cleanup(self, request):
        """清理过期日志"""
        try:
            # 只有超级管理员可以执行清理操作
            if not request.user.is_superuser:
                return ApiResponse.error("权限不足", ErrorCode.PERMISSION_DENIED)

            days = request.data.get('days', 90)
            if not isinstance(days, int) or days < 1:
                return ApiResponse.error("天数参数无效")

            # 执行清理
            deleted_count, _ = OperationLog.cleanup_old_logs(days)

            return ApiResponse.success({
                'deleted_count': deleted_count
            }, f"成功清理{deleted_count}条过期日志")
        except Exception as e:
            return ApiResponse.error(f"清理日志失败: {str(e)}")

    @action(detail=False, methods=['get'])
    def health(self, request):
        """获取系统健康状态"""
        try:
            from .services import AuditLogService

            health_stats = AuditLogService.get_system_health_stats()

            return ApiResponse.success(health_stats, "获取系统健康状态成功")
        except Exception as e:
            return ApiResponse.error(f"获取系统健康状态失败: {str(e)}")

    @action(detail=False, methods=['get'])
    def user_stats(self, request):
        """获取当前用户操作统计"""
        try:
            from .services import AuditLogService

            days = request.query_params.get('days', 30)
            try:
                days = int(days)
            except ValueError:
                days = 30

            user_stats = AuditLogService.get_user_operation_stats(request.user, days)

            return ApiResponse.success(user_stats, "获取用户操作统计成功")
        except Exception as e:
            return ApiResponse.error(f"获取用户操作统计失败: {str(e)}")
