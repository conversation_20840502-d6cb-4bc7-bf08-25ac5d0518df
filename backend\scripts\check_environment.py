#!/usr/bin/env python
"""
HEIM项目环境检查脚本
验证开发环境是否符合项目技术规范
"""
import sys
import subprocess
import os
from pathlib import Path

def print_header(title):
    """打印标题"""
    print(f"\n{'='*50}")
    print(f"  {title}")
    print(f"{'='*50}")

def print_check(description, status, details=""):
    """打印检查结果"""
    status_symbol = "✅" if status else "❌"
    print(f"{status_symbol} {description}")
    if details:
        print(f"   {details}")

def check_python_version():
    """检查Python版本"""
    print_header("Python版本检查")
    
    version = sys.version_info
    required_major, required_minor = 3, 12
    
    is_valid = version.major >= required_major and version.minor >= required_minor
    
    print_check(
        f"Python版本 >= {required_major}.{required_minor}",
        is_valid,
        f"当前版本: {version.major}.{version.minor}.{version.micro}"
    )
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    print_check(
        "运行在虚拟环境中",
        in_venv,
        f"Python路径: {sys.executable}"
    )
    
    return is_valid and in_venv

def check_uv_installation():
    """检查uv包管理工具"""
    print_header("uv包管理工具检查")
    
    try:
        result = subprocess.run(['uv', '--version'], capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        print_check("uv已安装", True, f"版本: {version}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_check("uv已安装", False, "请安装uv: pip install uv")
        return False

def check_project_structure():
    """检查项目结构"""
    print_header("项目结构检查")
    
    required_files = [
        'pyproject.toml',
        'manage.py',
        'config/__init__.py',
        'apps/__init__.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        exists = Path(file_path).exists()
        print_check(f"文件存在: {file_path}", exists)
        if not exists:
            all_exist = False
    
    return all_exist

def check_pyproject_toml():
    """检查pyproject.toml配置"""
    print_header("pyproject.toml配置检查")
    
    pyproject_path = Path('pyproject.toml')
    if not pyproject_path.exists():
        print_check("pyproject.toml存在", False)
        return False
    
    try:
        import tomllib
    except ImportError:
        try:
            import tomli as tomllib
        except ImportError:
            print_check("TOML解析器可用", False, "请安装tomli: uv add tomli")
            return False
    
    try:
        with open(pyproject_path, 'rb') as f:
            config = tomllib.load(f)
        
        # 检查必要的配置项
        has_project = 'project' in config
        print_check("包含[project]配置", has_project)
        
        if has_project:
            project = config['project']
            has_name = 'name' in project
            has_deps = 'dependencies' in project
            
            print_check("包含项目名称", has_name)
            print_check("包含依赖列表", has_deps)
            
            if has_deps:
                deps = project['dependencies']
                django_found = any('django' in dep.lower() for dep in deps)
                drf_found = any('djangorestframework' in dep.lower() for dep in deps)
                
                print_check("包含Django依赖", django_found)
                print_check("包含DRF依赖", drf_found)
                
                return has_name and has_deps and django_found and drf_found
        
        return has_project
        
    except Exception as e:
        print_check("pyproject.toml格式正确", False, f"解析错误: {e}")
        return False

def check_dependencies():
    """检查依赖安装"""
    print_header("依赖安装检查")
    
    try:
        result = subprocess.run(['uv', 'pip', 'list'], capture_output=True, text=True, check=True)
        installed_packages = result.stdout.lower()
        
        required_packages = [
            'django',
            'djangorestframework',
            'celery',
            'redis',
            'python-decouple'
        ]
        
        all_installed = True
        for package in required_packages:
            is_installed = package in installed_packages
            print_check(f"{package}已安装", is_installed)
            if not is_installed:
                all_installed = False
        
        return all_installed
        
    except subprocess.CalledProcessError:
        print_check("依赖检查", False, "无法获取包列表")
        return False

def check_django_setup():
    """检查Django配置"""
    print_header("Django配置检查")
    
    try:
        # 检查Django是否可以导入
        import django
        print_check("Django可导入", True, f"版本: {django.get_version()}")
        
        # 检查Django设置
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        django.setup()
        
        from django.conf import settings
        print_check("Django设置加载", True)
        
        # 检查数据库配置
        db_config = settings.DATABASES.get('default', {})
        has_db = bool(db_config)
        print_check("数据库配置存在", has_db)
        
        return True
        
    except Exception as e:
        print_check("Django配置", False, f"错误: {e}")
        return False

def check_celery_setup():
    """检查Celery配置"""
    print_header("Celery配置检查")
    
    try:
        from config.celery import app
        print_check("Celery应用可导入", True, f"应用名: {app.main}")
        return True
    except Exception as e:
        print_check("Celery配置", False, f"错误: {e}")
        return False

def check_forbidden_practices():
    """检查是否存在违规操作"""
    print_header("规范遵循检查")
    
    # 检查是否存在requirements.txt
    has_requirements = Path('requirements.txt').exists()
    print_check("未使用requirements.txt", not has_requirements, 
                "应使用pyproject.toml管理依赖" if has_requirements else "")
    
    # 检查是否在全局环境
    in_global = not (hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))
    print_check("未在全局环境开发", not in_global,
                "请使用虚拟环境" if in_global else "")
    
    return not has_requirements and not in_global

def main():
    """主检查函数"""
    print("🔍 HEIM项目环境检查")
    print("检查开发环境是否符合项目技术规范")
    
    checks = [
        ("Python版本", check_python_version),
        ("uv包管理工具", check_uv_installation),
        ("项目结构", check_project_structure),
        ("pyproject.toml配置", check_pyproject_toml),
        ("依赖安装", check_dependencies),
        ("Django配置", check_django_setup),
        ("Celery配置", check_celery_setup),
        ("规范遵循", check_forbidden_practices),
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print_check(f"{name}检查", False, f"检查失败: {e}")
            results.append((name, False))
    
    # 总结
    print_header("检查总结")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"通过检查: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有检查通过！环境配置符合项目规范")
        return 0
    else:
        print("⚠️ 存在问题，请根据上述检查结果进行修复")
        print("\n修复建议:")
        print("1. 确保使用uv作为包管理工具")
        print("2. 确保在虚拟环境中开发")
        print("3. 确保pyproject.toml配置正确")
        print("4. 运行 'uv sync' 同步依赖")
        return 1

if __name__ == '__main__':
    sys.exit(main())
